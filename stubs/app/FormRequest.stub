<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class {{ requestName }} extends FormRequest
{
    use \App\Traits\Blamable;

    /**
    * User Object 
    */
    protected App\Models\User $user;

    /**
    * construct function 
    */
    public function __construct() {
        $this->user = self::getBlamable();
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // create
        if ($this->isMethod('post')) {
            return $this->user()->can('create_', 'api');
        }

        // update
        if ($this->isMethod('put') || $this->isMethod('patch')) {
            return $this->user()->can('update_', 'api');
        }

        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            // Add shared validation rules here
        ];

        // Create 
        if ($this->isMethod('post')) {
            //$rules['name'] = ['required', 'string'];
        }

        // update
        if ($this->isMethod('put') || $this->isMethod('patch')) {
            //$rules['name'] = ['nullable', 'string'];
        }

        return $rules;
    }

    /**
     * Customize the error messages.
     *
     * @return array
     */
    public function messages()
    {
        return [
            //'field.required' => 'Field is required.',
        ];
    }

    /**
     * Handle failed validation.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        $response = \App\Helpers\ApiHelper::validationError(
            app(\App\Services\ApiResponseService::class),
            $validator->errors()->toArray()
        );

        throw new \Illuminate\Http\Exceptions\HttpResponseException($response);
    }
}