apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{client_name}}  ## need to change and meet the client name
  namespace: argocd
spec:
  destination:
    namespace: {{client_name}}  ## need to change and meet the client name
    server: https://kubernetes.default.svc
  source:
    path: isolution-sonarqube-helm-chart
    repoURL: **************:isolutions-sa/demo-sonar-k8s.git
    targetRevision: sonarqubechart
    helm:
      values: |
        ## NOTE: Configure the edition of SonarQube Server to deploy: developer or enterprise
        edition: "enterprise"

        image:
          repository: sonarqube
          # tag: latest         ## discuss with Ihab
          pullPolicy: IfNotPresent

        ### need to discuss with Ihab ### RollingUpdate / Recreate
        deploymentStrategy:
          type: Recreate

        ## NOTE: keep it enabled true to add the database secrets to allow connection to postgres database
        externalsecret:
          enabled: true

        ## NOTE: keep it enabled true to allow connect to GCP cloudsql database
        jdbcOverwrite:
          enabled: true
          jdbcUrl: ********************************/{{db_name}}    ## need modify to meet the correct db name
          jdbcUsername: "{{db_username}}"                                      ## need modify to meet the correct db username     
          jdbcSecretName: "SONAR_JDBC_SECRETPASSWORD_KEY"            ## The value refers to the ExternalSecret resource
          jdbcSecretPasswordKey: "SONAR_JDBC_SECRETPASSWORD_KEY"     ## The value refers to the ExternalSecret resource

        ## NOTE: keep it enabled true to create BackendConfig resource for service
        backendconfig:
          enabled: false

        service:
          type: ClusterIP
          protocol: TCP
          port: 80
          targetport: 9000
          labels: {}
          annotations: 
            cloud.google.com/neg: true
            cloud.google.com/backend-config: true
            # cloud.google.com/example: '{"ingress": true}' ## edit as needed

        ## NOTE: keep it enabled true to create ManagedCertificate resource and issue certificate
        managedcertificate:
          enabled: false
          domains:
            - {{domain}} ## need modify to meet the correct domain name

        ## NOTE: keep it enabled true to create FrontendConfig resource to redirectToHttps
        frontendconfig:
          enabled: false

        ## NOTE: keep it enabled true to create ingress resource to allow incoming traffic
        ingress:
          enabled: false
          annotations: 
            networking.gke.io/v1beta1.FrontendConfig: true
            networking.gke.io/managed-certificates: true
            kubernetes.io/ingress.global-static-ip-name: "{{static_ip_address}}"  ## need modify to meet the correct public static ip address
            kubernetes.io/ingress.class: "gce"
          hosts:
            - name: {{domain}}  ## need modify to meet the correct domain name

        ## NOTE: keep it enabled true to add the service account key.json file and mount it to the sidecar pod
        cloudsqlproxy:
          enabled: true

        ## NOTE: Used to deploy a sidecar pod to allow connection to postgres database
        extraContainers:
          - name: cloud-sql-proxy
            image: gcr.io/cloudsql-docker/gce-proxy:1.33.14
            command:
              - "/cloud_sql_proxy"
              - "-instances=prj-devlab-0225:europe-west1:sonar-demo-db=tcp:5432"
              - "-credential_file=/secrets/cloudsql/credentials.json"
            volumeMounts:
              - name: cloudsql-instance-credentials
                mountPath: /secrets/cloudsql
                readOnly: true

        readinessProbe:
          exec:
            command:
            - sh
            - -c
            - |
              #!/bin/bash
              # A Sonarqube container is considered ready if the status is UP, DB_MIGRATION_NEEDED or DB_MIGRATION_RUNNING
              # status about migration are added to prevent the node to be kill while SonarQube is upgrading the database.
              if wget --no-proxy -qO- http://localhost:9000/api/system/status | grep -q -e '"status":"UP"' -e '"status":"DB_MIGRATION_NEEDED"' -e '"status":"DB_MIGRATION_RUNNING"'; then
                exit 0
              fi
              exit 1
          initialDelaySeconds: 300 # Increase initial delay to accommodate the database start time
          timeoutSeconds: 60
          periodSeconds: 30
          successThreshold: 1
          failureThreshold: 6

        livenessProbe:
          exec:
            command:
            - sh
            - -c
            - |
              wget --no-proxy --quiet -O /dev/null --timeout={{ .Values.livenessProbe.timeoutSeconds }} --header="X-Sonar-Passcode: $SONAR_WEB_SYSTEMPASSCODE" "http://localhost:9000/api/system/liveness"
          initialDelaySeconds: 300  # Ensure the application has enough time to start
          timeoutSeconds: 60
          periodSeconds: 30
          successThreshold: 1
          failureThreshold: 6


        resources:
          limits:
            cpu: 1000m
            memory: 5120Mi
          requests:
            cpu: 100m
            memory: 225Mi

        ## The value refers to the ExternalSecret resource, 
        #  in case need to change it you should change also the key value in "externalsecret.yaml" file as well
        monitoringPasscode: "SONAR_WEB_SYSTEMPASSCODE_KEY"

        secretManagerCreds:
          enabled: true

        ## NOTE: To specify the deployment type to be "Deployment" instead of "StatefulSet"
        deploymentType: "Deployment"

        ## NOTE: Edit in case we need more replica number
        replicaCount: 1

        ## NOTE: To keep only 3 versions of old replicaSets
        revisionHistoryLimit: 3

        # NOTE: keep it enabled: false -- We are using an external DB (Cloud SQL)
        postgresql:
          enabled: false

        ## NOTE: keep it enabled: false to avoid deploy community edition version
        community:
          enabled: false

        startupProbe:
          enabled: false
          initialDelaySeconds: 300
          timeoutSeconds: 300
          periodSeconds: 10
          successThreshold: 1
          failureThreshold: 24

        ## NOTE: we create the volume in _pod.tpl file
        # extraVolumes:
        #   - name: cloudsql-instance-credentials
        #     secret:
        #       secretName: sonar-client-demo-sonarqube-cloudsql-proxy-creds

  sources: []
  project: default
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
    automated:
      selfHeal: true
      prune: true