<?php

namespace {{ namespace }};

use {{ resource }};
use Filament\Actions;
use {{ baseResourcePage }};

class {{ resourcePageClass }} extends {{ baseResourcePageClass }}
{
    protected static string $resource = {{ resourceClass }}::class;

    protected function getHeaderActions(): array
    {
        return [
{{ actions }}
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        if (empty($data['password'])) {
            unset($data['password']); 
        }

        return $data;
    }
}
