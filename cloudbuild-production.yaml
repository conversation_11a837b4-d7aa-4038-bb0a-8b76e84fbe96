steps:
  # Step 1: Authenticate with Artifact Registry
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo $PROJECT_ID
        gcloud auth configure-docker ${_REGION}-docker.pkg.dev

  # sonar scanner cli
  - name: 'sonarsource/sonar-scanner-cli'
    entrypoint: 'bash'
    secretEnv: ['SONAR_TOKEN_TEST_PRJ_ADMINS']
    args:
      - '-c'
      - |
        mkdir -P /tmp/.sonar
        mkdir -P /tmp/.scannerwork
        export SONAR_USER_HOME=/tmp/.sonar
        sonar-scanner \
         -Dsonar.projectKey=ismena-sonarqube-admins \
         -Dsonar.projectName=ismena-sonarqube-admins \
         -Dsonar.sources=. \
         -Dsonar.host.url=http://************:9050 \
         -Dsonar.login=$$SONAR_TOKEN_TEST_PRJ_ADMINS \
         -Dsonar.working.directory=/tmp/.scannerwork

  # Step 2: Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    entrypoint: 'bash'
    secretEnv: 
    - AUTH_COMPOSER
    args:
    - '-c'
    - |
      docker build \
        -f docker/dockerfile-php \
        -t "${_REGION}-docker.pkg.dev/${_PROJECT_ID}/${_REPO_NAME}/${_IMAGE_NAME}:latest" \
        -t "${_REGION}-docker.pkg.dev/${_PROJECT_ID}/${_REPO_NAME}/${_IMAGE_NAME}:$COMMIT_SHA" \
        --build-arg AUTH_COMPOSER="$$AUTH_COMPOSER" \
        . 

      
  # Step 3: Push the Docker image with "latest" tag
  - name: 'gcr.io/cloud-builders/docker'
    args:
      [
        'push',
        '${_REGION}-docker.pkg.dev/${_PROJECT_ID}/${_REPO_NAME}/${_IMAGE_NAME}:latest',
      ]

  # Step 4: Push the Docker image with the commit hash tag
  - name: 'gcr.io/cloud-builders/docker'
    args:
      [
        'push',
        '${_REGION}-docker.pkg.dev/${_PROJECT_ID}/${_REPO_NAME}/${_IMAGE_NAME}:$COMMIT_SHA',
      ]

  # Step 5: Substitute image ID and commit the change
  - name: 'gcr.io/cloud-builders/git'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "Cloning repository..."
        echo "Repo URL: https://github.com/${_GITHUB_ORG}/${_GITHUB_REPO}.git"
        git clone https://$$<EMAIL>/${_GITHUB_ORG}/${_GITHUB_REPO}.git

        echo "Changing directory into manifests directory"
        cd ${_GITHUB_REPO}/${_MANIFESTS_DIR}
        echo "Current directory: $(pwd)"

        echo "Before updating:"
        grep 'image:' ${_DEPLOYMENT_FILE}.yaml
        grep 'image:' ${_CRONJOB_FILE}.yaml
        grep 'image:' ${_CRONJOB_FILE_UPDATE_USAGE}.yaml

        echo "Updating image ID in ${_DEPLOYMENT_FILE}.yaml..."
        sed -i "s|\(image: \).*${_IMAGE_NAME}:.*|\1${_REGION}-docker.pkg.dev/${_PROJECT_ID}/${_REPO_NAME}/${_IMAGE_NAME}:$COMMIT_SHA|" ${_DEPLOYMENT_FILE}.yaml

        echo "Updating image ID in ${_CRONJOB_FILE}.yaml..."
        sed -i "s|\(image: \).*${_IMAGE_NAME}:.*|\1${_REGION}-docker.pkg.dev/${_PROJECT_ID}/${_REPO_NAME}/${_IMAGE_NAME}:$COMMIT_SHA|" ${_CRONJOB_FILE}.yaml

                echo "Updating image ID in ${_CRONJOB_FILE_UPDATE_USAGE}.yaml..."
        sed -i "s|\(image: \).*${_IMAGE_NAME}:.*|\1${_REGION}-docker.pkg.dev/${_PROJECT_ID}/${_REPO_NAME}/${_IMAGE_NAME}:$COMMIT_SHA|" ${_CRONJOB_FILE_UPDATE_USAGE}.yaml

        echo "After updating:"
        grep 'image:' ${_DEPLOYMENT_FILE}.yaml
        grep 'image:' ${_CRONJOB_FILE}.yaml
        grep 'image:' ${_CRONJOB_FILE_UPDATE_USAGE}.yaml

        echo "Configuring Git user..."
        git config --global user.email "${_GITHUB_EMAIL}"
        git config --global user.name "${_GITHUB_USER}"

        echo "Checking for changes..."
        git status
        git diff

        echo "Committing changes..."
        git add ${_DEPLOYMENT_FILE}.yaml
        git add ${_CRONJOB_FILE}.yaml
        git add ${_CRONJOB_FILE_UPDATE_USAGE}.yaml
        git commit -m "Update image ID to $COMMIT_SHA" || echo "Nothing to commit"

        echo "Pushing changes..."
        git push origin main

        echo "Changes pushed; refresh ArgoCD or wait 3 minutes"
    secretEnv: ['GITHUB_TOKEN']

availableSecrets:
  secretManager:
    - versionName: projects/378947332109/secrets/GITHUB_TOKEN/versions/latest
      env: 'GITHUB_TOKEN'
    - versionName: projects/378947332109/secrets/AUTH_COMPOSER/versions/latest
      env: 'AUTH_COMPOSER'
    - versionName: projects/378947332109/secrets/SONAR_TOKEN_TEST_PRJ_ADMINS/versions/latest
      env: 'SONAR_TOKEN_TEST_PRJ_ADMINS'

substitutions:
  COMMIT_SHA: $BUILD_ID
  _GITHUB_ORG: isolutions-sa
  _GITHUB_REPO: sonar-k8s-manifest
  _GITHUB_USER: devops.ismena
  _GITHUB_EMAIL: <EMAIL>
  _MANIFESTS_DIR: k8s-manifest/sonar-admin
  _DEPLOYMENT_FILE: sonar-admin
  _CRONJOB_FILE: sonar-admin-cron-job
  _CRONJOB_FILE_UPDATE_USAGE: sonar-admin-cron-job-update-usage
  _REGION: europe-west1
  _PROJECT_ID: prj-sonarbyisolutions-0425
  _REPO_NAME: sonar-repo
  _IMAGE_NAME: sonar-admin

options:
  logging: CLOUD_LOGGING_ONLY
  env:
    - PROJECT_ID=prj-sonarbyisolutions-0425