FROM php:8.2-fpm

# FROM europe-west1-docker.pkg.dev/prj-sonarbyisolutions-0425/sonar-repo/sonar-admin:base-image

# Install required extensions
RUN apt-get update && apt-get install -y wget curl \
    libzip-dev \
    zip \
    sendmail \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libwebp-dev \
    libpng-dev \
    curl \
    && docker-php-ext-configure gd --with-freetype --with-jpeg --with-webp \
    && docker-php-ext-install -j$(nproc) gd \
    && docker-php-ext-install exif \
    && docker-php-ext-install zip \
    && docker-php-ext-install pdo_mysql bcmath sockets mysqli exif

RUN apt-get -y update \
    && apt-get install -y libicu-dev \
    && docker-php-ext-configure intl \
    && docker-php-ext-install intl

RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer && chmod +x /usr/local/bin/composer

RUN curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
RUN chmod +x kubectl \
    && mv ./kubectl /usr/local/bin/kubectl

WORKDIR /var/www/html/

COPY . .

ARG AUTH_COMPOSER

RUN touch ~/.composer/auth.json && echo $AUTH_COMPOSER > ~/.composer/auth.json
RUN composer install --no-scripts
RUN touch storage/logs/laravel.log
RUN chown -R www-data:www-data storage bootstrap/cache

COPY ./docker/entrypoint_custom.sh /etc/entrypoint_custom.sh
RUN chmod +x /etc/entrypoint_custom.sh

ENTRYPOINT ["/etc/entrypoint_custom.sh"]