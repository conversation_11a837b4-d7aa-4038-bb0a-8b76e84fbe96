<?php

/**
 * Test script to verify the server_id unique validation logic
 * This simulates the validation logic used in the SubscriptionResource
 */

// Mock the SubscriptionLicense model query behavior
class MockSubscriptionLicense
{
    public static function where($field, $value)
    {
        return new MockQueryBuilder($field, $value);
    }
}

class MockQueryBuilder
{
    private $conditions = [];
    
    public function __construct($field, $value)
    {
        $this->conditions[] = [$field, '=', $value];
    }
    
    public function where($field, $operator, $value)
    {
        $this->conditions[] = [$field, $operator, $value];
        return $this;
    }
    
    public function exists()
    {
        // Simulate database records
        $existingRecords = [
            ['subscription_license_id' => 1, 'server_id' => 'ABC12345-1234567890abcdef1234'],
            ['subscription_license_id' => 2, 'server_id' => 'XYZ67890-abcdef1234567890abcd'],
        ];
        
        foreach ($existingRecords as $record) {
            $matches = true;
            foreach ($this->conditions as $condition) {
                [$field, $operator, $value] = $condition;
                
                if ($operator === '=' && $record[$field] !== $value) {
                    $matches = false;
                    break;
                }
                if ($operator === '!=' && $record[$field] === $value) {
                    $matches = false;
                    break;
                }
            }
            if ($matches) {
                return true;
            }
        }
        return false;
    }
}

// Test the validation logic
function testServerIdValidation($serverId, $currentLicenseId = null)
{
    echo "Testing server_id: $serverId" . ($currentLicenseId ? " (editing license ID: $currentLicenseId)" : " (creating new)") . "\n";
    
    // Build the unique validation query (same logic as in the form)
    $query = MockSubscriptionLicense::where('server_id', $serverId);
    
    // If we're editing an existing license, exclude it from the check
    if ($currentLicenseId) {
        $query->where('subscription_license_id', '!=', $currentLicenseId);
    }
    
    // Check if any other record has this server_id
    $exists = $query->exists();
    
    if ($exists) {
        echo "❌ VALIDATION FAILED: The server id has already been taken.\n";
        return false;
    } else {
        echo "✅ VALIDATION PASSED: Server ID is unique.\n";
        return true;
    }
}

echo "=== Server ID Unique Validation Test ===\n\n";

// Test Case 1: Creating new license with existing server_id
echo "Test Case 1: Creating new license with existing server_id\n";
testServerIdValidation('ABC12345-1234567890abcdef1234');
echo "\n";

// Test Case 2: Creating new license with unique server_id
echo "Test Case 2: Creating new license with unique server_id\n";
testServerIdValidation('NEW12345-1234567890abcdef1234');
echo "\n";

// Test Case 3: Editing existing license with same server_id (should pass)
echo "Test Case 3: Editing existing license with same server_id\n";
testServerIdValidation('ABC12345-1234567890abcdef1234', 1);
echo "\n";

// Test Case 4: Editing existing license with different existing server_id (should fail)
echo "Test Case 4: Editing existing license with different existing server_id\n";
testServerIdValidation('XYZ67890-abcdef1234567890abcd', 1);
echo "\n";

// Test Case 5: Editing existing license with unique server_id (should pass)
echo "Test Case 5: Editing existing license with unique server_id\n";
testServerIdValidation('EDIT1234-1234567890abcdef1234', 1);
echo "\n";

echo "=== Test Complete ===\n";
