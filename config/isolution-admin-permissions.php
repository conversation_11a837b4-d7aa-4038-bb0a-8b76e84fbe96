<?php

return [
    // Customer management
    'company' => ['view', 'create', 'update', 'delete', 'restore'],
    'country' => ['view', 'create', 'update', 'delete', 'restore'],
    'platform::country' => ['view', 'create', 'update', 'delete', 'restore'],
    'currency' => ['view', 'create', 'update', 'delete', 'restore'],
    'industry' => ['view', 'create', 'update', 'delete', 'restore'],
    'sector' => ['view', 'create', 'update', 'delete', 'restore'],
    'suspension::reason' => ['view', 'create', 'update', 'delete', 'restore'],

    // User management
    'user' => ['view', 'create', 'update', 'delete', 'restore'],
    'admin::user' => ['view', 'create', 'update', 'delete', 'restore'],
    'customer::user' => ['view', 'create', 'update', 'delete', 'restore'],

    // CMS
    'cms::page' => ['view', 'create', 'update', 'delete', 'restore'],

    // Subscription management
    'subscription' => ['view', 'create', 'update', 'delete', 'restore'],
    'subscription::receipt' => ['view'],
    'subscription::history' => ['view'],
    'subscription::progress' => ['view'],

    // License management
    'subscription::license' => ['view', 'create', 'update', 'delete', 'restore'],

    // Billing management
    'billing' => ['view', 'create', 'update', 'delete', 'approve_payment'],
    'platform::banks' => ['view', 'create', 'update', 'delete', 'approve_payment'],
    
    // System management
    'status' => ['view', 'create', 'update', 'delete', 'restore'],

    // Audit
    'audit' => ['audit_records', 'rollback_audit'],

    // Logs and plans
    'log' => ['view', 'create', 'update', 'delete', 'restore'],
    'plan' => ['view', 'create', 'update', 'delete', 'restore'],

    // Shield generated permissions (excluding role-related)
//    'shield::user' => ['view', 'view_any', 'create', 'update', 'delete', 'delete_any'],
//    'shield::permission' => ['view', 'view_any', 'create', 'update', 'delete', 'delete_any'],

    // Pages
    'page' => ['view'],

    // Widgets
    'widget' => ['view'],

    // Tags
    'cms::tag' => ['view', 'create', 'update', 'delete', 'restore'],

    // Navigation
    'cms::page::navigation::category' => ['view', 'create', 'update', 'delete', 'restore'],
];