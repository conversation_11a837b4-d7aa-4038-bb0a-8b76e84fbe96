<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'google' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect' => env('GOOGLE_REDIRECT_URI'),
    ],

    'google-deployment' => [
        'project_id' => env('GOOGLE_CLOUD_PROJECT_ID'),
        'credentials' => env('GOOGLE_APPLICATION_CREDENTIALS'),
        'region' => env('GCP_RESOURCE_REGION', 'europe-west1'),
        'sql_instance_id' => env('GOOGLE_CLOUD_SQL_INSTANCE_ID', 'sonar-demo-db'),
    ],

    'godaddy' => [
        'api_key' => env('GODADDY_API_KEY'),
        'api_secret' => env('GODADDY_API_SECRET'),
        'base_domain' => env('GODADDY_BASE_DOMAIN', 'jawda.ismena.com'),
        'ote_base_url' => env('GODADDY_OTE_BASE_URL', 'https://api.ote-godaddy.com'),
        'api_base_url' => env('GODADDY_PRODUCTION_API_URL', 'https://api.godaddy.com'),
        'use_ote' => env('GODADDY_USE_OTE', false), // Set to false to use production url
        'default_ttl' => env('GODADDY_DEFAULT_TTL', 600),
    ],

    # for testing purposes only
    'sonarqube_working_instance' => [
        'url' => env('SERVICES_SONARQUBE_URL'),
        'admin_username' => env('SERVICES_SONARQUBE_ADMIN_USERNAME'),
        'admin_password' => env('SERVICES_SONARQUBE_ADMIN_PASSWORD'),
    ],

    'cloudflare' => [
        'api_token' => env('CLOUDFLARE_API_TOKEN'),
        'zone_id' => env('CLOUDFLARE_ZONE_ID'),
        'base_domain' => env('CLOUDFLARE_BASE_DOMAIN'),
    ],
];
