@extends('emails.layouts.app')

@section('content')
    <x-email.paragraph color="#585858">
        Hello <x-email.span fontWeight="700">{{ $subscription->subscriber->name }}</x-email.span>
    </x-email.paragraph>
    <x-email.heading>
        Payment Approved
    </x-email.heading>
    <x-email.paragraph marginBottom="0">
        We're pleased to inform you that your payment for your subscription has been approved.
    </x-email.paragraph>
    
    <x-email.paragraph>
        <strong>Subscription Details:</strong><br>
        • Plan: {{ $subscription->plan->name }}<br>
        • Status: {{ ucfirst($subscription->subscription_status) }}<br>
        • Start Date: {{ $subscription->starts_at_formatted_date }}<br>
        • End Date: {{ $subscription->ends_at_formatted_date }}
    </x-email.paragraph>
    
    <x-email.paragraph marginBottom="0">
        @if($subscription->subscriptionHasFeature('on-prem'))
        Your subscription is now pending license key generation. You will receive another email once your license key is ready.
        @else
        Your subscription is now pending activation. You will receive another email once your subscription is activated.
        @endif
    </x-email.paragraph>

    <x-email.paragraph>
        If you have any questions, please don't hesitate to contact our support team at <a href="mailto:{{ config('settings.product_support_email') }}">{{ config('settings.product_support_email') }}</a>.
    </x-email.paragraph>

    <x-email.paragraph color="#585858" marginBottom="0px" fontWeight="700">
        Thanks,<br> {{ config('app.name') }} Team
    </x-email.paragraph>
@endsection
