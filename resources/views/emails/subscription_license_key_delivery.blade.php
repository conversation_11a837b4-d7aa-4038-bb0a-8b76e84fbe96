@extends('emails.layouts.app')

@section('content')
    <x-email.paragraph color="#585858">
        Hi <x-email.span fontWeight="700">{{ optional($subscription->subscriber->primaryContact)->name }},</x-email.span>
    </x-email.paragraph>
    <x-email.paragraph color="#585858">
        We’re excited to let you know that your license key for server: 
    </x-email.paragraph>
    <x-email.paragraph color="#585858">
       <x-email.span fontWeight="700">{{ $license->server_id }} ( {{ $license_environment }} )</x-email.span> is now available. 🎉
    </x-email.paragraph>
    <x-email.paragraph color="#585858">
        You can now activate your server using the license key below: <x-email.span fontWeight="700">{{ $license->license_key }}</x-email.span> 
    </x-email.paragraph>
    <x-email.paragraph color="#585858">
        Need help? Our support team is always ready to assist you - <a href="mailto:{{ config('settings.product_support_email') }}">{{ config('settings.product_support_email') }}</a>.
    </x-email.paragraph>
    <x-email.paragraph color="#585858" marginBottom="0px" fontWeight="700">
        Happy coding!<br>
        {{ config('app.name') }}
    </x-email.paragraph>
@endsection
