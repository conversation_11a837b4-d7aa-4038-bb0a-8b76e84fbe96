@extends('emails.layouts.app')

@section('content')
    <x-email.paragraph color="#585858">
        Dear <x-email.span fontWeight="700">{{ $user->name }},</x-email.span>
    </x-email.paragraph>
    <x-email.paragraph color="#585858" marginBottom="0">
        This email is to inform you that your role in {{ config('app.name') }} has been updated.
    </x-email.paragraph>
    <x-email.paragraph color="#585858">
        Your role has been changed from <x-email.span fontWeight="700">{{ $previousRole }}</x-email.span> to <x-email.span fontWeight="700">{{ $newRole }}</x-email.span>.
    </x-email.paragraph>
    <x-email.paragraph color="#585858" marginBottom="0">
        This change was made by <x-email.span fontWeight="700">{{ $modifiedBy }}</x-email.span>.
     </x-email.paragraph>
    <x-email.paragraph color="#585858">
        If you believe this change is incorrect or have any questions, please contact our support team directly at <a href="mailto:{{ config('settings.product_support_email') }}">{{ config('settings.product_support_email') }}</a>
     </x-email.paragraph>
    <x-email.paragraph color="#585858" marginBottom="0px" fontWeight="700">
        Thanks,<br> {{ config('app.name') }} Team
    </x-email.paragraph>
@endsection
