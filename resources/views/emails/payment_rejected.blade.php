@extends('emails.layouts.app')

@section('content')
    <x-email.paragraph color="#585858">
        Hi <x-email.span fontWeight="700">{{ $subscription->subscriber->name }}</x-email.span>
    </x-email.paragraph>
    <x-email.heading>
        Payment Rejected
    </x-email.heading>
    <x-email.paragraph marginBottom="0">
        We regret to inform you that your payment for your subscription has been rejected.
    </x-email.paragraph>
    
    <x-email.paragraph>
        <strong>Subscription Details:</strong><br>
        • Plan: {{ $subscription->plan->name }}<br>
        • Status: {{ ucfirst($subscription->subscription_status) }}
    </x-email.paragraph>
    
    <x-email.paragraph>
        <strong>Reason for Rejection:</strong><br>
        {{ $rejectionReason }}
    </x-email.paragraph>
    
    @if($rejectionDescription)
    <x-email.paragraph>
        <strong>Additional Details:</strong><br>
        {{ $rejectionDescription }}
    </x-email.paragraph>
    @endif
    
    <x-email.paragraph marginBottom="0">
        Your subscription status has been updated to {{ ucfirst($subscription->subscription_status) }}. Please update your payment information and submit a new receipt.
    </x-email.paragraph>

    <x-email.paragraph>
        If you have any questions or need assistance, please contact our support team - <a href="mailto:{{ config('settings.product_support_email') }}">{{ config('settings.product_support_email') }}</a>.
    </x-email.paragraph>
    
    <x-email.paragraph color="#585858" marginBottom="0px" fontWeight="700">
        Thanks,<br> {{ config('app.name') }} Team
    </x-email.paragraph>
@endsection
