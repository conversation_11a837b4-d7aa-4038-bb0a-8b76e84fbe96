<div class="space-y-6">
    @if($company->history->count() > 0)
        <div class="overflow-hidden bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 rounded-xl">
            <div class="overflow-x-auto">
                <table class="w-full text-sm">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th class="px-4 py-3 text-left font-medium text-gray-900 dark:text-gray-100">Date & Time</th>
                            <th class="px-4 py-3 text-left font-medium text-gray-900 dark:text-gray-100">Action</th>
                            <th class="px-4 py-3 text-left font-medium text-gray-900 dark:text-gray-100">Change Type</th>
                            <th class="px-4 py-3 text-left font-medium text-gray-900 dark:text-gray-100">Performed By</th>
                            <th class="px-4 py-3 text-left font-medium text-gray-900 dark:text-gray-100">Changes</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach($company->history as $historyRecord)
                            <tr class="">
                                <td class="px-4 py-3 text-gray-900 dark:text-gray-100">
                                    <div class="font-medium">
                                        {{ $historyRecord->created_at->format('M d, Y') }}
                                    </div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ $historyRecord->created_at->format('H:i:s') }}
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full
                                        @if($historyRecord->action === 'created') bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400
                                        @elseif($historyRecord->action === 'updated') bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400
                                        @elseif($historyRecord->action === 'deleted') bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400
                                        @elseif($historyRecord->action === 'restored') bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400
                                        @else bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400
                                        @endif">
                                        {{ ucfirst($historyRecord->action) }}
                                    </span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full
                                        @if($historyRecord->change_type === 'direct') bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400
                                        @elseif($historyRecord->change_type === 'countries') bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400
                                        @elseif($historyRecord->change_type === 'sectors') bg-cyan-100 text-cyan-800 dark:bg-cyan-900/20 dark:text-cyan-400
                                        @elseif($historyRecord->change_type === 'industries') bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400
                                        @else bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400
                                        @endif">
                                        {{ ucfirst($historyRecord->change_type ?? 'direct') }}
                                    </span>
                                </td>
                                <td class="px-4 py-3 text-gray-900 dark:text-gray-100">
                                    @if($historyRecord->performedBy)
                                        <div class="font-medium">{{ $historyRecord->performedBy->name }}</div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400">{{ $historyRecord->performedBy->email }}</div>
                                    @else
                                        <span class="text-gray-500 dark:text-gray-400">System</span>
                                    @endif
                                </td>
                                <td class="px-4 py-3">
                                    @if($historyRecord->change_type === 'direct' && $historyRecord->changed_fields && count($historyRecord->changed_fields) > 0)
                                        {{-- Direct company field changes --}}
                                        <div class="space-y-1">
                                            @foreach($historyRecord->changed_fields as $field)
                                                <div class="text-xs">
                                                    <span class="font-medium text-gray-900 dark:text-gray-100">{{ ucfirst(str_replace('_', ' ', $field)) }}:</span>
                                                    @if(isset($historyRecord->old_values[$field]) && isset($historyRecord->new_values[$field]))
                                                        <div class="ml-2">
                                                            <span class="text-red-600 dark:text-red-400">{{ $historyRecord->old_values[$field] ?? 'null' }}</span>
                                                            <span class="text-gray-500 dark:text-gray-400"> → </span>
                                                            <span class="text-green-600 dark:text-green-400">{{ $historyRecord->new_values[$field] ?? 'null' }}</span>
                                                        </div>
                                                    @elseif(isset($historyRecord->new_values[$field]))
                                                        <span class="text-green-600 dark:text-green-400 ml-2">{{ $historyRecord->new_values[$field] }}</span>
                                                    @endif
                                                </div>
                                            @endforeach
                                        </div>
                                    @elseif(in_array($historyRecord->change_type, ['countries', 'sectors', 'industries']))
                                        {{-- Pivot table changes --}}
                                        @php
                                            $pivotChanges = $historyRecord->pivot_changes;
                                            $changeType = $historyRecord->change_type;
                                        @endphp
                                        @if(isset($pivotChanges[$changeType]))
                                            @php
                                                $change = $pivotChanges[$changeType];
                                                $oldNames = array_values($change['old']);
                                                $newNames = array_values($change['new']);
                                            @endphp
                                            <div class="text-xs">
                                                <span class="font-medium text-gray-900 dark:text-gray-100">{{ ucfirst($changeType) }}:</span>
                                                <div class="ml-2 mt-1">
                                                    @if(!empty($oldNames))
                                                        <div class="text-red-600 dark:text-red-400">
                                                            <span class="font-medium">Removed:</span> {{ implode(', ', $oldNames) }}
                                                        </div>
                                                    @endif
                                                    @if(!empty($newNames))
                                                        <div class="text-green-600 dark:text-green-400">
                                                            <span class="font-medium">Added:</span> {{ implode(', ', $newNames) }}
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        @endif
                                    @else
                                        <span class="text-gray-500 dark:text-gray-400 text-xs">
                                            @if($historyRecord->action === 'created')
                                                Company created
                                            @elseif($historyRecord->action === 'deleted')
                                                Company deleted
                                            @elseif($historyRecord->action === 'restored')
                                                Company restored
                                            @else
                                                No specific changes tracked
                                            @endif
                                        </span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        @if($company->history->count() > 20)
            <div class="text-center">
                <p class="text-sm text-gray-500 dark:text-gray-400">
                    Showing latest {{ $company->history->count() }} history records.
                </p>
            </div>
        @endif
    @else
        <div class="text-center py-12">
            <div class="mx-auto h-10 w-10 text-gray-400">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No history available</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                No history records have been created for this company yet.
            </p>
        </div>
    @endif
</div>
