<x-filament::section>
    <div class="space-y-6">
        <div class="filament-tables-container rounded-xl border border-gray-300 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800">
            <div class="filament-tables-header-container">
                <h4 class="text-xl font-bold tracking-tight p-4">
                    License History for Subscription #{{ $subscription->public_subscription_id }}
                </h4>
            </div>

            @php
                $licenseHistory = \App\Models\SubscriptionLicenseHistory::where('subscription_id', $subscription->id)
                    ->orderBy('created_at', 'desc')
                    ->limit(20)
                    ->get();
            @endphp

            <div class="overflow-x-auto">
                <table class="filament-tables-table w-full table-auto divide-y divide-gray-200 text-start dark:divide-gray-700">
                    <thead>
                        <tr class="bg-gray-50 dark:bg-gray-700">
                            <th class="p-2 text-start text-sm font-medium text-gray-600 dark:text-gray-200">Date</th>
                            <th class="p-2 text-start text-sm font-medium text-gray-600 dark:text-gray-200">Event</th>
                            <th class="p-2 text-start text-sm font-medium text-gray-600 dark:text-gray-200">License Key</th>
                            <th class="p-2 text-start text-sm font-medium text-gray-600 dark:text-gray-200">Server ID</th>
                            <th class="p-2 text-start text-sm font-medium text-gray-600 dark:text-gray-200">Type</th>
                            <th class="p-2 text-start text-sm font-medium text-gray-600 dark:text-gray-200">Environment</th>
{{--                            <th class="p-2 text-start text-sm font-medium text-gray-600 dark:text-gray-200">Change Reason</th>--}}
                            <th class="p-2 text-start text-sm font-medium text-gray-600 dark:text-gray-200">User</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($licenseHistory as $history)
                            <tr class="filament-tables-row transition">
                                <td class="p-2 text-sm text-gray-600 dark:text-gray-400">
                                    {{ $history->created_at->format('Y-m-d H:i:s') }}
                                </td>
                                <td class="p-2 text-sm">
                                    <span class="inline-flex items-center justify-center whitespace-nowrap rounded-full px-2 py-0.5 text-xs font-medium">
                                        {{ App\Enums\Subscriptions::getLabel($history->history_event) ?? ucfirst($history->history_event) }}
                                    </span>
                                </td>
                                <td class="p-2 text-sm text-gray-600 dark:text-gray-400">
                                    {{ $history->license_key ?: '-' }}
                                </td>
                                <td class="p-2 text-sm text-gray-600 dark:text-gray-400">
                                    {{ $history->server_id ?: '-' }}
                                </td>
                                <td class="p-2 text-sm text-gray-600 dark:text-gray-400">
                                    {{ ucfirst($history->license_type ?: '-') }}
                                </td>
                                <td class="p-2 text-sm text-gray-600 dark:text-gray-400">
                                    {{ ucfirst($history->environment ?: '-') }}
                                </td>
{{--                                <td class="p-2 text-sm text-gray-600 dark:text-gray-400">--}}
{{--                                    {{ $history->change_reason ?: '-' }}--}}
{{--                                </td>--}}
                                <td class="p-2 text-sm text-gray-600 dark:text-gray-400">
                                    @php
                                        $user = \App\Models\User::find($history->created_by);
                                    @endphp
                                    {{ $user ? $user->name : $history->created_by }}
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="p-4 text-center text-sm text-gray-500 dark:text-gray-400">
                                    No license history records found.
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

{{--            <div class="p-2">--}}
{{--                {{ $licenseHistory->links() }}--}}
{{--            </div>--}}
        </div>
    </div>
</x-filament::section>