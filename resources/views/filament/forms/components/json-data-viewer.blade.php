<div class="p-2 bg-white dark:bg-gray-800 rounded-lg shadow">
    @php
        $data = $getState();

        function resolveIdToName($key, $value) {
            $map = [
                'user_id' => \App\Models\User::class,
                'created_by' => \App\Models\User::class,
                'updated_by' => \App\Models\User::class,
                'deleted_by' => \App\Models\User::class,
                'subscription_id' => \App\Models\Subscription::class,
                'plan_id' => \App\Models\Plan::class,
                'company_id' => \App\Models\Company::class,
                'country_id' => \App\Models\Country::class,
                'billing_country_id' => \App\Models\Country::class,
                'currency_id' => \App\Models\Currency::class,
                'status_id' => \App\Models\Status::class,
                'suspension_reason_id' => \App\Models\SuspensionReason::class,
            ];

            if (isset($map[$key]) && $value) {
                $model = $map[$key]::find($value);
                return $model ? $model->name : $value;
            }

            return $value;
        }

        // Updated categories with new order
        $categories = [
            'Subscription Details' => [
                'plan_id', 'subscription_type', 'plan_features', 'addon_features',
                'assigned_quota', 'used_quota', 'remaining_quota', 'subscription_status',
                'auto_renew'
            ],
            'Subscriber Details' => [
                'subscriber_type', 'subscriber_id', 'country_id', 'public_subscription_id'
            ],
            'Payment Details' => [
                'currency_id', 'payment_status', 'billing_country_id', 'last_payment_date'
            ],
            'Subscription License Details' => [
                'subscription_licenses'
            ],
            'Payment Rejection Details' => [
                'rejection_reason', 'rejection_reason_description'
            ],
            'Dates' => [
                'trial_ends_at', 'starts_at', 'ends_at', 'canceled_at', 'cancels_at'
            ],
            'Audit' => [
                'created_at', 'created_by', 'updated_at', 'updated_by', 'deleted_at', 'deleted_by'
            ]
        ];

        // Create reverse mapping
        $keyToCategory = [];
        foreach ($categories as $category => $keys) {
            foreach ($keys as $key) {
                $keyToCategory[$key] = $category;
            }
        }

        $grouped = [];
        foreach ((array)$data as $key => $value) {
            if (isset($keyToCategory[$key])) {
                $grouped[$keyToCategory[$key]][$key] = $value;
            } else {
                $grouped['Other'][$key] = $value;
            }
        }

        // Define section display order
        $sectionOrder = [
            'Subscription Details',
            'Subscriber Details',
            'Payment Details',
            'Subscription License Details',
            'Payment Rejection Details',
            'Dates',
            'Audit',
            'Other'
        ];
    @endphp

    @if(is_array($data) || is_object($data))
        <div class="space-y-4">
            @foreach($sectionOrder as $section)
                @if(!empty($grouped[$section]))
                    <details class="group bg-gray-50 dark:bg-gray-900 rounded-lg p-3 transition-all duration-300" open>
                        <summary class="flex justify-between items-center cursor-pointer list-none">
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white">
                                {{ $section }}
                            </h3>
                            <span class="transition-transform duration-300 group-open:rotate-180 text-gray-500">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </span>
                        </summary>
                        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                @foreach($grouped[$section] as $key => $value)
                                    <div class="mb-3">
                                        <div class="text-sm font-semibold text-gray-700 dark:text-gray-300">
                                            {{ str_ends_with($key, '_id') ? str_replace('_id', '', ucfirst(str_replace('_', ' ', $key))) : ucfirst(str_replace('_', ' ', $key)) }}
                                        </div>
                                        <div class="text-sm text-gray-900 dark:text-gray-100 mt-1">
                                            @if(is_array($value) || is_object($value))
                                                <details class="text-sm">
                                                    <summary class="cursor-pointer text-primary-600 hover:text-primary-500">
                                                        {{ is_array($value) ? 'Array' : 'Object' }} ({{ count((array)$value) }} items)
                                                    </summary>
                                                    <div class="mt-2 pl-4 border-l-2 border-gray-200 dark:border-gray-700">
                                                        <table class="w-full text-sm">
                                                            <tbody>
                                                            @foreach((array)$value as $nestedKey => $nestedValue)
                                                                <tr class="border-t dark:border-gray-700">
                                                                    <td class="py-2 px-4 font-medium text-gray-700 dark:text-gray-200 bg-gray-50 dark:bg-gray-700">{{ $nestedKey }}</td>
                                                                    <td class="py-2 px-4 dark:text-gray-200">
                                                                        @if(is_array($nestedValue) || is_object($nestedValue))
                                                                            <code class="text-xs bg-gray-100 dark:bg-gray-700 p-1 rounded">{{ json_encode($nestedValue, JSON_PRETTY_PRINT) }}</code>
                                                                        @elseif(is_bool($nestedValue))
                                                                            <span class="px-2 py-1 text-xs font-medium rounded-full {{ $nestedValue ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                                                {{ $nestedValue ? 'true' : 'false' }}
                                                                            </span>
                                                                        @elseif(is_null($nestedValue))
                                                                            <span class="text-gray-400 italic">null</span>
                                                                        @else
                                                                            {{ $nestedValue }}
                                                                        @endif
                                                                    </td>
                                                                </tr>
                                                            @endforeach
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </details>
                                            @elseif(is_bool($value))
                                                <span class="px-2 py-1 text-xs font-medium rounded-full {{ $value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                {{ $value ? 'true' : 'false' }}
                                            </span>
                                            @elseif(is_null($value))
                                                <span class="text-gray-400 italic">null</span>
                                            @elseif(preg_match('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/', $value))
                                                {{ \Carbon\Carbon::parse($value)->format('Y-m-d H:i:s') }}
                                            @elseif(str_ends_with($key, '_id') || in_array($key, ['created_by', 'updated_by', 'deleted_by']))
                                                <div class="flex items-center">
                                                    <span>{{ resolveIdToName($key, $value) }}</span>
                                                    <span class="ml-2 text-xs text-gray-500">(ID: {{ $value }})</span>
                                                </div>
                                            @else
                                                {{ $value }}
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </details>
                @endif
            @endforeach
        </div>
    @elseif(is_null($data))
        <div class="p-2 text-gray-500 dark:text-gray-400 italic">No data available</div>
    @else
        <div class="p-2 dark:text-gray-200">{{ $data }}</div>
    @endif
</div>