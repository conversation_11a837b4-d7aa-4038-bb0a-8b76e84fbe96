@php
    $url = $getRecord()->receipt_url;
    $path = parse_url($url, PHP_URL_PATH);
    $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
@endphp

<div x-data="{ showModal: false }">
    @if($extension === '')
        <span class="text-gray-500 text-sm ">No receipt available</span>
    @elseif(in_array($extension, ['jpg', 'jpeg', 'png']))
        <img
            src="{{ $url }}"
            alt="Receipt Preview"
            class="h-11 rounded-sm cursor-pointer"
            @click="showModal = true"
        />

        <div
            x-show="showModal"
            class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
            x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            style="display: none;" 
        >
            <div
                class="relative max-w-full max-h-full p-4 rounded shadow-lg"
                @click.away="showModal = false;" 
                x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0 transform scale-95"
                x-transition:enter-end="opacity-100 transform scale-100"
                x-transition:leave="transition ease-in duration-200"
                x-transition:leave-start="opacity-100 transform scale-100"
                x-transition:leave-end="opacity-0 transform scale-95"
            >
                <img src="{{ $url }}" class="block max-h-[85vh] max-w-[90vw] rounded"/> 
                <button
                    @click="showModal = false"
                    class="absolute text-white bg-black bg-opacity-50 rounded-full p-1 leading-none hover:bg-opacity-75 focus:outline-none"
                    style="font-size: 1.5rem; cursor: pointer; top: 15px; right: 25px;" 
                    aria-label="Close modal"
                >
                    &times;
                </button>
            </div>
        </div>
    @elseif($extension === 'pdf')
        <a href="{{ $url }}" target="_blank" class="text-sm text-primary-600 underline">View PDF</a>
    @else
        <span class="text-gray-500 text-sm">Unsupported format</span>
    @endif
</div>