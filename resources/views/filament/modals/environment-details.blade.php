<div>
    {{-- Main grid container --}}
    <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-4">

        {{-- Row 1, Column 1: Server ID --}}
        <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Server ID</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                <div x-data="{ copied: false, valueToCopy: '{{ $serverId ?? '' }}' }" class="flex items-center">
                    <span class="truncate flex-grow" title="{{ $serverId ?? '' }}">{{ $serverId ?? 'N/A' }}</span>
                    <button
                        @click="
                            if (valueToCopy.trim()) {
                                navigator.clipboard.writeText(valueToCopy.trim());
                                copied = true;
                                setTimeout(() => { copied = false }, 2000);
                            }
                        "
                        type="button"
                        class="ml-3 p-1 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 rounded-md focus:outline-none"
                        :title="copied ? 'Copied!' : 'Copy Server ID'" aria-label="Copy Server ID">
                        <template x-if="!copied">
                            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M15.666 3.888A2.25 2.25 0 0 0 13.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.*************.084.612v0a.75.75 0 0 1-.75.75H9a.75.75 0 0 1-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 0 1-2.25 2.25H6.75A2.25 2.25 0 0 1 4.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 0 1 1.927-.184" />
                            </svg>
                        </template>
                        <template x-if="copied">
                            <svg class="w-5 h-5 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                            </svg>
                        </template>
                    </button>
                </div>
            </dd>
        </div>

        {{-- Row 2, Column 1: Sonar URL --}}
        <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Sonar URL</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                <div x-data="{ copied: false, valueToCopy: '{{ $sonarUrl ?? '' }}' }" class="flex items-center">
                    <a target="_blank" href="{{ $sonarUrl ?? '#' }}"
                        class="truncate text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300 flex-grow"
                        title="{{ $sonarUrl ?? '' }}">{{ $sonarUrl ?? 'N/A' }}</a>
                    <button
                        @click="
                            if (valueToCopy.trim()) {
                                navigator.clipboard.writeText(valueToCopy.trim());
                                copied = true;
                                setTimeout(() => { copied = false }, 2000);
                            }
                        "
                        type="button"
                        class="ml-3 p-1 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 rounded-md focus:outline-none"
                        :title="copied ? 'Copied!' : 'Copy Sonar URL'" aria-label="Copy Sonar URL">
                        <template x-if="!copied">
                            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M15.666 3.888A2.25 2.25 0 0 0 13.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.*************.084.612v0a.75.75 0 0 1-.75.75H9a.75.75 0 0 1-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 0 1-2.25 2.25H6.75A2.25 2.25 0 0 1 4.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 0 1 1.927-.184" />
                            </svg>
                        </template>
                        <template x-if="copied">
                            <svg class="w-5 h-5 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                            </svg>
                        </template>
                    </button>
                </div>
            </dd>
        </div>

        {{-- Row 1, Column 2: Username --}}
        <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Username</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                {{-- Assume $username variable is passed to the view --}}
                <div x-data="{ copied: false, valueToCopy: '{{ $sonarUsername ?? '' }}' }" class="flex items-center">
                    <span class="truncate flex-grow" title="{{ $sonarUsername ?? '' }}">{{ $sonarUsername ?? 'N/A' }}</span>
                    <button
                        @click="
                            if (valueToCopy.trim()) {
                                navigator.clipboard.writeText(valueToCopy.trim());
                                copied = true;
                                setTimeout(() => { copied = false }, 2000);
                            }
                        "
                        type="button"
                        class="ml-3 p-1 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 rounded-md focus:outline-none"
                        :title="copied ? 'Copied!' : 'Copy Username'" aria-label="Copy Username">
                        <template x-if="!copied">
                            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M15.666 3.888A2.25 2.25 0 0 0 13.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.*************.084.612v0a.75.75 0 0 1-.75.75H9a.75.75 0 0 1-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 0 1-2.25 2.25H6.75A2.25 2.25 0 0 1 4.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 0 1 1.927-.184" />
                            </svg>
                        </template>
                        <template x-if="copied">
                            <svg class="w-5 h-5 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                            </svg>
                        </template>
                    </button>
                </div>
            </dd>
        </div>

        {{-- Row 2, Column 2: Password --}}
        <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Password</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                {{-- Assume $password variable is passed to the view --}}
                <div x-data="{ copied: false, valueToCopy: '{{ $sonarPassword ?? '' }}', showPassword: false }" class="flex items-center">
                    <span x-show="!showPassword" class="truncate flex-grow"
                        title="Click to show/hide password">••••••••</span>
                    <span x-show="showPassword" x-text="valueToCopy || 'N/A'" class="truncate flex-grow"
                        title="Click to show/hide password"></span>
                    <button @click="showPassword = !showPassword" type="button"
                        class="ml-3 p-1 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 rounded-md focus:outline-none"
                        :title="showPassword ? 'Hide Password' : 'Show Password'"
                        aria-label="Toggle Password Visibility">
                        <template x-if="!showPassword">
                            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z" />
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                            </svg>
                        </template>
                        <template x-if="showPassword">
                            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.753 9.753" />
                            </svg>
                        </template>
                    </button>
                    <button
                        @click="
                            if (valueToCopy.trim()) {
                                navigator.clipboard.writeText(valueToCopy.trim());
                                copied = true;
                                setTimeout(() => { copied = false }, 2000);
                            }
                        "
                        type="button"
                        class="ml-2 p-1 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 rounded-md focus:outline-none"
                        :title="copied ? 'Copied!' : 'Copy Password'" aria-label="Copy Password">
                        <template x-if="!copied">
                            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M15.666 3.888A2.25 2.25 0 0 0 13.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.*************.084.612v0a.75.75 0 0 1-.75.75H9a.75.75 0 0 1-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 0 1-2.25 2.25H6.75A2.25 2.25 0 0 1 4.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 0 1 1.927-.184" />
                            </svg>
                        </template>
                        <template x-if="copied">
                            <svg class="w-5 h-5 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                            </svg>
                        </template>
                    </button>
                </div>
            </dd>
        </div>

    </div>
</div>
