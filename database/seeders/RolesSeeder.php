<?php

namespace Database\Seeders;

use App\Helpers\AppHelper;
use App\Helpers\RbacHelper;
use Spatie\Permission\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Seeder;

class RolesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'name'       => RbacHelper::ROLE_PRIMARY,
                'type'       => RbacHelper::TYPE_CUSTOMER,
                'guard_name' => RbacHelper::GUARD_API,
                'sort_order' => 0,
                'exposed_to_customer' => false,
            ],
            [
                'name'       => RbacHelper::ROLE_ADMIN,
                'type'       => RbacHelper::TYPE_CUSTOMER,
                'guard_name' => RbacHelper::GUARD_API,
                'sort_order' => 1
            ],
            [
                'name'       => RbacHelper::ROLE_DEVELOPER,
                'type'       => RbacHelper::TYPE_CUSTOMER,
                'guard_name' => RbacHelper::GUARD_API,
                'sort_order' => 2
            ],
            [
                'name'       => RbacHelper::ROLE_READ_ONLY,
                'type'       => RbacHelper::TYPE_CUSTOMER,
                'guard_name' => RbacHelper::GUARD_API,
                'sort_order' => 3
            ],
        ];

        foreach ($roles as $role) {
            $roleModel = Role::updateOrCreate(
                ['name' => $role['name']],
                $role
            );

            /**
            * Primary 
            */
            if (AppHelper::matchStrings($roleModel->name, RbacHelper::ROLE_PRIMARY)) {
                $permissions = static::flattenPermissions(
                    RbacHelper::getRolePermissionsArray([
                        'subscriptions' => ['limited_access']
                    ])
                );

                foreach ($permissions as $permissionName) {
                    Permission::firstOrCreate([
                        'name'       => $permissionName,
                        'guard_name' => RbacHelper::GUARD_API,
                    ]);
                }

                $roleModel->syncPermissions($permissions);
            }

            /**
            * Admin 
            */
            if (AppHelper::matchStrings($roleModel->name, RbacHelper::ROLE_ADMIN)) {
                $permissions = static::flattenPermissions(
                    RbacHelper::getRolePermissionsArray([
                        'customer-users' => ['update_primary'],
                        'roles' => ['update_primary'],
                    ])
                );

                $roleModel->syncPermissions($permissions);
            }

            /**
            * Developer 
            */
            if (AppHelper::matchStrings($roleModel->name, RbacHelper::ROLE_DEVELOPER)) {
                $permissions = static::flattenPermissions(
                    RbacHelper::getRolePermissionsArray([
                        'customer-profile' => ['update'],
                        'subscriptions' => ['create', 'update'],
                        'billings' =>['*'],
                        'payments' =>['*'],
                        'customer-users' => ['*'],
                        'roles' => ['*'],
                    ])
                );

                $roleModel->syncPermissions($permissions);
            }

            /**
            * Read Only 
            */
            if (AppHelper::matchStrings($roleModel->name, RbacHelper::ROLE_READ_ONLY)) {
                $permissions = static::flattenPermissions(
                    RbacHelper::getRolePermissionsArray([
                        'customer-profile' => ['update'],
                        'subscriptions' => ['create', 'update'],
                        'billings' =>['create', 'update'],
                        'payments' =>['create', 'update'],
                        'customer-users' => ['*'],
                        'roles' => ['*'],
                        'server-id' => ['*'],
                        'licenses' => ['*'],
                    ])
                );

                $roleModel->syncPermissions($permissions);
            }
        }
    }

    protected static function flattenPermissions(array $permissions): array
    {
        $flattened = [];

        foreach ($permissions as $module => $actions) {
            foreach ($actions as $action) {
                $flattened[] = "{$action}::{$module}";
            }
        }

        return $flattened;
    }
}
