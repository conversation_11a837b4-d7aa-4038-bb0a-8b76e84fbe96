<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Helpers\RbacHelper;

class RolePermissionSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin roles
        $isolutionAdminRole = Role::firstOrCreate([
            'name' => RbacHelper::ROLE_ISOLUTION_ADMIN,
            'type' => RbacHelper::TYPE_ADMIN,
            'guard_name' => RbacHelper::GUARD_WEB,
            'sort_order' => 5
        ]);

        $sonarTeamRole = Role::firstOrCreate([
            'name' => RbacHelper::ROLE_SONAR_TEAM,
            'type' => RbacHelper::TYPE_ADMIN,
            'guard_name' => RbacHelper::GUARD_WEB,
            'sort_order' => 6
        ]);

        $financeTeamRole = Role::firstOrCreate([
            'name' => RbacHelper::ROLE_FINANCE_TEAM,
            'type' => RbacHelper::TYPE_ADMIN,
            'guard_name' => RbacHelper::GUARD_WEB,
            'sort_order' => 7
        ]);

        // Get permissions from config files
        $isolutionAdminPermissions = $this->getPermissionsFromConfig('isolution-admin-permissions');
        $sonarTeamPermissions = $this->getPermissionsFromConfig('sonar-team-permissions');
        $financeTeamPermissions = $this->getPermissionsFromConfig('finance-team-permissions');

        // Create and assign permissions for each role
        $this->assignPermissions($isolutionAdminRole, $isolutionAdminPermissions, RbacHelper::GUARD_WEB);
        $this->assignPermissions($sonarTeamRole, $sonarTeamPermissions, RbacHelper::GUARD_WEB);
        $this->assignPermissions($financeTeamRole, $financeTeamPermissions, RbacHelper::GUARD_WEB);
    }

    /**
     * Helper method to create and assign permissions to a role
     */
    private function assignPermissions(Role $role, array $permissions, string $guardName): void
    {
        foreach ($permissions as $permName) {
            $permission = Permission::firstOrCreate([
                'name' => $permName,
                'guard_name' => $guardName
            ]);
            
            if (!$role->hasPermissionTo($permission)) {
                $role->givePermissionTo($permission);
            }
        }
    }

    /**
     * Get flattened permissions from config file
     */
    private function getPermissionsFromConfig(string $configName): array
    {
        $config = config($configName, []);
        $permissions = [];

        foreach ($config as $module => $actions) {
            foreach ($actions as $action) {
                // Handle special cases like 'approve_payment'
                if (in_array($action, ['audit_records', 'rollback_audit', 'approve_payment'])) {
                    $permissions[] = $action;
                } else {
                    $permissions[] = "{$action}_{$module}";
                }
            }
        }

        return $permissions;
    }
}