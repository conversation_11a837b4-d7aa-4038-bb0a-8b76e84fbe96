<?php

namespace Database\Seeders;

use App\Models\PlatformCountry;
use App\Models\SuspensionReason;
use Illuminate\Database\Seeder;

class PlatformCountrySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $platformCountries = [
            [
                'name' => 'United Arab Emirates',
                'code' => 'AE',
                'phone' => '784',
                'lat' => '24.00',
                'lang' => '54.00',
            ],
            [
                'name' => 'Qatar',
                'code' => 'QA',
                'phone' => '634',
                'lat' => '25.50',
                'lang' => '51.25',
            ],
            [
                'name' => 'Saudi Arabia',
                'code' => 'SA',
                'phone' => '682',
                'lat' => '25.00',
                'lang' => '45.00',
            ],
        ];

        collect($platformCountries)->each(function ($platformCountry) {
            PlatformCountry::updateOrCreate(
                ['name' => $platformCountry['name']],
                $platformCountry
            );
        });
    }
}
