<?php

namespace Database\Seeders;

use App\Enums\DefaultValues;
use App\Models\Feature;
use App\Models\Plan;
use App\Models\PlanFeature;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class PlanSeeder extends Seeder
{
    public function run(): void
    {
        Schema::disableForeignKeyConstraints();

        Plan::truncate();
        Feature::truncate();
        PlanFeature::truncate();

        Schema::enableForeignKeyConstraints();


        DB::transaction(function () {

            $defaultCurrency = DefaultValues::CURRENCY->get();

            $features = [
                [
                    'id' => 1,
                    'name' => 'On Prem',
                    'valid_from_minimum' => null,
                    'valid_to_maximum'   => null,
                    'price' => 0,
                    'value' => 0,
                ],
                [
                    'id' => 2,
                    'name' => 'Saas',
                    'valid_from_minimum' => null,
                    'valid_to_maximum'   => null,
                    'price' => 0,
                    'value' => 0,
                ],
            ];

            /**
             * Addons and Extra Addons 
             */
            $addons = [
                'On Prem' => $this->getPlansAddons(),
                'Saas' => $this->getPlansAddons()
            ];

            $extraAddons = [
                'On Prem' => $this->getPlansExtraAddons(),
                'Saas' => $this->getPlansExtraAddons()
            ];

            $lineOfCodes = [
                'On Prem' => [
                    [
                        'name' => 'line of code bundle 1',
                        'valid_from_minimum' => 1,
                        'valid_to_maximum'   => 1000000,
                        'price' => 44100,
                        'value' => 0,
                    ],
                    [
                        'name' => 'line of code bundle 2',
                        'valid_from_minimum' => 1000001,
                        'valid_to_maximum'   => 5000000,
                        'price' => 57700,
                        'value' => 0,
                    ],
                    [
                        'name' => 'line of code bundle 3',
                        'valid_from_minimum' => 5000001,
                        'valid_to_maximum'   => 10000000,
                        'price' => 77200,
                        'value' => 0,
                    ],
                    [
                        'name' => 'line of code bundle 4',
                        'valid_from_minimum' => 10000001,
                        'valid_to_maximum'   => 20000000,
                        'price' => 93400,
                        'value' => 0,
                    ],
                    [
                        'name' => 'line of code bundle 5',
                        'valid_from_minimum' => 20000001,
                        'valid_to_maximum'   => 50000000,
                        'price' => 154300,
                        'value' => 0,
                    ],
                ],
                'Saas' => [
                    [
                        'name' => 'line of code bundle 1',
                        'valid_from_minimum' => 1,
                        'valid_to_maximum'   => 1000000,
                        'price' => 63345,
                        'value' => 0,
                    ],
                    [
                        'name' => 'line of code bundle 2',
                        'valid_from_minimum' => 1000001,
                        'valid_to_maximum'   => 5000000,
                        'price' => 76945,
                        'value' => 0,
                    ],
                    [
                        'name' => 'line of code bundle 3',
                        'valid_from_minimum' => 5000001,
                        'valid_to_maximum'   => 10000000,
                        'price' => 115690,
                        'value' => 0,
                    ],
                    [
                        'name' => 'line of code bundle 4',
                        'valid_from_minimum' => 10000001,
                        'valid_to_maximum'   => 20000000,
                        'price' => 131890,
                        'value' => 0,
                    ],
                    [
                        'name' => 'line of code bundle 5',
                        'valid_from_minimum' => 20000001,
                        'valid_to_maximum'   => 50000000,
                        'price' => 192790,
                        'value' => 0,
                    ],
                ],
            ];

            $plans = [
                [
                    'name' => "Trial Version",
                    'description' => "Analyze your projects, identify code smells, bugs, and vulnerabilities, and explore the platform's features and benefits before committing to a subscription.",
                    'currency' => $defaultCurrency,
                    'billing_cycle' => 'yearly',
                    'trial_interval' => 'day',
                    'trial_period' => DefaultValues::SUBSCRIPTION_TRIAL_PERIOD_DAYS->get(),
                    'is_active' => true,
                    'features' => $features,
                    'line_of_codes' => $lineOfCodes
                ],
                [
                    'name' => 'Enterprise Edition',
                    'description' => 'Enterprise Edition offers advanced features and functionalities to meet the demanding needs of enterprise software development. ',
                    'badge' => 'Starting USD 40K a year - VAT Excl.',
                    'currency' => $defaultCurrency,
                    'billing_cycle' => 'yearly',
                    'is_active' => true,
                    'features' => $features,
                    'line_of_codes' => $lineOfCodes,
                    'addons' => $extraAddons
                ],
                [
                    'name' => 'Data Center Edition',
                    'description' => 'Data Center Edition offers a clustered deployment option. This ensures continuous operation and optimal performance even under heavy loads, making it ideal for mission-critical applications and large development teams.',
                    'badge' => 'Starting USD 40K a year - VAT Excl.',
                    'currency' => $defaultCurrency,
                    'billing_cycle' => 'yearly',
                    'is_active' => true,
                    'features' => $features,
                    'addons' => [
                        'On Prem' => array_merge($addons['On Prem'], $extraAddons['On Prem']),
                        'Saas'    => array_merge($addons['Saas'], $extraAddons['Saas'])
                    ],
                    'line_of_codes' => $lineOfCodes
                ],
            ];

            foreach ($plans as $plan) {
                $planFeatures = $plan['features'];
                $planAddons = $plan['addons'] ?? [];
                $planLineOfCodes = $plan['line_of_codes'];

                unset($plan['features']);
                unset($plan['addons']);
                unset($plan['line_of_codes']);

                $planModel = Plan::updateOrCreate([
                    'name->en' => $plan['name'],
                ], $plan);

                /**
                 * Features 
                 */
                collect($planFeatures)->each(function ($feature) use ($planModel) {
                    $featureModel = Feature::updateOrCreate(['plan_id' => $planModel->id, 'name->en' => $feature['name']], [
                        'plan_id' => $planModel->id,
                        'name->en' => $feature['name'],
                        'value' => $feature['value']
                    ]);

                    PlanFeature::updateOrCreate([
                        'plan_id' => $planModel->id,
                        'feature_id' => $featureModel->id,
                        'valid_from_minimum' => $feature['valid_from_minimum'],
                        'valid_to_maximum' => $feature['valid_to_maximum'],
                        'price' => $feature['price'],
                    ], [
                        'plan_id' => $planModel->id,
                        'feature_id' => $featureModel->id,
                        'valid_from_minimum' => $feature['valid_from_minimum'],
                        'valid_to_maximum' => $feature['valid_to_maximum'],
                        'price' => $feature['price'],
                    ]);
                });

                /**
                 * Addons 
                 */
                collect($planAddons)->each(function ($planAddonsArr, $featureName) use ($planModel) {
                    collect($planAddonsArr)->each(function ($planAddon) use ($planModel, $featureName) {
                        $featureModel = Feature::create([
                            'plan_id' => $planModel->id,
                            'name->en' => $planAddon['name'],
                            'value' => $planAddon['value']
                        ]);

                        $featureId = Feature::where('name->en', $featureName)->where('plan_id', $planModel->id)->first()->id ?? null;

                        PlanFeature::updateOrCreate([
                            'plan_id' => $planModel->id,
                            'feature_id' => $featureModel->id,
                            'parent_plan_feature_id' => $featureId,
                            'valid_from_minimum' => $planAddon['valid_from_minimum'],
                            'valid_to_maximum' => $planAddon['valid_to_maximum'],
                            'price' => $planAddon['price'],
                        ], [
                            'plan_id' => $planModel->id,
                            'feature_id' => $featureModel->id,
                            'parent_plan_feature_id' => $featureId,
                            'valid_from_minimum' => $planAddon['valid_from_minimum'],
                            'valid_to_maximum' => $planAddon['valid_to_maximum'],
                            'price' => $planAddon['price'],
                            'is_addon' => $planAddon['is_addon'],
                            'is_extra_addon' => $planAddon['is_extra_addon'] ?? 0,
                        ]);
                    });
                });

                /**
                 * Line Of Codes 
                 */
                collect($planLineOfCodes)->each(function ($lineOfCodeArr, $featureName) use ($planModel) {
                    collect($lineOfCodeArr)->each(function ($lineOfCode) use ($planModel, $featureName) {
                        $featureModel = Feature::create([
                            'plan_id' => $planModel->id,
                            'name->en' => $lineOfCode['name'],
                            'value' => $lineOfCode['value']
                        ]);

                        $featureId = Feature::where('name->en', $featureName)->where('plan_id', $planModel->id)->first()->id ?? null;

                        PlanFeature::updateOrCreate([
                            'plan_id' => $planModel->id,
                            'feature_id' => $featureModel->id,
                            'parent_plan_feature_id' => $featureId,
                            'valid_from_minimum' => $lineOfCode['valid_from_minimum'],
                            'valid_to_maximum' => $lineOfCode['valid_to_maximum'],
                            'price' => $lineOfCode['price'],
                        ], [
                            'plan_id' => $planModel->id,
                            'feature_id' => $featureModel->id,
                            'parent_plan_feature_id' => $featureId,
                            'valid_from_minimum' => $lineOfCode['valid_from_minimum'],
                            'valid_to_maximum' => $lineOfCode['valid_to_maximum'],
                            'price' => $lineOfCode['price'],
                        ]);
                    });
                });
            }
        });
    }

    /**
     * Plans Normal Addons ( DRs )
     * @return array[]
     */
    function getPlansAddons(): array
    {
        return [
            [
                'name' => 'Include Disaster Recovery',
                'valid_from_minimum' => null,
                'valid_to_maximum' => null,
                'price' => 0,
                'value' => 0,
                'is_addon' => true,
            ],
            [
                'name' => 'Include Disaster Recovery',
                'valid_from_minimum' => 1,
                'valid_to_maximum' => 1000000,
                'price' => 0,
                'value' => 0,
                'is_addon' => true,
            ],
            [
                'name' => 'Include Disaster Recovery',
                'valid_from_minimum' => 1000001,
                'valid_to_maximum' => 5000000,
                'price' => 0,
                'value' => 0,
                'is_addon' => true,
            ],
            [
                'name' => 'Include Disaster Recovery',
                'valid_from_minimum' => 5000001,
                'valid_to_maximum' => 10000000,
                'price' => 0,
                'value' => 0,
                'is_addon' => true,
            ],
            [
                'name' => 'Include Disaster Recovery',
                'valid_from_minimum' => 10000001,
                'valid_to_maximum' => 20000000,
                'price' => 0,
                'value' => 0,
                'is_addon' => true,
            ],
            [
                'name' => 'Include Disaster Recovery',
                'valid_from_minimum' => 20000001,
                'valid_to_maximum' => 50000000,
                'price' => 0,
                'value' => 0,
                'is_addon' => true,
            ],
        ];
    }

    /**
     * Plans Extra Addons ( Premium Support, Security Add-on ) 
     * @return array[]
     */
    function getPlansExtraAddons(): array
    {
        return [
            [
                'name' => 'Premium support',
                'valid_from_minimum' => null,
                'valid_to_maximum' => null,
                'price' => 0,
                'value' => 0,
                'is_addon' => true,
                'is_extra_addon' => true,
            ],
            [
                'name' => 'Premium support',
                'valid_from_minimum' => 1,
                'valid_to_maximum' => 1000000,
                'price' => 1000,
                'value' => 0,
                'is_addon' => true,
                'is_extra_addon' => true,
            ],
            [
                'name' => 'Premium support',
                'valid_from_minimum' => 1000001,
                'valid_to_maximum' => 5000000,
                'price' => 2000,
                'value' => 0,
                'is_addon' => true,
                'is_extra_addon' => true,
            ],
            [
                'name' => 'Premium support',
                'valid_from_minimum' => 5000001,
                'valid_to_maximum' => 10000000,
                'price' => 3000,
                'value' => 0,
                'is_addon' => true,
                'is_extra_addon' => true,
            ],
            [
                'name' => 'Premium support',
                'valid_from_minimum' => 10000001,
                'valid_to_maximum' => 20000000,
                'price' => 4000,
                'value' => 0,
                'is_addon' => true,
                'is_extra_addon' => true,
            ],
            [
                'name' => 'Premium support',
                'valid_from_minimum' => 20000001,
                'valid_to_maximum' => 50000000,
                'price' => 5000,
                'value' => 0,
                'is_addon' => true,
                'is_extra_addon' => true,
            ],
            [
                'name' => 'Security add-on',
                'valid_from_minimum' => null,
                'valid_to_maximum' => null,
                'price' => 0,
                'value' => 0,
                'is_addon' => true,
                'is_extra_addon' => true,
            ],
            [
                'name' => 'Security add-on',
                'valid_from_minimum' => 1,
                'valid_to_maximum' => 1000000,
                'price' => 1500,
                'value' => 0,
                'is_addon' => true,
                'is_extra_addon' => true,
            ],
            [
                'name' => 'Security add-on',
                'valid_from_minimum' => 1000001,
                'valid_to_maximum' => 5000000,
                'price' => 2500,
                'value' => 0,
                'is_addon' => true,
                'is_extra_addon' => true,
            ],
            [
                'name' => 'Security add-on',
                'valid_from_minimum' => 5000001,
                'valid_to_maximum' => 10000000,
                'price' => 3500,
                'value' => 0,
                'is_addon' => true,
                'is_extra_addon' => true,
            ],
            [
                'name' => 'Security add-on',
                'valid_from_minimum' => 10000001,
                'valid_to_maximum' => 20000000,
                'price' => 4500,
                'value' => 0,
                'is_addon' => true,
                'is_extra_addon' => true,
            ],
            [
                'name' => 'Security add-on',
                'valid_from_minimum' => 20000001,
                'valid_to_maximum' => 50000000,
                'price' => 5500,
                'value' => 0,
                'is_addon' => true,
                'is_extra_addon' => true,
            ],
        ];
    }
}
