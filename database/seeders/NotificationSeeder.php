<?php

namespace Database\Seeders;

use App\Enums\DefaultValues;
use App\Enums\Notifications;
use App\Models\NotificationType;
use App\Models\NotificationTypeTranslation;
use Illuminate\Database\Seeder;

class NotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedNotificationTypes();
    }

    private function seedNotificationTypes(): void
    {
        $notificationTypes = [
            [
                'name' => 'account_created',
                'event' => Notifications::NOTIFICATION_EVENT_ACCOUNT_CREATED->value,
                'default_channels' => [
                    Notifications::NOTIFICATION_CHANNEL_MAIL->value,
                    Notifications::NOTIFICATION_CHANNEL_DATABASE->value
                ],
                'placeholders' => ['user_name', 'login_url'],
                'translations' => [
                    'en' => [
                        'description' => 'Notification sent when a new account is created',
                        'template_string' => 'Welcome {user_name}! Your account has been created successfully. You can now login at {login_url}.'
                    ]
                ]
            ],
            [
                'name' => 'account_otp_sent',
                'event' => Notifications::NOTIFICATION_EVENT_ACCOUNT_OTP_SENT->value,
                'default_channels' => [
                    Notifications::NOTIFICATION_CHANNEL_MAIL->value,
                    Notifications::NOTIFICATION_CHANNEL_DATABASE->value
                ],
                'placeholders' => ['user_name', 'otp_code', 'action', 'expiry_minutes'],
                'translations' => [
                    'en' => [
                        'description' => 'Notification sent when OTP verification code is generated',
                        'template_string' => 'Hello {user_name}, your OTP verification code for {action} is: {otp_code}. This code will expire in {expiry_minutes} minutes.'
                    ]
                ]
            ],
            [
                'name' => 'account_password_reset',
                'event' => Notifications::NOTIFICATION_EVENT_ACCOUNT_PASSWORD_RESET->value,
                'default_channels' => [
                    Notifications::NOTIFICATION_CHANNEL_MAIL->value,
                    Notifications::NOTIFICATION_CHANNEL_DATABASE->value
                ],
                'placeholders' => ['user_name', 'otp_code', 'expiry_minutes'],
                'translations' => [
                    'en' => [
                        'description' => 'Notification sent when password reset is requested',
                        'template_string' => 'Hello {user_name}, your password reset verification code is: {otp_code}. This code will expire in {expiry_minutes} minutes.'
                    ]
                ]
            ],
            [
                'name' => 'account_role_changed',
                'event' => Notifications::NOTIFICATION_EVENT_ACCOUNT_ROLE_CHANGED->value,
                'default_channels' => [
                    Notifications::NOTIFICATION_CHANNEL_MAIL->value,
                    Notifications::NOTIFICATION_CHANNEL_DATABASE->value
                ],
                'placeholders' => ['user_name', 'old_role', 'new_role', 'changed_by'],
                'translations' => [
                    'en' => [
                        'description' => 'Notification sent when user role is changed',
                        'template_string' => 'Hello {user_name}, your account role has been changed from {old_role} to {new_role} by {changed_by}.'
                    ]
                ]
            ],
            [
                'name' => 'account_invitation_sent',
                'event' => Notifications::NOTIFICATION_EVENT_ACCOUNT_INVITATION_SENT->value,
                'default_channels' => [
                    Notifications::NOTIFICATION_CHANNEL_MAIL->value,
                    Notifications::NOTIFICATION_CHANNEL_DATABASE->value
                ],
                'placeholders' => ['user_name', 'company_name', 'invitation_url'],
                'translations' => [
                    'en' => [
                        'description' => 'Notification sent when user is invited to join workspace',
                        'template_string' => 'Hello {user_name}, you have been invited to join {company_name} workspace. Click here to accept: {invitation_url}'
                    ]
                ]
            ],
            [
                'name' => 'subscription_activated',
                'event' => Notifications::NOTIFICATION_EVENT_SUBSCRIPTION_ACTIVATED->value,
                'default_channels' => [
                    Notifications::NOTIFICATION_CHANNEL_MAIL->value,
                    Notifications::NOTIFICATION_CHANNEL_DATABASE->value
                ],
                'placeholders' => ['user_name', 'subscription_id', 'plan_name'],
                'translations' => [
                    'en' => [
                        'description' => 'Notification sent when subscription is activated',
                        'template_string' => 'Hello {user_name}, your subscription {subscription_id} for {plan_name} has been activated successfully.'
                    ]
                ]
            ],
            [
                'name' => 'provided_license',
                'event' => Notifications::NOTIFICATION_EVENT_PROVIDED_LICENSE->value,
                'default_channels' => [
                    Notifications::NOTIFICATION_CHANNEL_MAIL->value,
                    Notifications::NOTIFICATION_CHANNEL_DATABASE->value
                ],
                'placeholders' => ['user_name', 'license_key', 'environment', 'subscription_id'],
                'translations' => [
                    'en' => [
                        'description' => 'Notification sent when license key is provided',
                        'template_string' => 'Hello {user_name}, your license key for {environment} environment has been provided. License: {license_key} for subscription {subscription_id}.'
                    ]
                ]
            ],
            [
                'name' => 'assigned_license',
                'event' => Notifications::NOTIFICATION_EVENT_ASSIGNED_LICENSE->value,
                'default_channels' => [
                    Notifications::NOTIFICATION_CHANNEL_MAIL->value,
                    Notifications::NOTIFICATION_CHANNEL_DATABASE->value
                ],
                'placeholders' => ['user_name', 'license_key', 'environment', 'subscription_id'],
                'translations' => [
                    'en' => [
                        'description' => 'Notification sent when license is activated',
                        'template_string' => 'Hello {user_name}, your license for {environment} environment has been activated. License: {license_key} for subscription {subscription_id}.'
                    ]
                ]
            ]
        ];

        foreach ($notificationTypes as $typeData) {
            $translations = $typeData['translations'];
            unset($typeData['translations']);

            $notificationType = NotificationType::updateOrCreate(
                ['name' => $typeData['name']],
                array_merge($typeData, [
                    'created_by' => DefaultValues::SYSTEM_USER_ID->get(),
                    'updated_by' => DefaultValues::SYSTEM_USER_ID->get(),
                ])
            );

            // Create translations
            foreach ($translations as $langCode => $translation) {
                NotificationTypeTranslation::updateOrCreate(
                    [
                        'type_id' => $notificationType->notification_type_id,
                        'language_code' => $langCode
                    ],
                    array_merge($translation, [
                        'created_by' => DefaultValues::SYSTEM_USER_ID->get(),
                        'updated_by' => DefaultValues::SYSTEM_USER_ID->get(),
                    ])
                );
            }
        }
    }

}
