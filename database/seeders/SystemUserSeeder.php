<?php

namespace Database\Seeders;

use App\Enums\DefaultValues;
use App\Models\User;
use Illuminate\Database\Seeder;

class SystemUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::updateOrCreate(
            ['user_id' => 1], 
            [
                'name' => DefaultValues::SYSTEM_USER_ID->name(),
                'email' => '<EMAIL>',
                'password' => bcrypt('password'), 
                'created_by' => DefaultValues::SYSTEM_USER_ID->get(),
                'updated_by' => DefaultValues::SYSTEM_USER_ID->get(),
            ]
        );
    }
}
