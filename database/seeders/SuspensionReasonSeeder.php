<?php

namespace Database\Seeders;

use App\Models\SuspensionReason;
use Illuminate\Database\Seeder;

class SuspensionReasonSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $suspensionReasons = [
            [
                'name' => 'PAYMENT_FAILURE',
                'description' => 'Failed payment transaction.',
            ],
            [
                'name' => 'OVERDUE_INVOICE',
                'description' => 'Customer invoice is overdue.',
            ],
            [
                'name' => 'CREDIT_LIMIT_EXCEEDED',
                'description' => 'Prepaid quota exhausted.',
            ],
            [
                'name' => 'SUBSCRIPTION_NOT_RENEWED',
                'description' => 'Subscription was not renewed.',
            ],
            [
                'name' => 'CUSTOMER_REQUEST',
                'description' => 'Customer requested temporary suspension.',
            ],
            [
                'name' => 'SERVICE_NOT_NEEDED',
                'description' => 'Customer no longer needs the service but wants to keep the account.',
            ],
            [
                'name' => 'CONTRACT_TERMINATION_PENDING',
                'description' => 'Contract is ending soon, awaiting finalization.',
            ],
            [
                'name' => 'SEASONAL_PAUSE',
                'description' => 'Customer is pausing services for a seasonal reason.',
            ],
            [
                'name' => 'SECURITY_VIOLATION',
                'description' => 'Customer triggered a security alert.',
            ],
            [
                'name' => 'FRAUD_SUSPECTED',
                'description' => 'Fraudulent activities detected.',
            ],
            [
                'name' => 'TERMS_OF_SERVICE_VIOLATION',
                'description' => 'Breach of terms of service agreement.',
            ],
            [
                'name' => 'SERVICE_OUTAGE',
                'description' => 'Temporary downtime or issue in the service.',
            ],
            [
                'name' => 'SYSTEM_MIGRATION',
                'description' => 'Service paused due to cloud or infrastructure migration.',
            ],
            [
                'name' => 'INFRASTRUCTURE_MAINTENANCE',
                'description' => 'Maintenance is ongoing on the system.',
            ],
            [
                'name' => 'DATA_RESIDENCY_VIOLATION',
                'description' => 'Violation of local data residency laws.',
            ],
            [
                'name' => 'ACCOUNT_OWNERSHIP_CHANGE',
                'description' => 'Change in account ownership or organization structure.',
            ],
            [
                'name' => 'MULTIPLE_FAILED_LOGIN_ATTEMPTS',
                'description' => 'Account locked due to repeated login failures.',
            ],
            [
                'name' => 'UNAUTHORIZED_API_ACCESS',
                'description' => 'API access attempt was unauthorized.',
            ],
            [
                'name' => 'CUSTOMER_NON_RESPONSIVE',
                'description' => 'Customer did not respond to required actions.',
            ],
        ];

        foreach ($suspensionReasons as $suspensionReason) {
            SuspensionReason::updateOrCreate(
                ['name' => $suspensionReason['name']],
                $suspensionReason
            );
        }
    }
}
