<?php

namespace Database\Seeders;

use App\Models\PlatformBanks;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class PlatformBankSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Schema::disableForeignKeyConstraints();

        PlatformBanks::truncate();

        Schema::enableForeignKeyConstraints();

        PlatformBanks::insert(
            [
                // KSA
                [
                    'bank_name' => 'SAB Bank',
                    'account_number' => '8531 0563 3001',
                    'swift_code' => 'SABBSARIXXX',
                    'bank_address' => 'Jeddah , Saudi Arabia',
                    'iban' => 'SA30 4500 0000 8531 0563 3001',
                    'country_code' => 'SA',
                ],
                [
                    'bank_name' => 'SNB',
                    'account_number' => '**************',
                    'swift_code' => 'NCBKSAJE', 
                    'bank_address' => 'Jeddah , Saudi Arabia',
                    'iban' => 'SA31100000**************',
                    'country_code' => 'SA',
                ],
                [
                    'bank_name' => 'Inmaa',
                    'account_number' => '**************',
                    'swift_code' => 'INMASARIXXX',
                    'bank_address' => 'Jeddah , Saudi Arabia',
                    'iban' => 'SA50050000**************',
                    'country_code' => 'SA',
                ],
                [
                    'bank_name' => 'Inmaa',
                    'account_number' => '**************',
                    'swift_code' => 'INMASARIXXX',
                    'bank_address' => 'Jeddah , Saudi Arabia',
                    'iban' => 'SA23050000**************',
                    'country_code' => 'SA',
                ],
                [
                    'bank_name' => 'Inmaa',
                    'account_number' => '**************',
                    'swift_code' => 'INMASARIXXX',
                    'bank_address' => 'Jeddah , Saudi Arabia',
                    'iban' => 'SA93050000**************',
                    'country_code' => 'SA',
                ],
                [
                    'bank_name' => 'Inmaa',
                    'account_number' => '**************',
                    'swift_code' => 'INMASARIXXX',
                    'bank_address' => 'Jeddah , Saudi Arabia',
                    'iban' => 'SA66050000**************',
                    'country_code' => 'SA',
                ],
                [
                    'bank_name' => 'Inmaa',
                    'account_number' => '**************',
                    'swift_code' => 'INMASARIXXX',
                    'bank_address' => 'Jeddah , Saudi Arabia',
                    'iban' => 'SA39050000**************',
                    'country_code' => 'SA',
                ],
    
    
                // UAE
                [
                    'bank_name' => 'Emirates NBD',
                    'account_number' => '0515 3309 23502',
                    'swift_code' => 'EBILAEAD',
                    'bank_address' => 'Dubai , United Arab Emirates',
                    'iban' => 'AE65 0260 0005 1533 0923 502',
                    'country_code' => 'AE',
                ],
                [
                    'bank_name' => 'Emirates NBD',
                    'account_number' => '0515 3309 23501',
                    'swift_code' => 'EBILAEAD',
                    'bank_address' => 'Dubai , United Arab Emirates',
                    'iban' => 'AE92 0260 0005 1533 0923 501',
                    'country_code' => 'AE',
                ],

                // JORDAN
                [
                    'bank_name' => 'Bank Al Etihad',
                    'account_number' => '0010 1273 2071 6301',
                    'swift_code' => 'UBSIJOAXXXX',
                    'bank_address' => 'Amman , Jordan',
                    'iban' => 'JO46 UBSI 1010 0000 1012 7320 7163 01',
                    'country_code' => 'JO',
                ],
                [
                    'bank_name' => 'Bank Al Etihad',
                    'account_number' => '0010 2273 2071 5101',
                    'swift_code' => 'UBSIJOAXXXX',
                    'bank_address' => 'Amman , Jordan',
                    'iban' => 'JO10 UBSI 1010 0000 1022 7320 7151 01',
                    'country_code' => 'JO',
                ],

                // QATAR
                [
                    'bank_name' => 'QNB',
                    'account_number' => '0260-068863-001',
                    'swift_code' => 'QNBAQAQASLB',
                    'bank_address' => 'Qatar',
                    'iban' => 'QA36 QNBA 0000 0000 0260 0688 6300 1',
                    'country_code' => 'QA',
                ],
                [
                    'bank_name' => 'QNB',
                    'account_number' => '0260-068863-052',
                    'swift_code' => 'QNBAQAQA',
                    'bank_address' => 'Qatar',
                    'iban' => 'QA17 QNBA 0000 0000 0260 0688 6305 2',
                    'country_code' => 'QA',
                ],

                // EGYPT
                [
                    'bank_name' => 'Bank Misr',
                    'account_number' => '52 20001 0000 1061 4',
                    'swift_code' => 'BMISEGCXXXX',
                    'bank_address' => 'Misr, ministry of investement, salah salem street, Cairo',
                    'iban' => 'EG10 0002 0522 0522 00010 0001 0614',
                    'country_code' => 'EG',
                ],
                [
                    'bank_name' => 'Bank Misr',
                    'account_number' => '52 2012 0000 0025 72',
                    'swift_code' => 'BMISEGCXXXX',
                    'bank_address' => 'Misr, ministry of investement, salah salem street, Cairo',
                    'iban' => 'EG38 0002 0522 052 2012 0000 0025 72',
                    'country_code' => 'EG',
                ],
            ]
        );
    }
}
