<?php

namespace Database\Seeders;

use App\Helpers\StatusHelper;
use App\Models\Status;
use Illuminate\Database\Seeder;

class StatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $statuses = [
            StatusHelper::STATUS_TYPE_CUSTOMER => [
                [
                    'name' => 'pending',
                    'color' => '#FFB700',
                ],
                [
                    'name' => 'incomplete',
                    'color' => '#EA5E20',
                ],
                [
                    'name' => 'active',
                    'color' => '#009D0D',
                ],
                [
                    'name' => 'closed',
                    'color' => '#C00000',
                ],
                [
                    'name' => 'suspended',
                    'color' => '#C00000',
                ],
                [
                    'name' => 'terminated',
                    'color' => '#C00000',
                ],
            ],
            StatusHelper::STATUS_TYPE_USER => [
                [
                    'name' => 'pending',
                    'color' => '#FFB700',
                ],
                [
                    'name' => 'accepted',
                    'color' => '#009D0D',
                ],
                [
                    'name' => 'incomplete',
                    'color' => '#EA5E20',
                ],
                [
                    'name' => 'active',
                    'color' => '#009D0D',
                ],
                [
                    'name' => 'suspended',
                    'color' => '#C00000',
                ],
                [
                    'name' => 'deactivated',
                    'color' => '#C00000',
                ],
            ],
          
        ];

        collect($statuses)->each(function ($statusArray, $type) {
            collect($statusArray)->each(function ($status) use ($type) {
                Status::updateOrCreate(
                    [
                        'name' => $status['name'],
                        'type' => $type,
                    ],
                    [
                        'color' => $status['color'],
                        'type' => $type,
                    ]
                );
            });
        });
    }
}
