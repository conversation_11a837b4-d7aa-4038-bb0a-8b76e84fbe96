<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('subscription_licenses', function (Blueprint $table) {
            $table->string('sonar_url')->unique()->nullable()->after('environment')->comment('Base URL of the SonarQube instance');
            $table->string('sonar_username')->after('sonar_url')->nullable()->comment('Initial admin username, primarily for token generation');
            $table->string('sonar_password')->after('sonar_username')->nullable()->comment('Initial admin password, primarily for token generation');
            $table->text('sonar_api_token')->after('sonar_username')->nullable();
            $table->enum('environment_status', ['pending', 'active', 'failed'])->after('sonar_api_token')->nullable()->comment('Status of the environment');
        });

        Schema::table('subscription_license_history', function (Blueprint $table) {
            $table->string('sonar_url')->unique()->nullable()->after('environment')->comment('Base URL of the SonarQube instance');
            $table->string('sonar_username')->after('sonar_url')->nullable()->comment('Initial admin username, primarily for token generation');
            $table->string('sonar_password')->after('sonar_username')->nullable()->comment('Initial admin password, primarily for token generation');
            $table->text('sonar_api_token')->after('sonar_username')->nullable();
            $table->enum('environment_status', ['pending', 'active', 'failed'])->after('sonar_api_token')->nullable()->comment('Status of the environment');
        });
    }

    public function down(): void
    {
        Schema::table('subscription_licenses', function (Blueprint $table) {
            $table->dropColumn(['sonar_url', 'sonar_username', 'sonar_password', 'sonar_api_token', 'environment_status']);
        });

        Schema::table('subscription_license_history', function (Blueprint $table) {
            $table->dropColumn(['sonar_url', 'sonar_username', 'sonar_password', 'sonar_api_token', 'environment_status']);
        });
    }
};
