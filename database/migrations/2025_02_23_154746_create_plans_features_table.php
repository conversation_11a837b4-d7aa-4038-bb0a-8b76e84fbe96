<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('plan_features', function (Blueprint $table): void {
            $table->id('plan_feature_id');

            $table->foreignId('plan_id')->constrained(config('laravel-subscriptions.tables.plans'))->restrictOnDelete();
            $table->foreignId('feature_id')->nullable()->constrained(config('laravel-subscriptions.tables.features'));

            $table->unsignedBigInteger('valid_from_minimum')->nullable();
            $table->unsignedBigInteger('valid_to_maximum')->nullable();
            $table->boolean('is_addon')->default(false);
            
            $table->decimal('price', 8, 2)->default(0.00);
            $table->unsignedInteger('sort_order')->default(0);

            $table->foreignId('parent_plan_feature_id')
                ->nullable()
                ->constrained('plan_features', 'plan_feature_id')
                ->nullOnDelete();

            $table->withAudits();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('plan_features');
    }
};
