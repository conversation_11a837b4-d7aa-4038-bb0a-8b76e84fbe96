<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('billing_vat_mapping', function (Blueprint $table) {
            $table->id('billing_vat_mapping_id')->autoIncrement();
            $table->string('customer_billing_country_code', 255)->nullable()->default(null)->comment('customer fr country');
            $table->string('platform_billing_country_code', 255)->comment('customer mapped country');
            $table->decimal('vat', 2, 2);
            $table->unsignedTinyInteger('is_default')->nullable()->default(0);
            $table->date('from_date')->nullable()->default(null);
            $table->date('to_date')->nullable()->default(null);

            $table->withAudits();

            $table->index('customer_billing_country_code');
            $table->index('platform_billing_country_code');

            $table->foreign('customer_billing_country_code')->references('code')->on('countries')->onDelete('restrict')->onUpdate('restrict');
            $table->foreign('platform_billing_country_code')->references('code')->on('countries')->onDelete('restrict')->onUpdate('restrict');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('billing_vat_mapping');
    }
};
