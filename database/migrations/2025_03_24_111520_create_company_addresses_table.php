<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_addresses', function (Blueprint $table) {
            $table->id('company_address_id')->autoIncrement();
            $table->foreignId('company_id')->constrained('companies', 'company_id')->restrictOnDelete()->restrictOnUpdate();
            $table->foreignId('country_id')->constrained('countries', 'id')->restrictOnDelete()->restrictOnUpdate();
            $table->foreignId('platform_bank_id')->nullable()->constrained('platform_banks', 'platform_bank_id')->restrictOnDelete()->restrictOnUpdate();
            $table->string('tax_id', 255)->nullable()->default(null);
            $table->string('commercial_registration_number', 255)->nullable()->default(null);
            $table->string('state', 255)->nullable()->default(null);
            $table->string('city', 255)->nullable()->default(null);
            $table->string('zip', 255)->nullable()->default(null);
            $table->string('address_1', 255)->nullable()->default(null);
            $table->char('currency_code', 255)->nullable()->default(null);

            $table->enum('address_type', ['company_address', 'billing_address'])->default('company_address');
            
            $table->withAudits();

            $table->index('currency_code');
            $table->index('company_id');

            $table->foreign('currency_code')->references('iso')->on('currencies')->onDelete('restrict')->onUpdate('restrict');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_addresses');
    }
};
