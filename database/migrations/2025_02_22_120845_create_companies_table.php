<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->id('company_id');
            $table->string('public_company_id')->default('');
            $table->string('name')->nullable();
            $table->foreignId('status_id')->nullable()->constrained('statuses', 'status_id')->nullOnDelete();
            $table->string('timezone')->nullable();
            $table->enum('registration_type', ['self_registered', 'invited_by_operator'])->default('invited_by_operator');
            $table->timestamp('last_activity_at')->nullable();
            
            $table->withAudits();
        });

        // Create pivot tables for many-to-many relationships
        Schema::create('company_countries', function (Blueprint $table) {
            $table->id('company_country_id');
            $table->foreignId('company_id')->constrained('companies', 'company_id')->onDelete('cascade');
            $table->foreignId('country_id')->constrained('countries')->onDelete('cascade');
            
            $table->withAudits();

            $table->unique(['company_id', 'country_id']);
        });

        Schema::create('company_sectors', function (Blueprint $table) {
            $table->id('company_sector_id');
            $table->foreignId('company_id')->constrained('companies', 'company_id')->onDelete('cascade');
            $table->foreignId('sector_id')->constrained('sectors', 'sector_id')->onDelete('cascade');
            
            $table->withAudits();

            $table->unique(['company_id', 'sector_id']);
        });

        Schema::create('company_industries', function (Blueprint $table) {
            $table->id('company_industry_id');
            $table->foreignId('company_id')->constrained('companies', 'company_id')->onDelete('cascade');
            $table->foreignId('industry_id')->constrained('industries', 'industry_id')->onDelete('cascade');
            
            $table->withAudits();

            $table->unique(['company_id', 'industry_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('company_industries');
        Schema::dropIfExists('company_sectors');
        Schema::dropIfExists('company_countries');
        Schema::dropIfExists('companies');
    }
};
