<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB; // Import DB facade

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        DB::table('subscription_licenses')->truncate();
        DB::table('subscription_license_history')->truncate();

        DB::statement("ALTER TABLE subscription_licenses CHANGE COLUMN environment environment ENUM('production', 'disaster_recovery', 'uat', 'test') NULL");

        DB::statement("ALTER TABLE subscription_license_history CHANGE COLUMN environment environment ENUM('production', 'disaster_recovery', 'uat', 'test') NULL");

        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }

    public function down(): void
    {
        //
    }
};