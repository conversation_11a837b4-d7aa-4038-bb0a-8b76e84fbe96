<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->foreignId('company_id')->after('user_id')->nullable()->constrained('companies', 'company_id')->nullOnDelete();
            $table->foreignId('status_id')->after('company_id')->nullable()->constrained('statuses', 'status_id')->nullOnDelete();
        });
    }

    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['company_id']);
            $table->dropForeign(['status_id']);
    
            $table->dropColumn([
                'company_id',
                'status_id',
            ]);
        });
    }
};
