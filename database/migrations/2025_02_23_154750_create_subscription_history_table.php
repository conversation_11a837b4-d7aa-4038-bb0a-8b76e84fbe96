<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('subscription_history', function (Blueprint $table): void {
            $table->id('subscription_history_id');

            $table->foreignId('subscription_id')->constrained(config('laravel-subscriptions.tables.subscriptions'));
            $table->string('public_subscription_id', 255)->default('');

            $table->foreignId('plan_id')->nullable()->constrained(config('laravel-subscriptions.tables.plans'))->restrictOnDelete()->restrictOnUpdate();
            $table->foreignId('country_id')->constrained('countries');
            $table->foreignId('currency_id')->nullable()->constrained('currencies');
            $table->foreignId('suspension_reason_id')->nullable()->constrained('suspension_reasons', 'suspension_reasons_id');
            $table->foreignId('billing_country_id')->nullable()->constrained('countries');

            $table->morphs('subscriber');
            $table->json('name');
            $table->string('slug');
            $table->json('description')->nullable();
            $table->string('timezone')->nullable();
            $table->string('erp_sales_order_id', 10)->nullable();

            $table->dateTime('trial_ends_at')->nullable();
            $table->dateTime('starts_at')->nullable();
            $table->dateTime('ends_at')->nullable();
            $table->dateTime('cancels_at')->nullable();
            $table->dateTime('canceled_at')->nullable();
            $table->dateTime('last_payment_date')->nullable();

            $table->enum('subscription_status', ['pending_payment', 'pending_license_key', 'pending_receipt', 'active', 'expired', 'suspended', 'terminated', 'incomplete'])->default('pending_payment');
            $table->enum('payment_status', ['pending', 'in_review', 'failed', 'paid', 'reversed'])->default('pending');
            $table->tinyInteger('auto_renew')->default(true);

            $table->enum('history_event', ['renewal', 'suspension', 'termination', 'assigned_quota_change', 'remaining_quota_change', 'payment_update', 'license_key', 'activation', 'auto_renew_setting_change', 'extension'])->nullable();
            $table->text('rejection_reason_description')->nullable();

            $table->unsignedBigInteger('assigned_quota')->nullable();
            $table->unsignedBigInteger('used_quota')->nullable();
            $table->unsignedBigInteger('remaining_quota')->nullable();

            $table->withAudits();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('subscription_history');
    }
};
