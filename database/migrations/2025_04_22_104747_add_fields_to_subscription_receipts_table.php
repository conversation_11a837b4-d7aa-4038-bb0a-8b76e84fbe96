<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscription_receipts', function (Blueprint $table) {
            $table->string('receipt_path')->nullable()->after('receipt_number');
            $table->string('storage')->after('receipt_path');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscription_receipts', function (Blueprint $table) {
            $table->dropColumn(['receipt_path', 'storage']);
        });
    }
};
