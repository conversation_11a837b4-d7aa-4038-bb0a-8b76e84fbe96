<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscription_payments', function (Blueprint $table) {
            $table->string('payment_type')->nullable()->change();
        });

        Schema::table('subscriptions', function (Blueprint $table) {
            $table->boolean('auto_renew')->nullable()->change();
        });

        Schema::table('subscription_history', function (Blueprint $table) {
            $table->boolean('auto_renew')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscription_payments', function (Blueprint $table) {
            $table->string('payment_type')->nullable(false)->change();
        });

        Schema::table('subscriptions', function (Blueprint $table) {
            $table->boolean('auto_renew')->nullable(false)->change();
        });

        Schema::table('subscription_history', function (Blueprint $table) {
            $table->boolean('auto_renew')->nullable(false)->change();
        });
    }
};
