<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('suspension_reasons', function (Blueprint $table): void {
            $table->id('suspension_reasons_id');

            $table->string('slug', 255)->nullable();
            $table->string('name', 255)->nullable();
            $table->string('description', 255)->nullable();

            $table->withAudits();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('suspension_reasons');
    }
};
