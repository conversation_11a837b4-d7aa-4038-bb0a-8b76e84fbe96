<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('company_history', function (Blueprint $table) {
            $table->id('company_history_id');

            // Reference to the original company
            $table->foreignId('company_id')->constrained('companies', 'company_id')->onDelete('cascade');
            
            // Action tracking
            $table->enum('action', ['created', 'updated', 'deleted', 'restored'])->index();
            $table->string('change_type')->nullable()->index(); // 'direct', 'countries', 'sectors', 'industries', 'addresses', 'users'
            
            // User who performed the action
            $table->foreignId('performed_by')->nullable()->constrained('users', 'user_id')->nullOnDelete();
            
            // Company data snapshot (all fields from companies table)
            $table->string('public_company_id')->default('');
            $table->string('name')->nullable();
            $table->foreignId('status_id')->nullable();
            $table->string('timezone')->nullable();
            $table->string('website')->nullable();
            $table->integer('number_of_employees')->nullable();
            $table->enum('registration_type', ['self_registered', 'invited_by_operator'])->default('invited_by_operator');
            $table->timestamp('invitation_expires_at')->nullable();
            $table->timestamp('last_activity_at')->nullable();
            
            // Original timestamps from company record
            $table->timestamp('original_created_at')->nullable();
            $table->timestamp('original_updated_at')->nullable();
            $table->timestamp('original_deleted_at')->nullable();
            $table->unsignedBigInteger('original_created_by')->nullable();
            $table->unsignedBigInteger('original_updated_by')->nullable();
            $table->unsignedBigInteger('original_deleted_by')->nullable();
            
            // Change tracking for direct company fields
            $table->json('changed_fields')->nullable(); // Array of field names that changed
            $table->json('old_values')->nullable(); // Old values for changed fields
            $table->json('new_values')->nullable(); // New values for changed fields

            // Pivot table tracking
            $table->json('old_country_ids')->nullable(); // Previous country IDs
            $table->json('new_country_ids')->nullable(); // New country IDs
            $table->json('old_sector_ids')->nullable(); // Previous sector IDs
            $table->json('new_sector_ids')->nullable(); // New sector IDs
            $table->json('old_industry_ids')->nullable(); // Previous industry IDs
            $table->json('new_industry_ids')->nullable(); // New industry IDs
            
            // Additional context
            $table->text('notes')->nullable(); // Optional notes about the change
            $table->json('request_data')->nullable(); // Request context
            $table->string('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            
            $table->timestamps();
            
            // Indexes for better performance
            $table->index(['company_id', 'action']);
            $table->index(['company_id', 'created_at']);
            $table->index(['performed_by', 'created_at']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('company_history');
    }
};
