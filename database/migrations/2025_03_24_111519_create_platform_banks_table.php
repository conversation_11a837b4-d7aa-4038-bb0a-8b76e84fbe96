<?php

use App\Helpers\MigrationHelper;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('platform_banks', function (Blueprint $table) {
            $table->id('platform_bank_id');
            $table->string('bank_name')->nullable();
            $table->string('account_number')->nullable();
            $table->string('swift_code')->nullable();
            $table->string('bank_address', 512)->nullable();
            $table->string('iban')->nullable();
            $table->string('country_code')->nullable();
            
            $table->withAudits();

            $table->index('country_code', 'country_code');

            $table->foreign('country_code')->references('code')->on('countries')->restrictOnDelete()->restrictOnUpdate();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('platform_banks');
    }
};
