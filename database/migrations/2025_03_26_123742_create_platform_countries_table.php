<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('platform_countries', function (Blueprint $table) {
            $table->id('platform_country_id');
            $table->string('name')->unique();
            $table->string('code')->unique();
            $table->string('phone');
            $table->string('lat')->nullable();
            $table->string('lang')->nullable();
            
            $table->withAudits();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('platform_countries');
    }
};
