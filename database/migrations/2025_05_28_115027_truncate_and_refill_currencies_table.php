<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema; // Kept for consistency, though not directly used for schema changes here

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     * @throws \Throwable
     */
    public function up()
    {
        DB::beginTransaction();

        $currenciesSqlPath = database_path('data/currencies.sql');

        try {
            if (!File::exists($currenciesSqlPath)) {
                DB::rollBack();
            }

            DB::statement('SET FOREIGN_KEY_CHECKS=0;');

            DB::table('currencies')->truncate();

            DB::statement('SET FOREIGN_KEY_CHECKS=1;');

            $currencies = File::get($currenciesSqlPath);
            DB::connection()->getPdo()->exec($currencies);

            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
};
