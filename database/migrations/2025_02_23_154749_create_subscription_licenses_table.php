<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('subscription_licenses', function (Blueprint $table): void {
            $table->id('subscription_license_id');

            $table->foreignId('subscription_id')->constrained(config('laravel-subscriptions.tables.subscriptions'));
            $table->foreignId('company_id')->constrained('companies', 'company_id');

            $table->string('license_key', 255)->nullable();
            $table->string('server_id', 255)->nullable();

            $table->enum('license_type', ['evaluation','commercial'])->nullable();
            $table->enum('enviroment', ['test','production','disaster_recovery','development'])->nullable();

            $table->dateTime('request_date')->nullable();
            $table->dateTime('issue_date')->nullable();
            $table->dateTime('first_use_date')->nullable();

            $table->withAudits();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('subscription_licenses');
    }
};
