<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table(config('laravel-subscriptions.tables.subscriptions'), function (Blueprint $table) {
            $table->integer('number_of_years')->default(1)->after('ends_at');
        });

        Schema::table('subscription_history', function (Blueprint $table) {
            $table->integer('number_of_years')->default(1)->after('ends_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table(config('laravel-subscriptions.tables.subscriptions'), function (Blueprint $table) {
            $table->dropColumn('number_of_years');
        });

        Schema::table('subscription_history', function (Blueprint $table) {
            $table->dropColumn('number_of_years');
        });
    }
};
