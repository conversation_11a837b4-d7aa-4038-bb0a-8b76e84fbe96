<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('cities', function (Blueprint $table) {
            $table->json('translations')->nullable();
            $table->dateTime('timezone')->nullable();
            $table->string('lng')->after('lat')->nullable();
            $table->boolean('is_activated')->default(1);
        });

        $cities = \Illuminate\Support\Facades\File::get(database_path('data/cities.sql'));
        DB::connection()->getPdo()->exec($cities);

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('cities', function (Blueprint $table) {
            $table->dropColumn(['translations', 'timezone', 'lng', 'is_activated']);
        });
    }
};