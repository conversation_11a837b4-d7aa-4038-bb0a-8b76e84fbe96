<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('logs', function (Blueprint $table) {
            $table->id('log_id');
            $table->enum('level', ['info','warning','error']);
            $table->string('event');
            $table->text('description'); 
            $table->string('reason')->nullable(); 
            $table->longText('context')->nullable();
            $table->enum('status', ['success', 'failure']);
            $table->string('ip_address')->nullable(); 
            $table->string('user_id')->nullable(); 
            $table->string('email')->nullable();
            $table->longText('device_info')->nullable();
            
            $table->withAudits();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('logs');
    }
};
