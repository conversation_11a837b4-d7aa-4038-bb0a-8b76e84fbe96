<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('subscription_licenses', function (Blueprint $table): void {
            $table->renameColumn('enviroment', 'environment');
        });
    }

    public function down(): void
    {
        Schema::table('subscription_licenses', function (Blueprint $table): void {
            // Revert the change if needed
            $table->renameColumn('environment', 'enviroment');
        });
    }
};