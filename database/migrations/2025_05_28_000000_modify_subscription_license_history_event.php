<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE subscription_license_history MODIFY COLUMN history_event ENUM('created', 'updated', 'revoked', 'renewed', 'server_id_added', 'license_key_provided')");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("ALTER TABLE subscription_license_history MODIFY COLUMN history_event ENUM('created', 'updated', 'revoked', 'renewed')");
    }
};