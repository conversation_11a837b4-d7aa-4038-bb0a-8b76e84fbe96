<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_receipts', function (Blueprint $table) {
            $table->id('subscription_receipts_id');

            $table->foreignId('subscription_id')->constrained(config('laravel-subscriptions.tables.subscriptions'));

            $table->string('receipt_number')->unique();
            $table->decimal('amount', 10, 2);
            $table->string('currency_code', 3);
            $table->enum('receipt_status', ['pending', 'rejected', 'approved'])->default('pending');
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->unsignedBigInteger('rejected_by')->nullable();

            $table->enum('rejection_reason', ['incorrect_amount','rejected_by_bank'])->nullable();

            $table->timestamp('paid_at')->nullable();
            $table->withAudits();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_receipts');
    }
};
