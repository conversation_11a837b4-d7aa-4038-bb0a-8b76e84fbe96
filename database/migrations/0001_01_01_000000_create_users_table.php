<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id('user_id');
            $table->string('public_user_id', 255)->default('');
            $table->string('name', 255);
            $table->string('email')->unique();
            $table->string('phone_number')->nullable();
            $table->string('password')->nullable();
            $table->text('two_factor_secret')->nullable();
            $table->text('two_factor_recovery_codes')->nullable();
            $table->timestamp('two_factor_confirmed_at')->nullable();
            $table->string('otp')->nullable();
            $table->string('timezone')->nullable();
            $table->enum('type', ['admin', 'system_user', 'customer'])->default('customer');
            $table->boolean('is_primary_customer')->default(false);
            $table->boolean('accepted')->default(false);
            $table->rememberToken();
            $table->string('invitation_token')->nullable()->unique();
            $table->dateTime('invitation_expires_at')->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->timestamp('last_activity_at')->nullable();
            $table->integer('login_attempts')->default(0);
            $table->boolean('locked')->default(false);
            $table->dateTime('locked_until')->nullable();
            $table->integer('otp_attempts')->default(0);
            $table->dateTime('otp_expires_at')->nullable();
            $table->dateTime('otp_locked_until')->nullable();
           
            $table->withAudits();
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
