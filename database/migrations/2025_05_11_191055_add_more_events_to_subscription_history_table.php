<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscription_history', function (Blueprint $table) {
            DB::statement("ALTER TABLE `subscription_history` MODIFY `history_event` ENUM('new', 'renewal', 'suspension', 'termination', 'assigned_quota_change', 'remaining_quota_change', 'payment_update', 'payment_approved', 'payment_rejected', 'license_key', 'activation', 'auto_renew_setting_change', 'extension')");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscription_history', function (Blueprint $table) {
            DB::statement("ALTER TABLE `subscription_history` MODIFY `history_event` ENUM('new', 'renewal', 'suspension', 'termination', 'assigned_quota_change', 'remaining_quota_change', 'payment_update', 'license_key', 'activation', 'auto_renew_setting_change', 'extension')");
        });
    }
};
