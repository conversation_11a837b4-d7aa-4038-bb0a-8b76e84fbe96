<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('sectors', function (Blueprint $table) {
            $table->id('sector_id');
            $table->string('name')->unique();
            
            $table->withAudits();
        });

        Schema::create('industries', function (Blueprint $table) {
            $table->id('industry_id');
            $table->string('name')->unique();
            
            $table->withAudits();
        });
    }

    public function down()
    {
        Schema::dropIfExists('industries');
        Schema::dropIfExists('sectors');
    }
};
