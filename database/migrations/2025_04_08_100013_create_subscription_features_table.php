<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_features', function (Blueprint $table) {
            $table->id('subscription_features_id');

            $table->foreignId('subscription_id')->nullable()->constrained(config('laravel-subscriptions.tables.subscription'));
            $table->foreignId('feature_id')->nullable()->constrained(config('laravel-subscriptions.tables.features'));

            $table->boolean('value')->default(false);

            $table->withAudits();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_features');
    }
};
