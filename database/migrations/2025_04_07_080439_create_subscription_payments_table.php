<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_payments', function (Blueprint $table) {
            $table->id('subscription_payments_id');
            $table->foreignId('subscription_id')->constrained('subscriptions', 'id')->restrictOnDelete()->restrictOnUpdate();

            $table->enum('payment_type', ['wire_transfer', 'online_payment'])->default('wire_transfer');
            $table->string('payment_gateway', 255)->nullable()->default(null);

            $table->withAudits();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_payments');
    }
};
