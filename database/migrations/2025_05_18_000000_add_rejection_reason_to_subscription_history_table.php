<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscription_history', function (Blueprint $table) {
            $table->enum('rejection_reason', [
                'INCORRECT_AMOUNT',
                'INVALID_RECEIPT',
                'REJECTED_BY_BANK',
                'PAYMENT_NOT_RECEIVED',
                'DUPLICATE_PAYMENT',
                'INCORRECT_CURRENCY',
                'INCOMPLETE_INFORMATION',
                'PAYMENT_METHOD_NOT_ACCEPTED',
                'OTHER'
            ])->nullable()->after('rejection_reason_description');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscription_history', function (Blueprint $table) {
            $table->dropColumn('rejection_reason');
        });
    }
};