<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_progress', function (Blueprint $table) {
            $table->id('subscription_progress_id');

            $table->foreignId('subscription_id')->constrained(config('laravel-subscriptions.tables.subscriptions'));
            $table->enum('step_name', ['selected_plan', 'provided_address', 'entered_payment_details', 'confirmed_order'])->default('selected_plan');
            $table->json('step_data');
            $table->timestamp('completed_at');
            $table->withAudits();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_progress');
    }
};
