
INSERT INTO `countries` (`id`, `name`, `code`, `phone`, `created_at`, `updated_at`, `translations`, `timezones`, `numeric_code`, `iso3`, `nationality`, `capital`, `tld`, `native`, `region`, `currency`, `currency_name`, `currency_symbol`, `wikiDataId`, `lat`, `lng`, `emoji`, `emojiU`, `flag`, `is_activated`, `sort_order`) VALUES
(1, 'Aland Islands', 'AX', '248', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Aland Islands\", \"cn\": \"奥兰群岛\", \"de\": \"Åland\", \"es\": \"Alandia\", \"fa\": \"جزایر الند\", \"fr\": \"Åland\", \"hr\": \"Ålandski otoci\", \"it\": \"Isole Aland\", \"ja\": \"オーランド諸島\", \"kr\": \"올란드 제도\", \"nl\": \"Ålandeilanden\", \"pt\": \"Ilhas de Aland\", \"tr\": \"Åland Adalari\", \"pt-BR\": \"Ilhas de Aland\"}', '[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Mariehamn\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]', '248', 'ALA', 'Aland Island', 'Mariehamn', '.ax', 'Åland', 'Europe', 'EUR', 'Euro', '€', NULL, 60.12, 19.90, '🇦🇽', 'U+1F1E6 U+1F1FD', 1, 1, 0),
(2, 'Albania', 'AL', '008', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Albania\", \"cn\": \"阿尔巴尼亚\", \"de\": \"Albanien\", \"es\": \"Albania\", \"fa\": \"آلبانی\", \"fr\": \"Albanie\", \"hr\": \"Albanija\", \"it\": \"Albania\", \"ja\": \"アルバニア\", \"kr\": \"알바니아\", \"nl\": \"Albanië\", \"pt\": \"Albânia\", \"tr\": \"Arnavutluk\", \"pt-BR\": \"Albânia\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Tirane\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '008', 'ALB', 'Albanian ', 'Tirana', '.al', 'Shqipëria', 'Europe', 'ALL', 'Albanian lek', 'Lek', NULL, 41.00, 20.00, '🇦🇱', 'U+1F1E6 U+1F1F1', 1, 1, 0),
(3, 'Algeria', 'DZ', '012', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Algeria\", \"cn\": \"阿尔及利亚\", \"de\": \"Algerien\", \"es\": \"Argelia\", \"fa\": \"الجزایر\", \"fr\": \"Algérie\", \"hr\": \"Alžir\", \"it\": \"Algeria\", \"ja\": \"アルジェリア\", \"kr\": \"알제리\", \"nl\": \"Algerije\", \"pt\": \"Argélia\", \"tr\": \"Cezayir\", \"pt-BR\": \"Argélia\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Africa/Algiers\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '012', 'DZA', 'Algerian', 'Algiers', '.dz', 'الجزائر', 'Africa', 'DZD', 'Algerian dinar', 'دج', NULL, 28.00, 3.00, '🇩🇿', 'U+1F1E9 U+1F1FF', 1, 1, 0),
(4, 'American Samoa', 'AS', '016', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"American Samoa\", \"cn\": \"美属萨摩亚\", \"de\": \"Amerikanisch-Samoa\", \"es\": \"Samoa Americana\", \"fa\": \"ساموآی آمریکا\", \"fr\": \"Samoa américaines\", \"hr\": \"Američka Samoa\", \"it\": \"Samoa Americane\", \"ja\": \"アメリカ領サモア\", \"kr\": \"아메리칸사모아\", \"nl\": \"Amerikaans Samoa\", \"pt\": \"Samoa Americana\", \"tr\": \"Amerikan Samoasi\", \"pt-BR\": \"Samoa Americana\"}', '[{\"tzName\": \"Samoa Standard Time\", \"zoneName\": \"Pacific/Pago_Pago\", \"gmtOffset\": -39600, \"abbreviation\": \"SST\", \"gmtOffsetName\": \"UTC-11:00\"}]', '016', 'ASM', 'American Samoan', 'Pago Pago', '.as', 'American Samoa', 'Oceania', 'USD', 'US Dollar', '$', NULL, -14.33, -170.00, '🇦🇸', 'U+1F1E6 U+1F1F8', 1, 1, 0),
(5, 'Andorra', 'AD', '020', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Andorra\", \"cn\": \"安道尔\", \"de\": \"Andorra\", \"es\": \"Andorra\", \"fa\": \"آندورا\", \"fr\": \"Andorre\", \"hr\": \"Andora\", \"it\": \"Andorra\", \"ja\": \"アンドラ\", \"kr\": \"안도라\", \"nl\": \"Andorra\", \"pt\": \"Andorra\", \"tr\": \"Andorra\", \"pt-BR\": \"Andorra\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Andorra\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '020', 'AND', 'Andorran', 'Andorra la Vella', '.ad', 'Andorra', 'Europe', 'EUR', 'Euro', '€', NULL, 42.50, 1.50, '🇦🇩', 'U+1F1E6 U+1F1E9', 1, 1, 0),
(6, 'Angola', 'AO', '024', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Angola\", \"cn\": \"安哥拉\", \"de\": \"Angola\", \"es\": \"Angola\", \"fa\": \"آنگولا\", \"fr\": \"Angola\", \"hr\": \"Angola\", \"it\": \"Angola\", \"ja\": \"アンゴラ\", \"kr\": \"앙골라\", \"nl\": \"Angola\", \"pt\": \"Angola\", \"tr\": \"Angola\", \"pt-BR\": \"Angola\"}', '[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Luanda\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}]', '024', 'AGO', 'Angolan', 'Luanda', '.ao', 'Angola', 'Africa', 'AOA', 'Angolan kwanza', 'Kz', NULL, -12.50, 18.50, '🇦🇴', 'U+1F1E6 U+1F1F4', 1, 1, 0),
(7, 'Anguilla', 'AI', '660', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Anguilla\", \"cn\": \"安圭拉\", \"de\": \"Anguilla\", \"es\": \"Anguilla\", \"fa\": \"آنگویلا\", \"fr\": \"Anguilla\", \"hr\": \"Angvila\", \"it\": \"Anguilla\", \"ja\": \"アンギラ\", \"kr\": \"앵귈라\", \"nl\": \"Anguilla\", \"pt\": \"Anguila\", \"tr\": \"Anguilla\", \"pt-BR\": \"Anguila\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Anguilla\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '660', 'AIA', 'Anguillan', 'The Valley', '.ai', 'Anguilla', 'Americas', 'XCD', 'East Caribbean dollar', '$', NULL, 18.25, -63.17, '🇦🇮', 'U+1F1E6 U+1F1EE', 1, 1, 0),
(8, 'Antarctica', 'AQ', '010', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Antarctica\", \"cn\": \"南极洲\", \"de\": \"Antarktika\", \"es\": \"Antártida\", \"fa\": \"جنوبگان\", \"fr\": \"Antarctique\", \"hr\": \"Antarktika\", \"it\": \"Antartide\", \"ja\": \"南極大陸\", \"kr\": \"남극\", \"nl\": \"Antarctica\", \"pt\": \"Antárctida\", \"tr\": \"Antartika\", \"pt-BR\": \"Antártida\"}', '[{\"tzName\": \"Australian Western Standard Time\", \"zoneName\": \"Antarctica/Casey\", \"gmtOffset\": 39600, \"abbreviation\": \"AWST\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Davis Time\", \"zoneName\": \"Antarctica/Davis\", \"gmtOffset\": 25200, \"abbreviation\": \"DAVT\", \"gmtOffsetName\": \"UTC+07:00\"}, {\"tzName\": \"Dumont dUrville Time\", \"zoneName\": \"Antarctica/DumontDUrville\", \"gmtOffset\": 36000, \"abbreviation\": \"DDUT\", \"gmtOffsetName\": \"UTC+10:00\"}, {\"tzName\": \"Mawson Station Time\", \"zoneName\": \"Antarctica/Mawson\", \"gmtOffset\": 18000, \"abbreviation\": \"MAWT\", \"gmtOffsetName\": \"UTC+05:00\"}, {\"tzName\": \"New Zealand Daylight Time\", \"zoneName\": \"Antarctica/McMurdo\", \"gmtOffset\": 46800, \"abbreviation\": \"NZDT\", \"gmtOffsetName\": \"UTC+13:00\"}, {\"tzName\": \"Chile Summer Time\", \"zoneName\": \"Antarctica/Palmer\", \"gmtOffset\": -10800, \"abbreviation\": \"CLST\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Rothera Research Station Time\", \"zoneName\": \"Antarctica/Rothera\", \"gmtOffset\": -10800, \"abbreviation\": \"ROTT\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Showa Station Time\", \"zoneName\": \"Antarctica/Syowa\", \"gmtOffset\": 10800, \"abbreviation\": \"SYOT\", \"gmtOffsetName\": \"UTC+03:00\"}, {\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Antarctica/Troll\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}, {\"tzName\": \"Vostok Station Time\", \"zoneName\": \"Antarctica/Vostok\", \"gmtOffset\": 21600, \"abbreviation\": \"VOST\", \"gmtOffsetName\": \"UTC+06:00\"}]', '010', 'ATA', 'Antarctic', '', '.aq', 'Antarctica', 'Polar', 'AAD', 'Antarctican dollar', '$', NULL, -74.65, 4.48, '🇦🇶', 'U+1F1E6 U+1F1F6', 1, 1, 0),
(9, 'Antigua And Barbuda', 'AG', '028', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Antigua And Barbuda\", \"cn\": \"安提瓜和巴布达\", \"de\": \"Antigua und Barbuda\", \"es\": \"Antigua y Barbuda\", \"fa\": \"آنتیگوا و باربودا\", \"fr\": \"Antigua-et-Barbuda\", \"hr\": \"Antigva i Barbuda\", \"it\": \"Antigua e Barbuda\", \"ja\": \"アンティグア・バーブーダ\", \"kr\": \"앤티가 바부다\", \"nl\": \"Antigua en Barbuda\", \"pt\": \"Antígua e Barbuda\", \"tr\": \"Antigua Ve Barbuda\", \"pt-BR\": \"Antígua e Barbuda\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Antigua\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '028', 'ATG', 'Antiguan or Barbudan', 'St. Johns', '.ag', 'Antigua and Barbuda', 'Americas', 'XCD', 'Eastern Caribbean dollar', '$', NULL, 17.05, -61.80, '🇦🇬', 'U+1F1E6 U+1F1EC', 1, 1, 0),
(10, 'Argentina', 'AR', '032', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Argentina\", \"cn\": \"阿根廷\", \"de\": \"Argentinien\", \"es\": \"Argentina\", \"fa\": \"آرژانتین\", \"fr\": \"Argentine\", \"hr\": \"Argentina\", \"it\": \"Argentina\", \"ja\": \"アルゼンチン\", \"kr\": \"아르헨티나\", \"nl\": \"Argentinië\", \"pt\": \"Argentina\", \"tr\": \"Arjantin\", \"pt-BR\": \"Argentina\"}', '[{\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/Buenos_Aires\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/Catamarca\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/Cordoba\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/Jujuy\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/La_Rioja\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/Mendoza\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/Rio_Gallegos\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/Salta\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/San_Juan\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/San_Luis\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/Tucuman\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/Ushuaia\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}]', '032', 'ARG', 'Argentine', 'Buenos Aires', '.ar', 'Argentina', 'Americas', 'ARS', 'Argentine peso', '$', NULL, -34.00, -64.00, '🇦🇷', 'U+1F1E6 U+1F1F7', 1, 1, 0),
(11, 'Armenia', 'AM', '051', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Armenia\", \"cn\": \"亚美尼亚\", \"de\": \"Armenien\", \"es\": \"Armenia\", \"fa\": \"ارمنستان\", \"fr\": \"Arménie\", \"hr\": \"Armenija\", \"it\": \"Armenia\", \"ja\": \"アルメニア\", \"kr\": \"아르메니아\", \"nl\": \"Armenië\", \"pt\": \"Arménia\", \"tr\": \"Ermenistan\", \"pt-BR\": \"Armênia\"}', '[{\"tzName\": \"Armenia Time\", \"zoneName\": \"Asia/Yerevan\", \"gmtOffset\": 14400, \"abbreviation\": \"AMT\", \"gmtOffsetName\": \"UTC+04:00\"}]', '051', 'ARM', 'Armenian', 'Yerevan', '.am', 'Հայաստան', 'Asia', 'AMD', 'Armenian dram', '֏', NULL, 40.00, 45.00, '🇦🇲', 'U+1F1E6 U+1F1F2', 1, 1, 0),
(12, 'Aruba', 'AW', '533', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Aruba\", \"cn\": \"阿鲁巴\", \"de\": \"Aruba\", \"es\": \"Aruba\", \"fa\": \"آروبا\", \"fr\": \"Aruba\", \"hr\": \"Aruba\", \"it\": \"Aruba\", \"ja\": \"アルバ\", \"kr\": \"아루바\", \"nl\": \"Aruba\", \"pt\": \"Aruba\", \"tr\": \"Aruba\", \"pt-BR\": \"Aruba\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Aruba\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '533', 'ABW', 'Aruban', 'Oranjestad', '.aw', 'Aruba', 'Americas', 'AWG', 'Aruban florin', 'ƒ', NULL, 12.50, -69.97, '🇦🇼', 'U+1F1E6 U+1F1FC', 1, 1, 0),
(13, 'Australia', 'AU', '036', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Australia\", \"cn\": \"澳大利亚\", \"de\": \"Australien\", \"es\": \"Australia\", \"fa\": \"استرالیا\", \"fr\": \"Australie\", \"hr\": \"Australija\", \"it\": \"Australia\", \"ja\": \"オーストラリア\", \"kr\": \"호주\", \"nl\": \"Australië\", \"pt\": \"Austrália\", \"tr\": \"Avustralya\", \"pt-BR\": \"Austrália\"}', '[{\"tzName\": \"Macquarie Island Station Time\", \"zoneName\": \"Antarctica/Macquarie\", \"gmtOffset\": 39600, \"abbreviation\": \"MIST\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Australian Central Daylight Saving Time\", \"zoneName\": \"Australia/Adelaide\", \"gmtOffset\": 37800, \"abbreviation\": \"ACDT\", \"gmtOffsetName\": \"UTC+10:30\"}, {\"tzName\": \"Australian Eastern Standard Time\", \"zoneName\": \"Australia/Brisbane\", \"gmtOffset\": 36000, \"abbreviation\": \"AEST\", \"gmtOffsetName\": \"UTC+10:00\"}, {\"tzName\": \"Australian Central Daylight Saving Time\", \"zoneName\": \"Australia/Broken_Hill\", \"gmtOffset\": 37800, \"abbreviation\": \"ACDT\", \"gmtOffsetName\": \"UTC+10:30\"}, {\"tzName\": \"Australian Eastern Daylight Saving Time\", \"zoneName\": \"Australia/Currie\", \"gmtOffset\": 39600, \"abbreviation\": \"AEDT\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Australian Central Standard Time\", \"zoneName\": \"Australia/Darwin\", \"gmtOffset\": 34200, \"abbreviation\": \"ACST\", \"gmtOffsetName\": \"UTC+09:30\"}, {\"tzName\": \"Australian Central Western Standard Time (Unofficial)\", \"zoneName\": \"Australia/Eucla\", \"gmtOffset\": 31500, \"abbreviation\": \"ACWST\", \"gmtOffsetName\": \"UTC+08:45\"}, {\"tzName\": \"Australian Eastern Daylight Saving Time\", \"zoneName\": \"Australia/Hobart\", \"gmtOffset\": 39600, \"abbreviation\": \"AEDT\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Australian Eastern Standard Time\", \"zoneName\": \"Australia/Lindeman\", \"gmtOffset\": 36000, \"abbreviation\": \"AEST\", \"gmtOffsetName\": \"UTC+10:00\"}, {\"tzName\": \"Lord Howe Summer Time\", \"zoneName\": \"Australia/Lord_Howe\", \"gmtOffset\": 39600, \"abbreviation\": \"LHST\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Australian Eastern Daylight Saving Time\", \"zoneName\": \"Australia/Melbourne\", \"gmtOffset\": 39600, \"abbreviation\": \"AEDT\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Australian Western Standard Time\", \"zoneName\": \"Australia/Perth\", \"gmtOffset\": 28800, \"abbreviation\": \"AWST\", \"gmtOffsetName\": \"UTC+08:00\"}, {\"tzName\": \"Australian Eastern Daylight Saving Time\", \"zoneName\": \"Australia/Sydney\", \"gmtOffset\": 39600, \"abbreviation\": \"AEDT\", \"gmtOffsetName\": \"UTC+11:00\"}]', '036', 'AUS', 'Australian', 'Canberra', '.au', 'Australia', 'Oceania', 'AUD', 'Australian dollar', '$', NULL, -27.00, 133.00, '🇦🇺', 'U+1F1E6 U+1F1FA', 1, 1, 0),
(14, 'Austria', 'AT', '040', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Austria\", \"cn\": \"奥地利\", \"de\": \"Österreich\", \"es\": \"Austria\", \"fa\": \"اتریش\", \"fr\": \"Autriche\", \"hr\": \"Austrija\", \"it\": \"Austria\", \"ja\": \"オーストリア\", \"kr\": \"오스트리아\", \"nl\": \"Oostenrijk\", \"pt\": \"áustria\", \"tr\": \"Avusturya\", \"pt-BR\": \"áustria\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Vienna\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '040', 'AUT', 'Austrian', 'Vienna', '.at', 'Österreich', 'Europe', 'EUR', 'Euro', '€', NULL, 47.33, 13.33, '🇦🇹', 'U+1F1E6 U+1F1F9', 1, 1, 0),
(15, 'Azerbaijan', 'AZ', '031', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Azerbaijan\", \"cn\": \"阿塞拜疆\", \"de\": \"Aserbaidschan\", \"es\": \"Azerbaiyán\", \"fa\": \"آذربایجان\", \"fr\": \"Azerbaïdjan\", \"hr\": \"Azerbajdžan\", \"it\": \"Azerbaijan\", \"ja\": \"アゼルバイジャン\", \"kr\": \"아제르바이잔\", \"nl\": \"Azerbeidzjan\", \"pt\": \"Azerbaijão\", \"tr\": \"Azerbaycan\", \"pt-BR\": \"Azerbaijão\"}', '[{\"tzName\": \"Azerbaijan Time\", \"zoneName\": \"Asia/Baku\", \"gmtOffset\": 14400, \"abbreviation\": \"AZT\", \"gmtOffsetName\": \"UTC+04:00\"}]', '031', 'AZE', 'Azerbaijani, Azeri', 'Baku', '.az', 'Azərbaycan', 'Asia', 'AZN', 'Azerbaijani manat', 'm', NULL, 40.50, 47.50, '🇦🇿', 'U+1F1E6 U+1F1FF', 1, 1, 0),
(16, 'The Bahamas', 'BS', '044', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"The Bahamas\", \"cn\": \"巴哈马\", \"de\": \"Bahamas\", \"es\": \"Bahamas\", \"fa\": \"باهاما\", \"fr\": \"Bahamas\", \"hr\": \"Bahami\", \"it\": \"Bahamas\", \"ja\": \"バハマ\", \"kr\": \"바하마\", \"nl\": \"Bahama’s\", \"pt\": \"Baamas\", \"tr\": \"Bahamalar\", \"pt-BR\": \"Bahamas\"}', '[{\"tzName\": \"Eastern Standard Time (North America)\", \"zoneName\": \"America/Nassau\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}]', '044', 'BHS', 'Bahamian', 'Nassau', '.bs', 'Bahamas', 'Americas', 'BSD', 'Bahamian dollar', 'B$', NULL, 24.25, -76.00, '🇧🇸', 'U+1F1E7 U+1F1F8', 1, 1, 0),
(17, 'Bahrain', 'BH', '048', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Bahrain\", \"cn\": \"巴林\", \"de\": \"Bahrain\", \"es\": \"Bahrein\", \"fa\": \"بحرین\", \"fr\": \"Bahreïn\", \"hr\": \"Bahrein\", \"it\": \"Bahrein\", \"ja\": \"バーレーン\", \"kr\": \"바레인\", \"nl\": \"Bahrein\", \"pt\": \"Barém\", \"tr\": \"Bahreyn\", \"pt-BR\": \"Bahrein\"}', '[{\"tzName\": \"Arabia Standard Time\", \"zoneName\": \"Asia/Bahrain\", \"gmtOffset\": 10800, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC+03:00\"}]', '048', 'BHR', 'Bahraini', 'Manama', '.bh', '‏البحرين', 'Asia', 'BHD', 'Bahraini dinar', '.د.ب', NULL, 26.00, 50.55, '🇧🇭', 'U+1F1E7 U+1F1ED', 1, 1, 5),
(18, 'Bangladesh', 'BD', '050', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Bangladesh\", \"cn\": \"孟加拉\", \"de\": \"Bangladesch\", \"es\": \"Bangladesh\", \"fa\": \"بنگلادش\", \"fr\": \"Bangladesh\", \"hr\": \"Bangladeš\", \"it\": \"Bangladesh\", \"ja\": \"バングラデシュ\", \"kr\": \"방글라데시\", \"nl\": \"Bangladesh\", \"pt\": \"Bangladeche\", \"tr\": \"Bangladeş\", \"pt-BR\": \"Bangladesh\"}', '[{\"tzName\": \"Bangladesh Standard Time\", \"zoneName\": \"Asia/Dhaka\", \"gmtOffset\": 21600, \"abbreviation\": \"BDT\", \"gmtOffsetName\": \"UTC+06:00\"}]', '050', 'BGD', 'Bangladeshi', 'Dhaka', '.bd', 'Bangladesh', 'Asia', 'BDT', 'Bangladeshi taka', '৳', NULL, 24.00, 90.00, '🇧🇩', 'U+1F1E7 U+1F1E9', 1, 1, 0),
(19, 'Barbados', 'BB', '052', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Barbados\", \"cn\": \"巴巴多斯\", \"de\": \"Barbados\", \"es\": \"Barbados\", \"fa\": \"باربادوس\", \"fr\": \"Barbade\", \"hr\": \"Barbados\", \"it\": \"Barbados\", \"ja\": \"バルバドス\", \"kr\": \"바베이도스\", \"nl\": \"Barbados\", \"pt\": \"Barbados\", \"tr\": \"Barbados\", \"pt-BR\": \"Barbados\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Barbados\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '052', 'BRB', 'Barbadian', 'Bridgetown', '.bb', 'Barbados', 'Americas', 'BBD', 'Barbadian dollar', 'Bds$', NULL, 13.17, -59.53, '🇧🇧', 'U+1F1E7 U+1F1E7', 1, 1, 0),
(20, 'Belarus', 'BY', '112', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Belarus\", \"cn\": \"白俄罗斯\", \"de\": \"Weißrussland\", \"es\": \"Bielorrusia\", \"fa\": \"بلاروس\", \"fr\": \"Biélorussie\", \"hr\": \"Bjelorusija\", \"it\": \"Bielorussia\", \"ja\": \"ベラルーシ\", \"kr\": \"벨라루스\", \"nl\": \"Wit-Rusland\", \"pt\": \"Bielorrússia\", \"tr\": \"Belarus\", \"pt-BR\": \"Bielorrússia\"}', '[{\"tzName\": \"Moscow Time\", \"zoneName\": \"Europe/Minsk\", \"gmtOffset\": 10800, \"abbreviation\": \"MSK\", \"gmtOffsetName\": \"UTC+03:00\"}]', '112', 'BLR', 'Belarusian', 'Minsk', '.by', 'Белару́сь', 'Europe', 'BYN', 'Belarusian ruble', 'Br', NULL, 53.00, 28.00, '🇧🇾', 'U+1F1E7 U+1F1FE', 1, 1, 0),
(21, 'Belgium', 'BE', '056', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Belgium\", \"cn\": \"比利时\", \"de\": \"Belgien\", \"es\": \"Bélgica\", \"fa\": \"بلژیک\", \"fr\": \"Belgique\", \"hr\": \"Belgija\", \"it\": \"Belgio\", \"ja\": \"ベルギー\", \"kr\": \"벨기에\", \"nl\": \"België\", \"pt\": \"Bélgica\", \"tr\": \"Belçika\", \"pt-BR\": \"Bélgica\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Brussels\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '056', 'BEL', 'Belgian', 'Brussels', '.be', 'België', 'Europe', 'EUR', 'Euro', '€', NULL, 50.83, 4.00, '🇧🇪', 'U+1F1E7 U+1F1EA', 1, 1, 0),
(22, 'Belize', 'BZ', '084', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Belize\", \"cn\": \"伯利兹\", \"de\": \"Belize\", \"es\": \"Belice\", \"fa\": \"بلیز\", \"fr\": \"Belize\", \"hr\": \"Belize\", \"it\": \"Belize\", \"ja\": \"ベリーズ\", \"kr\": \"벨리즈\", \"nl\": \"Belize\", \"pt\": \"Belize\", \"tr\": \"Belize\", \"pt-BR\": \"Belize\"}', '[{\"tzName\": \"Central Standard Time (North America)\", \"zoneName\": \"America/Belize\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}]', '084', 'BLZ', 'Belizean', 'Belmopan', '.bz', 'Belize', 'Americas', 'BZD', 'Belize dollar', '$', NULL, 17.25, -88.75, '🇧🇿', 'U+1F1E7 U+1F1FF', 1, 1, 0),
(23, 'Benin', 'BJ', '204', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Benin\", \"cn\": \"贝宁\", \"de\": \"Benin\", \"es\": \"Benín\", \"fa\": \"بنین\", \"fr\": \"Bénin\", \"hr\": \"Benin\", \"it\": \"Benin\", \"ja\": \"ベナン\", \"kr\": \"베냉\", \"nl\": \"Benin\", \"pt\": \"Benim\", \"tr\": \"Benin\", \"pt-BR\": \"Benin\"}', '[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Porto-Novo\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}]', '204', 'BEN', 'Beninese, Beninois', 'Porto-Novo', '.bj', 'Bénin', 'Africa', 'XOF', 'West African CFA franc', 'CFA', NULL, 9.50, 2.25, '🇧🇯', 'U+1F1E7 U+1F1EF', 1, 1, 0),
(24, 'Bermuda', 'BM', '060', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Bermuda\", \"cn\": \"百慕大\", \"de\": \"Bermuda\", \"es\": \"Bermudas\", \"fa\": \"برمودا\", \"fr\": \"Bermudes\", \"hr\": \"Bermudi\", \"it\": \"Bermuda\", \"ja\": \"バミューダ\", \"kr\": \"버뮤다\", \"nl\": \"Bermuda\", \"pt\": \"Bermudas\", \"tr\": \"Bermuda\", \"pt-BR\": \"Bermudas\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"Atlantic/Bermuda\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '060', 'BMU', 'Bermudian, Bermudan', 'Hamilton', '.bm', 'Bermuda', 'Americas', 'BMD', 'Bermudian dollar', '$', NULL, 32.33, -64.75, '🇧🇲', 'U+1F1E7 U+1F1F2', 1, 1, 0),
(25, 'Bhutan', 'BT', '064', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Bhutan\", \"cn\": \"不丹\", \"de\": \"Bhutan\", \"es\": \"Bután\", \"fa\": \"بوتان\", \"fr\": \"Bhoutan\", \"hr\": \"Butan\", \"it\": \"Bhutan\", \"ja\": \"ブータン\", \"kr\": \"부탄\", \"nl\": \"Bhutan\", \"pt\": \"Butão\", \"tr\": \"Butan\", \"pt-BR\": \"Butão\"}', '[{\"tzName\": \"Bhutan Time\", \"zoneName\": \"Asia/Thimphu\", \"gmtOffset\": 21600, \"abbreviation\": \"BTT\", \"gmtOffsetName\": \"UTC+06:00\"}]', '064', 'BTN', 'Bhutanese', 'Thimphu', '.bt', 'ʼbrug-yul', 'Asia', 'BTN', 'Bhutanese ngultrum', 'Nu.', NULL, 27.50, 90.50, '🇧🇹', 'U+1F1E7 U+1F1F9', 1, 1, 0),
(26, 'Bolivia', 'BO', '068', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Bolivia\", \"cn\": \"玻利维亚\", \"de\": \"Bolivien\", \"es\": \"Bolivia\", \"fa\": \"بولیوی\", \"fr\": \"Bolivie\", \"hr\": \"Bolivija\", \"it\": \"Bolivia\", \"ja\": \"ボリビア多民族国\", \"kr\": \"볼리비아\", \"nl\": \"Bolivia\", \"pt\": \"Bolívia\", \"tr\": \"Bolivya\", \"pt-BR\": \"Bolívia\"}', '[{\"tzName\": \"Bolivia Time\", \"zoneName\": \"America/La_Paz\", \"gmtOffset\": -14400, \"abbreviation\": \"BOT\", \"gmtOffsetName\": \"UTC-04:00\"}]', '068', 'BOL', 'Bolivian', 'Sucre', '.bo', 'Bolivia', 'Americas', 'BOB', 'Bolivian boliviano', 'Bs.', NULL, -17.00, -65.00, '🇧🇴', 'U+1F1E7 U+1F1F4', 1, 1, 0),
(27, 'Bonaire, Sint Eustatius and Saba', 'BQ', '535', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Bonaire, Sint Eustatius and Saba\", \"cn\": \"博内尔岛、圣尤斯特歇斯和萨巴岛\", \"de\": \"Bonaire, Sint Eustatius und Saba\", \"fa\": \"بونیر\", \"fr\": \"Bonaire, Saint-Eustache et Saba\", \"it\": \"Bonaire, Saint-Eustache e Saba\", \"kr\": \"보네르 섬\", \"pt\": \"Bonaire\", \"tr\": \"Karayip Hollandasi\", \"pt-BR\": \"Bonaire\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Anguilla\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '535', 'BES', 'Bonaire', 'Kralendijk', '.an', 'Caribisch Nederland', 'Americas', 'USD', 'United States dollar', '$', NULL, 12.15, -68.27, '🇧🇶', 'U+1F1E7 U+1F1F6', 1, 1, 0),
(28, 'Bosnia and Herzegovina', 'BA', '070', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Bosnia and Herzegovina\", \"cn\": \"波斯尼亚和黑塞哥维那\", \"de\": \"Bosnien und Herzegowina\", \"es\": \"Bosnia y Herzegovina\", \"fa\": \"بوسنی و هرزگوین\", \"fr\": \"Bosnie-Herzégovine\", \"hr\": \"Bosna i Hercegovina\", \"it\": \"Bosnia ed Erzegovina\", \"ja\": \"ボスニア・ヘルツェゴビナ\", \"kr\": \"보스니아 헤르체고비나\", \"nl\": \"Bosnië en Herzegovina\", \"pt\": \"Bósnia e Herzegovina\", \"tr\": \"Bosna Hersek\", \"pt-BR\": \"Bósnia e Herzegovina\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Sarajevo\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '070', 'BIH', 'Bosnian or Herzegovinian', 'Sarajevo', '.ba', 'Bosna i Hercegovina', 'Europe', 'BAM', 'Bosnia and Herzegovina convertible mark', 'KM', NULL, 44.00, 18.00, '🇧🇦', 'U+1F1E7 U+1F1E6', 1, 1, 0),
(29, 'Botswana', 'BW', '072', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Botswana\", \"cn\": \"博茨瓦纳\", \"de\": \"Botswana\", \"es\": \"Botswana\", \"fa\": \"بوتسوانا\", \"fr\": \"Botswana\", \"hr\": \"Bocvana\", \"it\": \"Botswana\", \"ja\": \"ボツワナ\", \"kr\": \"보츠와나\", \"nl\": \"Botswana\", \"pt\": \"Botsuana\", \"tr\": \"Botsvana\", \"pt-BR\": \"Botsuana\"}', '[{\"tzName\": \"Central Africa Time\", \"zoneName\": \"Africa/Gaborone\", \"gmtOffset\": 7200, \"abbreviation\": \"CAT\", \"gmtOffsetName\": \"UTC+02:00\"}]', '072', 'BWA', 'Motswana, Botswanan', 'Gaborone', '.bw', 'Botswana', 'Africa', 'BWP', 'Botswana pula', 'P', NULL, -22.00, 24.00, '🇧🇼', 'U+1F1E7 U+1F1FC', 1, 1, 0),
(30, 'Bouvet Island', 'BV', '074', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Bouvet Island\", \"cn\": \"布维岛\", \"de\": \"Bouvetinsel\", \"es\": \"Isla Bouvet\", \"fa\": \"جزیره بووه\", \"fr\": \"Île Bouvet\", \"hr\": \"Otok Bouvet\", \"it\": \"Isola Bouvet\", \"ja\": \"ブーベ島\", \"kr\": \"부벳 섬\", \"nl\": \"Bouveteiland\", \"pt\": \"Ilha Bouvet\", \"tr\": \"Bouvet Adasi\", \"pt-BR\": \"Ilha Bouvet\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Oslo\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '074', 'BVT', 'Bouvet Island', '', '.bv', 'Bouvetøya', '', 'NOK', 'Norwegian Krone', 'kr', NULL, -54.43, 3.40, '🇧🇻', 'U+1F1E7 U+1F1FB', 1, 1, 0),
(31, 'Brazil', 'BR', '076', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Brazil\", \"cn\": \"巴西\", \"de\": \"Brasilien\", \"es\": \"Brasil\", \"fa\": \"برزیل\", \"fr\": \"Brésil\", \"hr\": \"Brazil\", \"it\": \"Brasile\", \"ja\": \"ブラジル\", \"kr\": \"브라질\", \"nl\": \"Brazilië\", \"pt\": \"Brasil\", \"tr\": \"Brezilya\", \"pt-BR\": \"Brasil\"}', '[{\"tzName\": \"Brasília Time\", \"zoneName\": \"America/Araguaina\", \"gmtOffset\": -10800, \"abbreviation\": \"BRT\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Brasília Time\", \"zoneName\": \"America/Bahia\", \"gmtOffset\": -10800, \"abbreviation\": \"BRT\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Brasília Time\", \"zoneName\": \"America/Belem\", \"gmtOffset\": -10800, \"abbreviation\": \"BRT\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Amazon Time (Brazil)[3\", \"zoneName\": \"America/Boa_Vista\", \"gmtOffset\": -14400, \"abbreviation\": \"AMT\", \"gmtOffsetName\": \"UTC-04:00\"}, {\"tzName\": \"Amazon Time (Brazil)[3\", \"zoneName\": \"America/Campo_Grande\", \"gmtOffset\": -14400, \"abbreviation\": \"AMT\", \"gmtOffsetName\": \"UTC-04:00\"}, {\"tzName\": \"Brasilia Time\", \"zoneName\": \"America/Cuiaba\", \"gmtOffset\": -14400, \"abbreviation\": \"BRT\", \"gmtOffsetName\": \"UTC-04:00\"}, {\"tzName\": \"Acre Time\", \"zoneName\": \"America/Eirunepe\", \"gmtOffset\": -18000, \"abbreviation\": \"ACT\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Brasília Time\", \"zoneName\": \"America/Fortaleza\", \"gmtOffset\": -10800, \"abbreviation\": \"BRT\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Brasília Time\", \"zoneName\": \"America/Maceio\", \"gmtOffset\": -10800, \"abbreviation\": \"BRT\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Amazon Time (Brazil)\", \"zoneName\": \"America/Manaus\", \"gmtOffset\": -14400, \"abbreviation\": \"AMT\", \"gmtOffsetName\": \"UTC-04:00\"}, {\"tzName\": \"Fernando de Noronha Time\", \"zoneName\": \"America/Noronha\", \"gmtOffset\": -7200, \"abbreviation\": \"FNT\", \"gmtOffsetName\": \"UTC-02:00\"}, {\"tzName\": \"Amazon Time (Brazil)[3\", \"zoneName\": \"America/Porto_Velho\", \"gmtOffset\": -14400, \"abbreviation\": \"AMT\", \"gmtOffsetName\": \"UTC-04:00\"}, {\"tzName\": \"Brasília Time\", \"zoneName\": \"America/Recife\", \"gmtOffset\": -10800, \"abbreviation\": \"BRT\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Acre Time\", \"zoneName\": \"America/Rio_Branco\", \"gmtOffset\": -18000, \"abbreviation\": \"ACT\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Brasília Time\", \"zoneName\": \"America/Santarem\", \"gmtOffset\": -10800, \"abbreviation\": \"BRT\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Brasília Time\", \"zoneName\": \"America/Sao_Paulo\", \"gmtOffset\": -10800, \"abbreviation\": \"BRT\", \"gmtOffsetName\": \"UTC-03:00\"}]', '076', 'BRA', 'Brazilian', 'Brasilia', '.br', 'Brasil', 'Americas', 'BRL', 'Brazilian real', 'R$', NULL, -10.00, -55.00, '🇧🇷', 'U+1F1E7 U+1F1F7', 1, 1, 0),
(32, 'British Indian Ocean Territory', 'IO', '086', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"British Indian Ocean Territory\", \"cn\": \"英属印度洋领地\", \"de\": \"Britisches Territorium im Indischen Ozean\", \"es\": \"Territorio Británico del Océano Índico\", \"fa\": \"قلمرو بریتانیا در اقیانوس هند\", \"fr\": \"Territoire britannique de locéan Indien\", \"hr\": \"Britanski Indijskooceanski teritorij\", \"it\": \"Territorio britannico dell oceano indiano\", \"ja\": \"イギリス領インド洋地域\", \"kr\": \"영국령 인도양 지역\", \"nl\": \"Britse Gebieden in de Indische Oceaan\", \"pt\": \"Território Britânico do Oceano Índico\", \"tr\": \"Britanya Hint Okyanusu Topraklari\", \"pt-BR\": \"Território Britânico do Oceano íÍdico\"}', '[{\"tzName\": \"Indian Ocean Time\", \"zoneName\": \"Indian/Chagos\", \"gmtOffset\": 21600, \"abbreviation\": \"IOT\", \"gmtOffsetName\": \"UTC+06:00\"}]', '086', 'IOT', 'BIOT', 'Diego Garcia', '.io', 'British Indian Ocean Territory', 'Africa', 'USD', 'United States dollar', '$', NULL, -6.00, 71.50, '🇮🇴', 'U+1F1EE U+1F1F4', 1, 1, 0),
(33, 'Brunei', 'BN', '096', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Brunei\", \"cn\": \"文莱\", \"de\": \"Brunei\", \"es\": \"Brunei\", \"fa\": \"برونئی\", \"fr\": \"Brunei\", \"hr\": \"Brunej\", \"it\": \"Brunei\", \"ja\": \"ブルネイ・ダルサラーム\", \"kr\": \"브루나이\", \"nl\": \"Brunei\", \"pt\": \"Brunei\", \"tr\": \"Brunei\", \"pt-BR\": \"Brunei\"}', '[{\"tzName\": \"Brunei Darussalam Time\", \"zoneName\": \"Asia/Brunei\", \"gmtOffset\": 28800, \"abbreviation\": \"BNT\", \"gmtOffsetName\": \"UTC+08:00\"}]', '096', 'BRN', 'Bruneian', 'Bandar Seri Begawan', '.bn', 'Negara Brunei Darussalam', 'Asia', 'BND', 'Brunei dollar', 'B$', NULL, 4.50, 114.67, '🇧🇳', 'U+1F1E7 U+1F1F3', 1, 1, 0),
(34, 'Bulgaria', 'BG', '100', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Bulgaria\", \"cn\": \"保加利亚\", \"de\": \"Bulgarien\", \"es\": \"Bulgaria\", \"fa\": \"بلغارستان\", \"fr\": \"Bulgarie\", \"hr\": \"Bugarska\", \"it\": \"Bulgaria\", \"ja\": \"ブルガリア\", \"kr\": \"불가리아\", \"nl\": \"Bulgarije\", \"pt\": \"Bulgária\", \"tr\": \"Bulgaristan\", \"pt-BR\": \"Bulgária\"}', '[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Sofia\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]', '100', 'BGR', 'Bulgarian', 'Sofia', '.bg', 'България', 'Europe', 'BGN', 'Bulgarian lev', 'Лв.', NULL, 43.00, 25.00, '🇧🇬', 'U+1F1E7 U+1F1EC', 1, 1, 0),
(35, 'Burkina Faso', 'BF', '854', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Burkina Faso\", \"cn\": \"布基纳法索\", \"de\": \"Burkina Faso\", \"es\": \"Burkina Faso\", \"fa\": \"بورکینافاسو\", \"fr\": \"Burkina Faso\", \"hr\": \"Burkina Faso\", \"it\": \"Burkina Faso\", \"ja\": \"ブルキナファソ\", \"kr\": \"부르키나 파소\", \"nl\": \"Burkina Faso\", \"pt\": \"Burquina Faso\", \"tr\": \"Burkina Faso\", \"pt-BR\": \"Burkina Faso\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Ouagadougou\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]', '854', 'BFA', 'Burkinabe', 'Ouagadougou', '.bf', 'Burkina Faso', 'Africa', 'XOF', 'West African CFA franc', 'CFA', NULL, 13.00, -2.00, '🇧🇫', 'U+1F1E7 U+1F1EB', 1, 1, 0),
(36, 'Burundi', 'BI', '108', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Burundi\", \"cn\": \"布隆迪\", \"de\": \"Burundi\", \"es\": \"Burundi\", \"fa\": \"بوروندی\", \"fr\": \"Burundi\", \"hr\": \"Burundi\", \"it\": \"Burundi\", \"ja\": \"ブルンジ\", \"kr\": \"부룬디\", \"nl\": \"Burundi\", \"pt\": \"Burúndi\", \"tr\": \"Burundi\", \"pt-BR\": \"Burundi\"}', '[{\"tzName\": \"Central Africa Time\", \"zoneName\": \"Africa/Bujumbura\", \"gmtOffset\": 7200, \"abbreviation\": \"CAT\", \"gmtOffsetName\": \"UTC+02:00\"}]', '108', 'BDI', 'Burundian', 'Bujumbura', '.bi', 'Burundi', 'Africa', 'BIF', 'Burundian franc', 'FBu', NULL, -3.50, 30.00, '🇧🇮', 'U+1F1E7 U+1F1EE', 1, 1, 0),
(37, 'Cambodia', 'KH', '116', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Cambodia\", \"cn\": \"柬埔寨\", \"de\": \"Kambodscha\", \"es\": \"Camboya\", \"fa\": \"کامبوج\", \"fr\": \"Cambodge\", \"hr\": \"Kambodža\", \"it\": \"Cambogia\", \"ja\": \"カンボジア\", \"kr\": \"캄보디아\", \"nl\": \"Cambodja\", \"pt\": \"Camboja\", \"tr\": \"Kamboçya\", \"pt-BR\": \"Camboja\"}', '[{\"tzName\": \"Indochina Time\", \"zoneName\": \"Asia/Phnom_Penh\", \"gmtOffset\": 25200, \"abbreviation\": \"ICT\", \"gmtOffsetName\": \"UTC+07:00\"}]', '116', 'KHM', 'Cambodian', 'Phnom Penh', '.kh', 'Kâmpŭchéa', 'Asia', 'KHR', 'Cambodian riel', 'KHR', NULL, 13.00, 105.00, '🇰🇭', 'U+1F1F0 U+1F1ED', 1, 1, 0),
(38, 'Cameroon', 'CM', '120', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Cameroon\", \"cn\": \"喀麦隆\", \"de\": \"Kamerun\", \"es\": \"Camerún\", \"fa\": \"کامرون\", \"fr\": \"Cameroun\", \"hr\": \"Kamerun\", \"it\": \"Camerun\", \"ja\": \"カメルーン\", \"kr\": \"카메룬\", \"nl\": \"Kameroen\", \"pt\": \"Camarões\", \"tr\": \"Kamerun\", \"pt-BR\": \"Camarões\"}', '[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Douala\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}]', '120', 'CMR', 'Cameroonian', 'Yaounde', '.cm', 'Cameroon', 'Africa', 'XAF', 'Central African CFA franc', 'FCFA', NULL, 6.00, 12.00, '🇨🇲', 'U+1F1E8 U+1F1F2', 1, 1, 0),
(39, 'Canada', 'CA', '124', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Canada\", \"cn\": \"加拿大\", \"de\": \"Kanada\", \"es\": \"Canadá\", \"fa\": \"کانادا\", \"fr\": \"Canada\", \"hr\": \"Kanada\", \"it\": \"Canada\", \"ja\": \"カナダ\", \"kr\": \"캐나다\", \"nl\": \"Canada\", \"pt\": \"Canadá\", \"tr\": \"Kanada\", \"pt-BR\": \"Canadá\"}', '[{\"tzName\": \"Eastern Standard Time (North America)\", \"zoneName\": \"America/Atikokan\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Blanc-Sablon\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}, {\"tzName\": \"Mountain Standard Time (North America)\", \"zoneName\": \"America/Cambridge_Bay\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Mountain Standard Time (North America)\", \"zoneName\": \"America/Creston\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Mountain Standard Time (North America)\", \"zoneName\": \"America/Dawson\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Mountain Standard Time (North America)\", \"zoneName\": \"America/Dawson_Creek\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Mountain Standard Time (North America)\", \"zoneName\": \"America/Edmonton\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Mountain Standard Time (North America)\", \"zoneName\": \"America/Fort_Nelson\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Glace_Bay\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}, {\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Goose_Bay\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}, {\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Halifax\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}, {\"tzName\": \"Mountain Standard Time (North America\", \"zoneName\": \"America/Inuvik\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Iqaluit\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Moncton\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Nipigon\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Pangnirtung\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Rainy_River\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Rankin_Inlet\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Regina\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Resolute\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Newfoundland Standard Time\", \"zoneName\": \"America/St_Johns\", \"gmtOffset\": -12600, \"abbreviation\": \"NST\", \"gmtOffsetName\": \"UTC-03:30\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Swift_Current\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Thunder_Bay\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Toronto\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Pacific Standard Time (North America\", \"zoneName\": \"America/Vancouver\", \"gmtOffset\": -28800, \"abbreviation\": \"PST\", \"gmtOffsetName\": \"UTC-08:00\"}, {\"tzName\": \"Mountain Standard Time (North America\", \"zoneName\": \"America/Whitehorse\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Winnipeg\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Mountain Standard Time (North America\", \"zoneName\": \"America/Yellowknife\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}]', '124', 'CAN', 'Canadian', 'Ottawa', '.ca', 'Canada', 'Americas', 'CAD', 'Canadian dollar', '$', NULL, 60.00, -95.00, '🇨🇦', 'U+1F1E8 U+1F1E6', 1, 1, 0),
(40, 'Cape Verde', 'CV', '132', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Cape Verde\", \"cn\": \"佛得角\", \"de\": \"Kap Verde\", \"es\": \"Cabo Verde\", \"fa\": \"کیپ ورد\", \"fr\": \"Cap Vert\", \"hr\": \"Zelenortska Republika\", \"it\": \"Capo Verde\", \"ja\": \"カーボベルデ\", \"kr\": \"카보베르데\", \"nl\": \"Kaapverdië\", \"pt\": \"Cabo Verde\", \"tr\": \"Cabo Verde\", \"pt-BR\": \"Cabo Verde\"}', '[{\"tzName\": \"Cape Verde Time\", \"zoneName\": \"Atlantic/Cape_Verde\", \"gmtOffset\": -3600, \"abbreviation\": \"CVT\", \"gmtOffsetName\": \"UTC-01:00\"}]', '132', 'CPV', 'Verdean', 'Praia', '.cv', 'Cabo Verde', 'Africa', 'CVE', 'Cape Verdean escudo', '$', NULL, 16.00, -24.00, '🇨🇻', 'U+1F1E8 U+1F1FB', 1, 1, 0),
(41, 'Cayman Islands', 'KY', '136', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Cayman Islands\", \"cn\": \"开曼群岛\", \"de\": \"Kaimaninseln\", \"es\": \"Islas Caimán\", \"fa\": \"جزایر کیمن\", \"fr\": \"Îles Caïmans\", \"hr\": \"Kajmanski otoci\", \"it\": \"Isole Cayman\", \"ja\": \"ケイマン諸島\", \"kr\": \"케이먼 제도\", \"nl\": \"Caymaneilanden\", \"pt\": \"Ilhas Caimão\", \"tr\": \"Cayman Adalari\", \"pt-BR\": \"Ilhas Cayman\"}', '[{\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Cayman\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}]', '136', 'CYM', 'Caymanian', 'George Town', '.ky', 'Cayman Islands', 'Americas', 'KYD', 'Cayman Islands dollar', '$', NULL, 19.50, -80.50, '🇰🇾', 'U+1F1F0 U+1F1FE', 1, 1, 0),
(42, 'Central African Republic', 'CF', '140', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Central African Republic\", \"cn\": \"中非\", \"de\": \"Zentralafrikanische Republik\", \"es\": \"República Centroafricana\", \"fa\": \"جمهوری آفریقای مرکزی\", \"fr\": \"République centrafricaine\", \"hr\": \"Srednjoafrička Republika\", \"it\": \"Repubblica Centrafricana\", \"ja\": \"中央アフリカ共和国\", \"kr\": \"중앙아프리카 공화국\", \"nl\": \"Centraal-Afrikaanse Republiek\", \"pt\": \"República Centro-Africana\", \"tr\": \"Orta Afrika Cumhuriyeti\", \"pt-BR\": \"República Centro-Africana\"}', '[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Bangui\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}]', '140', 'CAF', 'Central African', 'Bangui', '.cf', 'Ködörösêse tî Bêafrîka', 'Africa', 'XAF', 'Central African CFA franc', 'FCFA', NULL, 7.00, 21.00, '🇨🇫', 'U+1F1E8 U+1F1EB', 1, 1, 0),
(43, 'Chad', 'TD', '148', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Chad\", \"cn\": \"乍得\", \"de\": \"Tschad\", \"es\": \"Chad\", \"fa\": \"چاد\", \"fr\": \"Tchad\", \"hr\": \"Čad\", \"it\": \"Ciad\", \"ja\": \"チャド\", \"kr\": \"차드\", \"nl\": \"Tsjaad\", \"pt\": \"Chade\", \"tr\": \"Çad\", \"pt-BR\": \"Chade\"}', '[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Ndjamena\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}]', '148', 'TCD', 'Chadian', 'NDjamena', '.td', 'Tchad', 'Africa', 'XAF', 'Central African CFA franc', 'FCFA', NULL, 15.00, 19.00, '🇹🇩', 'U+1F1F9 U+1F1E9', 1, 1, 0),
(44, 'Chile', 'CL', '152', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Chile\", \"cn\": \"智利\", \"de\": \"Chile\", \"es\": \"Chile\", \"fa\": \"شیلی\", \"fr\": \"Chili\", \"hr\": \"Čile\", \"it\": \"Cile\", \"ja\": \"チリ\", \"kr\": \"칠리\", \"nl\": \"Chili\", \"pt\": \"Chile\", \"tr\": \"Şili\", \"pt-BR\": \"Chile\"}', '[{\"tzName\": \"Chile Summer Time\", \"zoneName\": \"America/Punta_Arenas\", \"gmtOffset\": -10800, \"abbreviation\": \"CLST\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Chile Summer Time\", \"zoneName\": \"America/Santiago\", \"gmtOffset\": -10800, \"abbreviation\": \"CLST\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Easter Island Summer Time\", \"zoneName\": \"Pacific/Easter\", \"gmtOffset\": -18000, \"abbreviation\": \"EASST\", \"gmtOffsetName\": \"UTC-05:00\"}]', '152', 'CHL', 'Chilean', 'Santiago', '.cl', 'Chile', 'Americas', 'CLP', 'Chilean peso', '$', NULL, -30.00, -71.00, '🇨🇱', 'U+1F1E8 U+1F1F1', 1, 1, 0),
(45, 'China', 'CN', '156', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"China\", \"cn\": \"中国\", \"de\": \"China\", \"es\": \"China\", \"fa\": \"چین\", \"fr\": \"Chine\", \"hr\": \"Kina\", \"it\": \"Cina\", \"ja\": \"中国\", \"kr\": \"중국\", \"nl\": \"China\", \"pt\": \"China\", \"tr\": \"Çin\", \"pt-BR\": \"China\"}', '[{\"tzName\": \"China Standard Time\", \"zoneName\": \"Asia/Shanghai\", \"gmtOffset\": 28800, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC+08:00\"}, {\"tzName\": \"China Standard Time\", \"zoneName\": \"Asia/Urumqi\", \"gmtOffset\": 21600, \"abbreviation\": \"XJT\", \"gmtOffsetName\": \"UTC+06:00\"}]', '156', 'CHN', 'Chinese', 'Beijing', '.cn', '中国', 'Asia', 'CNY', 'Chinese yuan', '¥', NULL, 35.00, 105.00, '🇨🇳', 'U+1F1E8 U+1F1F3', 1, 1, 0),
(46, 'Christmas Island', 'CX', '162', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Christmas Island\", \"cn\": \"圣诞岛\", \"de\": \"Weihnachtsinsel\", \"es\": \"Isla de Navidad\", \"fa\": \"جزیره کریسمس\", \"fr\": \"Île Christmas\", \"hr\": \"Božićni otok\", \"it\": \"Isola di Natale\", \"ja\": \"クリスマス島\", \"kr\": \"크리스마스 섬\", \"nl\": \"Christmaseiland\", \"pt\": \"Ilha do Natal\", \"tr\": \"Christmas Adasi\", \"pt-BR\": \"Ilha Christmas\"}', '[{\"tzName\": \"Christmas Island Time\", \"zoneName\": \"Indian/Christmas\", \"gmtOffset\": 25200, \"abbreviation\": \"CXT\", \"gmtOffsetName\": \"UTC+07:00\"}]', '162', 'CXR', 'Christmas Island', 'Flying Fish Cove', '.cx', 'Christmas Island', 'Oceania', 'AUD', 'Australian dollar', '$', NULL, -10.50, 105.67, '🇨🇽', 'U+1F1E8 U+1F1FD', 1, 1, 0),
(47, 'Cocos (Keeling) Islands', 'CC', '166', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Cocos (Keeling) Islands\", \"cn\": \"科科斯（基林）群岛\", \"de\": \"Kokosinseln\", \"es\": \"Islas Cocos o Islas Keeling\", \"fa\": \"جزایر کوکوس\", \"fr\": \"Îles Cocos\", \"hr\": \"Kokosovi Otoci\", \"it\": \"Isole Cocos e Keeling\", \"ja\": \"ココス（キーリング）諸島\", \"kr\": \"코코스 제도\", \"nl\": \"Cocoseilanden\", \"pt\": \"Ilhas dos Cocos\", \"tr\": \"Cocos Adalari\", \"pt-BR\": \"Ilhas Cocos\"}', '[{\"tzName\": \"Cocos Islands Time\", \"zoneName\": \"Indian/Cocos\", \"gmtOffset\": 23400, \"abbreviation\": \"CCT\", \"gmtOffsetName\": \"UTC+06:30\"}]', '166', 'CCK', 'Cocos Island', 'West Island', '.cc', 'Cocos (Keeling) Islands', 'Oceania', 'AUD', 'Australian dollar', '$', NULL, -12.50, 96.83, '🇨🇨', 'U+1F1E8 U+1F1E8', 1, 1, 0),
(48, 'Colombia', 'CO', '170', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Colombia\", \"cn\": \"哥伦比亚\", \"de\": \"Kolumbien\", \"es\": \"Colombia\", \"fa\": \"کلمبیا\", \"fr\": \"Colombie\", \"hr\": \"Kolumbija\", \"it\": \"Colombia\", \"ja\": \"コロンビア\", \"kr\": \"콜롬비아\", \"nl\": \"Colombia\", \"pt\": \"Colômbia\", \"tr\": \"Kolombiya\", \"pt-BR\": \"Colômbia\"}', '[{\"tzName\": \"Colombia Time\", \"zoneName\": \"America/Bogota\", \"gmtOffset\": -18000, \"abbreviation\": \"COT\", \"gmtOffsetName\": \"UTC-05:00\"}]', '170', 'COL', 'Colombian', 'Bogotá', '.co', 'Colombia', 'Americas', 'COP', 'Colombian peso', '$', NULL, 4.00, -72.00, '🇨🇴', 'U+1F1E8 U+1F1F4', 1, 1, 0),
(49, 'Comoros', 'KM', '174', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Comoros\", \"cn\": \"科摩罗\", \"de\": \"Union der Komoren\", \"es\": \"Comoras\", \"fa\": \"کومور\", \"fr\": \"Comores\", \"hr\": \"Komori\", \"it\": \"Comore\", \"ja\": \"コモロ\", \"kr\": \"코모로\", \"nl\": \"Comoren\", \"pt\": \"Comores\", \"tr\": \"Komorlar\", \"pt-BR\": \"Comores\"}', '[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Indian/Comoro\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]', '174', 'COM', 'Comoran, Comorian', 'Moroni', '.km', 'Komori', 'Africa', 'KMF', 'Comorian franc', 'CF', NULL, -12.17, 44.25, '🇰🇲', 'U+1F1F0 U+1F1F2', 1, 1, 0),
(50, 'Congo', 'CG', '178', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Congo\", \"cn\": \"刚果\", \"de\": \"Kongo\", \"es\": \"Congo\", \"fa\": \"کنگو\", \"fr\": \"Congo\", \"hr\": \"Kongo\", \"it\": \"Congo\", \"ja\": \"コンゴ共和国\", \"kr\": \"콩고\", \"nl\": \"Congo [Republiek]\", \"pt\": \"Congo\", \"tr\": \"Kongo\", \"pt-BR\": \"Congo\"}', '[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Brazzaville\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}]', '178', 'COG', 'Congolese', 'Brazzaville', '.cg', 'République du Congo', 'Africa', 'XAF', 'Central African CFA franc', 'FC', NULL, -1.00, 15.00, '🇨🇬', 'U+1F1E8 U+1F1EC', 1, 1, 0),
(51, 'Democratic Republic of the Congo', 'CD', '180', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Democratic Republic of the Congo\", \"cn\": \"刚果（金）\", \"de\": \"Kongo (Dem. Rep.)\", \"es\": \"Congo (Rep. Dem.)\", \"fa\": \"جمهوری کنگو\", \"fr\": \"Congo (Rép. dém.)\", \"hr\": \"Kongo, Demokratska Republika\", \"it\": \"Congo (Rep. Dem.)\", \"ja\": \"コンゴ民主共和国\", \"kr\": \"콩고 민주 공화국\", \"nl\": \"Congo [DRC]\", \"pt\": \"RD Congo\", \"tr\": \"Kongo Demokratik Cumhuriyeti\", \"pt-BR\": \"RD Congo\"}', '[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Kinshasa\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}, {\"tzName\": \"Central Africa Time\", \"zoneName\": \"Africa/Lubumbashi\", \"gmtOffset\": 7200, \"abbreviation\": \"CAT\", \"gmtOffsetName\": \"UTC+02:00\"}]', '180', 'COD', 'Congolese', 'Kinshasa', '.cd', 'République démocratique du Congo', 'Africa', 'CDF', 'Congolese Franc', 'FC', NULL, 0.00, 25.00, '🇨🇩', 'U+1F1E8 U+1F1E9', 1, 1, 0),
(52, 'Cook Islands', 'CK', '184', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Cook Islands\", \"cn\": \"库克群岛\", \"de\": \"Cookinseln\", \"es\": \"Islas Cook\", \"fa\": \"جزایر کوک\", \"fr\": \"Îles Cook\", \"hr\": \"Cookovo Otočje\", \"it\": \"Isole Cook\", \"ja\": \"クック諸島\", \"kr\": \"쿡 제도\", \"nl\": \"Cookeilanden\", \"pt\": \"Ilhas Cook\", \"tr\": \"Cook Adalari\", \"pt-BR\": \"Ilhas Cook\"}', '[{\"tzName\": \"Cook Island Time\", \"zoneName\": \"Pacific/Rarotonga\", \"gmtOffset\": -36000, \"abbreviation\": \"CKT\", \"gmtOffsetName\": \"UTC-10:00\"}]', '184', 'COK', 'Cook Island', 'Avarua', '.ck', 'Cook Islands', 'Oceania', 'NZD', 'Cook Islands dollar', '$', NULL, -21.23, -159.77, '🇨🇰', 'U+1F1E8 U+1F1F0', 1, 1, 0),
(53, 'Costa Rica', 'CR', '188', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Costa Rica\", \"cn\": \"哥斯达黎加\", \"de\": \"Costa Rica\", \"es\": \"Costa Rica\", \"fa\": \"کاستاریکا\", \"fr\": \"Costa Rica\", \"hr\": \"Kostarika\", \"it\": \"Costa Rica\", \"ja\": \"コスタリカ\", \"kr\": \"코스타리카\", \"nl\": \"Costa Rica\", \"pt\": \"Costa Rica\", \"tr\": \"Kosta Rika\", \"pt-BR\": \"Costa Rica\"}', '[{\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Costa_Rica\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}]', '188', 'CRI', 'Costa Rican', 'San Jose', '.cr', 'Costa Rica', 'Americas', 'CRC', 'Costa Rican colón', '₡', NULL, 10.00, -84.00, '🇨🇷', 'U+1F1E8 U+1F1F7', 1, 1, 0),
(54, 'Cote DIvoire (Ivory Coast)', 'CI', '384', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Cote DIvoire (Ivory Coast)\", \"cn\": \"科特迪瓦\", \"de\": \"Elfenbeinküste\", \"es\": \"Costa de Marfil\", \"fa\": \"ساحل عاج\", \"fr\": \"Côte dIvoire\", \"hr\": \"Obala Bjelokosti\", \"it\": \"Costa DAvorio\", \"ja\": \"コートジボワール\", \"kr\": \"코트디부아르\", \"nl\": \"Ivoorkust\", \"pt\": \"Costa do Marfim\", \"tr\": \"Kotdivuar\", \"pt-BR\": \"Costa do Marfim\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Abidjan\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]', '384', 'CIV', 'Ivorian', 'Yamoussoukro', '.ci', NULL, 'Africa', 'XOF', 'West African CFA franc', 'CFA', NULL, 8.00, -5.00, '🇨🇮', 'U+1F1E8 U+1F1EE', 1, 1, 0),
(55, 'Croatia', 'HR', '191', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Croatia\", \"cn\": \"克罗地亚\", \"de\": \"Kroatien\", \"es\": \"Croacia\", \"fa\": \"کرواسی\", \"fr\": \"Croatie\", \"hr\": \"Hrvatska\", \"it\": \"Croazia\", \"ja\": \"クロアチア\", \"kr\": \"크로아티아\", \"nl\": \"Kroatië\", \"pt\": \"Croácia\", \"tr\": \"Hirvatistan\", \"pt-BR\": \"Croácia\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Zagreb\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '191', 'HRV', 'Croatian', 'Zagreb', '.hr', 'Hrvatska', 'Europe', 'HRK', 'Croatian kuna', 'kn', NULL, 45.17, 15.50, '🇭🇷', 'U+1F1ED U+1F1F7', 1, 1, 0),
(56, 'Cuba', 'CU', '192', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Cuba\", \"cn\": \"古巴\", \"de\": \"Kuba\", \"es\": \"Cuba\", \"fa\": \"کوبا\", \"fr\": \"Cuba\", \"hr\": \"Kuba\", \"it\": \"Cuba\", \"ja\": \"キューバ\", \"kr\": \"쿠바\", \"nl\": \"Cuba\", \"pt\": \"Cuba\", \"tr\": \"Küba\", \"pt-BR\": \"Cuba\"}', '[{\"tzName\": \"Cuba Standard Time\", \"zoneName\": \"America/Havana\", \"gmtOffset\": -18000, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-05:00\"}]', '192', 'CUB', 'Cuban', 'Havana', '.cu', 'Cuba', 'Americas', 'CUP', 'Cuban peso', '$', NULL, 21.50, -80.00, '🇨🇺', 'U+1F1E8 U+1F1FA', 1, 1, 0),
(57, 'Curaçao', 'CW', '531', '2020-06-17 10:37:04', '2024-01-21 12:54:30', '{\"ar\": \"Curaçao\", \"cn\": \"库拉索\", \"de\": \"Curaçao\", \"fa\": \"کوراسائو\", \"fr\": \"Curaçao\", \"it\": \"Curaçao\", \"kr\": \"퀴라소\", \"nl\": \"Curaçao\", \"pt\": \"Curaçao\", \"tr\": \"Curaçao\", \"pt-BR\": \"Curaçao\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Curacao\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '531', 'CUW', 'Curacaoan', 'Willemstad', '.cw', 'Curaçao', 'Americas', 'ANG', 'Netherlands Antillean guilder', 'ƒ', NULL, 12.12, -68.93, '🇨🇼', 'U+1F1E8 U+1F1FC', 1, 1, 0),
(58, 'Cyprus', 'CY', '196', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Cyprus\", \"cn\": \"塞浦路斯\", \"de\": \"Zypern\", \"es\": \"Chipre\", \"fa\": \"قبرس\", \"fr\": \"Chypre\", \"hr\": \"Cipar\", \"it\": \"Cipro\", \"ja\": \"キプロス\", \"kr\": \"키프로스\", \"nl\": \"Cyprus\", \"pt\": \"Chipre\", \"tr\": \"Kuzey Kıbrıs Türk Cumhuriyeti\", \"pt-BR\": \"Chipre\"}', '[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Asia/Famagusta\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}, {\"tzName\": \"Eastern European Time\", \"zoneName\": \"Asia/Nicosia\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]', '196', 'CYP', 'Cypriot', 'Nicosia', '.cy', 'Κύπρος', 'Europe', 'EUR', 'Euro', '€', NULL, 35.00, 33.00, '🇨🇾', 'U+1F1E8 U+1F1FE', 1, 1, 0),
(59, 'Czech Republic', 'CZ', '203', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Czech Republic\", \"cn\": \"捷克\", \"de\": \"Tschechische Republik\", \"es\": \"República Checa\", \"fa\": \"جمهوری چک\", \"fr\": \"République tchèque\", \"hr\": \"Češka\", \"it\": \"Repubblica Ceca\", \"ja\": \"チェコ\", \"kr\": \"체코\", \"nl\": \"Tsjechië\", \"pt\": \"República Checa\", \"tr\": \"Çekya\", \"pt-BR\": \"República Tcheca\"}', NULL, '203', 'CZE', 'Czech', 'Prague', '.cz', 'Česká republika', 'Europe', 'CZK', 'Czech koruna', 'Kč', NULL, 49.75, 15.50, '🇨🇿', 'U+1F1E8 U+1F1FF', 1, 1, 0),
(60, 'Denmark', 'DK', '208', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Denmark\", \"cn\": \"丹麦\", \"de\": \"Dänemark\", \"es\": \"Dinamarca\", \"fa\": \"دانمارک\", \"fr\": \"Danemark\", \"hr\": \"Danska\", \"it\": \"Danimarca\", \"ja\": \"デンマーク\", \"kr\": \"덴마크\", \"nl\": \"Denemarken\", \"pt\": \"Dinamarca\", \"tr\": \"Danimarka\", \"pt-BR\": \"Dinamarca\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Copenhagen\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '208', 'DNK', 'Danish', 'Copenhagen', '.dk', 'Danmark', 'Europe', 'DKK', 'Danish krone', 'Kr.', NULL, 56.00, 10.00, '🇩🇰', 'U+1F1E9 U+1F1F0', 1, 1, 0),
(61, 'Djibouti', 'DJ', '262', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Djibouti\", \"cn\": \"吉布提\", \"de\": \"Dschibuti\", \"es\": \"Yibuti\", \"fa\": \"جیبوتی\", \"fr\": \"Djibouti\", \"hr\": \"Džibuti\", \"it\": \"Gibuti\", \"ja\": \"ジブチ\", \"kr\": \"지부티\", \"nl\": \"Djibouti\", \"pt\": \"Djibuti\", \"tr\": \"Cibuti\", \"pt-BR\": \"Djibuti\"}', '[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Africa/Djibouti\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]', '262', 'DJI', 'Djiboutian', 'Djibouti', '.dj', 'Djibouti', 'Africa', 'DJF', 'Djiboutian franc', 'Fdj', NULL, 11.50, 43.00, '🇩🇯', 'U+1F1E9 U+1F1EF', 1, 1, 0),
(62, 'Dominica', 'DM', '212', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Dominica\", \"cn\": \"多米尼加\", \"de\": \"Dominica\", \"es\": \"Dominica\", \"fa\": \"دومینیکا\", \"fr\": \"Dominique\", \"hr\": \"Dominika\", \"it\": \"Dominica\", \"ja\": \"ドミニカ国\", \"kr\": \"도미니카 연방\", \"nl\": \"Dominica\", \"pt\": \"Dominica\", \"tr\": \"Dominika\", \"pt-BR\": \"Dominica\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Dominica\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '212', 'DMA', 'Dominican', 'Roseau', '.dm', 'Dominica', 'Americas', 'XCD', 'Eastern Caribbean dollar', '$', NULL, 15.42, -61.33, '🇩🇲', 'U+1F1E9 U+1F1F2', 1, 1, 0),
(63, 'Dominican Republic', 'DO', '214', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Dominican Republic\", \"cn\": \"多明尼加共和国\", \"de\": \"Dominikanische Republik\", \"es\": \"República Dominicana\", \"fa\": \"جمهوری دومینیکن\", \"fr\": \"République dominicaine\", \"hr\": \"Dominikanska Republika\", \"it\": \"Repubblica Dominicana\", \"ja\": \"ドミニカ共和国\", \"kr\": \"도미니카 공화국\", \"nl\": \"Dominicaanse Republiek\", \"pt\": \"República Dominicana\", \"tr\": \"Dominik Cumhuriyeti\", \"pt-BR\": \"República Dominicana\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Santo_Domingo\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '214', 'DOM', 'Dominican', 'Santo Domingo', '.do', 'República Dominicana', 'Americas', 'DOP', 'Dominican peso', '$', NULL, 19.00, -70.67, '🇩🇴', 'U+1F1E9 U+1F1F4', 1, 1, 0),
(64, 'Ecuador', 'EC', '218', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Ecuador\", \"cn\": \"厄瓜多尔\", \"de\": \"Ecuador\", \"es\": \"Ecuador\", \"fa\": \"اکوادور\", \"fr\": \"Équateur\", \"hr\": \"Ekvador\", \"it\": \"Ecuador\", \"ja\": \"エクアドル\", \"kr\": \"에콰도르\", \"nl\": \"Ecuador\", \"pt\": \"Equador\", \"tr\": \"Ekvator\", \"pt-BR\": \"Equador\"}', '[{\"tzName\": \"Ecuador Time\", \"zoneName\": \"America/Guayaquil\", \"gmtOffset\": -18000, \"abbreviation\": \"ECT\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Galápagos Time\", \"zoneName\": \"Pacific/Galapagos\", \"gmtOffset\": -21600, \"abbreviation\": \"GALT\", \"gmtOffsetName\": \"UTC-06:00\"}]', '218', 'ECU', 'Ecuadorian', 'Quito', '.ec', 'Ecuador', 'Americas', 'USD', 'United States dollar', '$', NULL, -2.00, -77.50, '🇪🇨', 'U+1F1EA U+1F1E8', 1, 1, 0),
(65, 'Egypt', 'EG', '818', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Egypt\", \"cn\": \"埃及\", \"de\": \"Ägypten\", \"es\": \"Egipto\", \"fa\": \"مصر\", \"fr\": \"Égypte\", \"hr\": \"Egipat\", \"it\": \"Egitto\", \"ja\": \"エジプト\", \"kr\": \"이집트\", \"nl\": \"Egypte\", \"pt\": \"Egipto\", \"tr\": \"Mısır\", \"pt-BR\": \"Egito\"}', '[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Africa/Cairo\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]', '818', 'EGY', 'Egyptian', 'Cairo', '.eg', 'مصر‎', 'Africa', 'EGP', 'Egyptian pound', 'ج.م', NULL, 27.00, 30.00, '🇪🇬', 'U+1F1EA U+1F1EC', 1, 1, 6),
(66, 'El Salvador', 'SV', '222', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"El Salvador\", \"cn\": \"萨尔瓦多\", \"de\": \"El Salvador\", \"es\": \"El Salvador\", \"fa\": \"السالوادور\", \"fr\": \"Salvador\", \"hr\": \"Salvador\", \"it\": \"El Salvador\", \"ja\": \"エルサルバドル\", \"kr\": \"엘살바도르\", \"nl\": \"El Salvador\", \"pt\": \"El Salvador\", \"tr\": \"El Salvador\", \"pt-BR\": \"El Salvador\"}', '[{\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/El_Salvador\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}]', '222', 'SLV', 'Salvadoran', 'San Salvador', '.sv', 'El Salvador', 'Americas', 'USD', 'United States dollar', '$', NULL, 13.83, -88.92, '🇸🇻', 'U+1F1F8 U+1F1FB', 1, 1, 0),
(67, 'Equatorial Guinea', 'GQ', '226', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Equatorial Guinea\", \"cn\": \"赤道几内亚\", \"de\": \"Äquatorial-Guinea\", \"es\": \"Guinea Ecuatorial\", \"fa\": \"گینه استوایی\", \"fr\": \"Guinée-Équatoriale\", \"hr\": \"Ekvatorijalna Gvineja\", \"it\": \"Guinea Equatoriale\", \"ja\": \"赤道ギニア\", \"kr\": \"적도 기니\", \"nl\": \"Equatoriaal-Guinea\", \"pt\": \"Guiné Equatorial\", \"tr\": \"Ekvator Ginesi\", \"pt-BR\": \"Guiné Equatorial\"}', '[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Malabo\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}]', '226', 'GNQ', 'Equatorial Guinean, Equatoguinean', 'Malabo', '.gq', 'Guinea Ecuatorial', 'Africa', 'XAF', 'Central African CFA franc', 'FCFA', NULL, 2.00, 10.00, '🇬🇶', 'U+1F1EC U+1F1F6', 1, 1, 0),
(68, 'Eritrea', 'ER', '232', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Eritrea\", \"cn\": \"厄立特里亚\", \"de\": \"Eritrea\", \"es\": \"Eritrea\", \"fa\": \"اریتره\", \"fr\": \"Érythrée\", \"hr\": \"Eritreja\", \"it\": \"Eritrea\", \"ja\": \"エリトリア\", \"kr\": \"에리트레아\", \"nl\": \"Eritrea\", \"pt\": \"Eritreia\", \"tr\": \"Eritre\", \"pt-BR\": \"Eritreia\"}', '[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Africa/Asmara\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]', '232', 'ERI', 'Eritrean', 'Asmara', '.er', 'ኤርትራ', 'Africa', 'ERN', 'Eritrean nakfa', 'Nfk', NULL, 15.00, 39.00, '🇪🇷', 'U+1F1EA U+1F1F7', 1, 1, 0),
(69, 'Estonia', 'EE', '233', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Estonia\", \"cn\": \"爱沙尼亚\", \"de\": \"Estland\", \"es\": \"Estonia\", \"fa\": \"استونی\", \"fr\": \"Estonie\", \"hr\": \"Estonija\", \"it\": \"Estonia\", \"ja\": \"エストニア\", \"kr\": \"에스토니아\", \"nl\": \"Estland\", \"pt\": \"Estónia\", \"tr\": \"Estonya\", \"pt-BR\": \"Estônia\"}', '[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Tallinn\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]', '233', 'EST', 'Estonian', 'Tallinn', '.ee', 'Eesti', 'Europe', 'EUR', 'Euro', '€', NULL, 59.00, 26.00, '🇪🇪', 'U+1F1EA U+1F1EA', 1, 1, 0),
(70, 'Ethiopia', 'ET', '231', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Ethiopia\", \"cn\": \"埃塞俄比亚\", \"de\": \"Äthiopien\", \"es\": \"Etiopía\", \"fa\": \"اتیوپی\", \"fr\": \"Éthiopie\", \"hr\": \"Etiopija\", \"it\": \"Etiopia\", \"ja\": \"エチオピア\", \"kr\": \"에티오피아\", \"nl\": \"Ethiopië\", \"pt\": \"Etiópia\", \"tr\": \"Etiyopya\", \"pt-BR\": \"Etiópia\"}', '[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Africa/Addis_Ababa\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]', '231', 'ETH', 'Ethiopian', 'Addis Ababa', '.et', 'ኢትዮጵያ', 'Africa', 'ETB', 'Ethiopian birr', 'Nkf', NULL, 8.00, 38.00, '🇪🇹', 'U+1F1EA U+1F1F9', 1, 1, 0),
(71, 'Falkland Islands', 'FK', '238', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Falkland Islands\", \"cn\": \"福克兰群岛\", \"de\": \"Falklandinseln\", \"es\": \"Islas Malvinas\", \"fa\": \"جزایر فالکلند\", \"fr\": \"Îles Malouines\", \"hr\": \"Falklandski Otoci\", \"it\": \"Isole Falkland o Isole Malvine\", \"ja\": \"フォークランド（マルビナス）諸島\", \"kr\": \"포클랜드 제도\", \"nl\": \"Falklandeilanden [Islas Malvinas]\", \"pt\": \"Ilhas Falkland\", \"tr\": \"Falkland Adalari\", \"pt-BR\": \"Ilhas Malvinas\"}', '[{\"tzName\": \"Falkland Islands Summer Time\", \"zoneName\": \"Atlantic/Stanley\", \"gmtOffset\": -10800, \"abbreviation\": \"FKST\", \"gmtOffsetName\": \"UTC-03:00\"}]', '238', 'FLK', 'Falkland Island', 'Stanley', '.fk', 'Falkland Islands', 'Americas', 'FKP', 'Falkland Islands pound', '£', NULL, -51.75, -59.00, '🇫🇰', 'U+1F1EB U+1F1F0', 1, 1, 0),
(72, 'Faroe Islands', 'FO', '234', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Faroe Islands\", \"cn\": \"法罗群岛\", \"de\": \"Färöer-Inseln\", \"es\": \"Islas Faroe\", \"fa\": \"جزایر فارو\", \"fr\": \"Îles Féroé\", \"hr\": \"Farski Otoci\", \"it\": \"Isole Far Oer\", \"ja\": \"フェロー諸島\", \"kr\": \"페로 제도\", \"nl\": \"Faeröer\", \"pt\": \"Ilhas Faroé\", \"tr\": \"Faroe Adalari\", \"pt-BR\": \"Ilhas Faroé\"}', '[{\"tzName\": \"Western European Time\", \"zoneName\": \"Atlantic/Faroe\", \"gmtOffset\": 0, \"abbreviation\": \"WET\", \"gmtOffsetName\": \"UTC±00\"}]', '234', 'FRO', 'Faroese', 'Torshavn', '.fo', 'Føroyar', 'Europe', 'DKK', 'Danish krone', 'Kr.', NULL, 62.00, -7.00, '🇫🇴', 'U+1F1EB U+1F1F4', 1, 1, 0),
(73, 'Fiji Islands', 'FJ', '242', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Fiji Islands\", \"cn\": \"斐济\", \"de\": \"Fidschi\", \"es\": \"Fiyi\", \"fa\": \"فیجی\", \"fr\": \"Fidji\", \"hr\": \"Fiđi\", \"it\": \"Figi\", \"ja\": \"フィジー\", \"kr\": \"피지\", \"nl\": \"Fiji\", \"pt\": \"Fiji\", \"tr\": \"Fiji\", \"pt-BR\": \"Fiji\"}', '[{\"tzName\": \"Fiji Time\", \"zoneName\": \"Pacific/Fiji\", \"gmtOffset\": 43200, \"abbreviation\": \"FJT\", \"gmtOffsetName\": \"UTC+12:00\"}]', '242', 'FJI', 'Fijian', 'Suva', '.fj', 'Fiji', 'Oceania', 'FJD', 'Fijian dollar', 'FJ$', NULL, -18.00, 175.00, '🇫🇯', 'U+1F1EB U+1F1EF', 1, 1, 0),
(74, 'Finland', 'FI', '246', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Finland\", \"cn\": \"芬兰\", \"de\": \"Finnland\", \"es\": \"Finlandia\", \"fa\": \"فنلاند\", \"fr\": \"Finlande\", \"hr\": \"Finska\", \"it\": \"Finlandia\", \"ja\": \"フィンランド\", \"kr\": \"핀란드\", \"nl\": \"Finland\", \"pt\": \"Finlândia\", \"tr\": \"Finlandiya\", \"pt-BR\": \"Finlândia\"}', '[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Helsinki\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]', '246', 'FIN', 'Finnish', 'Helsinki', '.fi', 'Suomi', 'Europe', 'EUR', 'Euro', '€', NULL, 64.00, 26.00, '🇫🇮', 'U+1F1EB U+1F1EE', 1, 1, 0),
(75, 'France', 'FR', '250', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"France\", \"cn\": \"法国\", \"de\": \"Frankreich\", \"es\": \"Francia\", \"fa\": \"فرانسه\", \"fr\": \"France\", \"hr\": \"Francuska\", \"it\": \"Francia\", \"ja\": \"フランス\", \"kr\": \"프랑스\", \"nl\": \"Frankrijk\", \"pt\": \"França\", \"tr\": \"Fransa\", \"pt-BR\": \"França\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Paris\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '250', 'FRA', 'French', 'Paris', '.fr', 'France', 'Europe', 'EUR', 'Euro', '€', NULL, 46.00, 2.00, '🇫🇷', 'U+1F1EB U+1F1F7', 1, 1, 0),
(76, 'French Guiana', 'GF', '254', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"French Guiana\", \"cn\": \"法属圭亚那\", \"de\": \"Französisch Guyana\", \"es\": \"Guayana Francesa\", \"fa\": \"گویان فرانسه\", \"fr\": \"Guayane\", \"hr\": \"Francuska Gvajana\", \"it\": \"Guyana francese\", \"ja\": \"フランス領ギアナ\", \"kr\": \"프랑스령 기아나\", \"nl\": \"Frans-Guyana\", \"pt\": \"Guiana Francesa\", \"tr\": \"Fransiz Guyanasi\", \"pt-BR\": \"Guiana Francesa\"}', '[{\"tzName\": \"French Guiana Time\", \"zoneName\": \"America/Cayenne\", \"gmtOffset\": -10800, \"abbreviation\": \"GFT\", \"gmtOffsetName\": \"UTC-03:00\"}]', '254', 'GUF', 'French Guianese', 'Cayenne', '.gf', 'Guyane française', 'Americas', 'EUR', 'Euro', '€', NULL, 4.00, -53.00, '🇬🇫', 'U+1F1EC U+1F1EB', 1, 1, 0),
(77, 'French Polynesia', 'PF', '258', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"French Polynesia\", \"cn\": \"法属波利尼西亚\", \"de\": \"Französisch-Polynesien\", \"es\": \"Polinesia Francesa\", \"fa\": \"پلی‌نزی فرانسه\", \"fr\": \"Polynésie française\", \"hr\": \"Francuska Polinezija\", \"it\": \"Polinesia Francese\", \"ja\": \"フランス領ポリネシア\", \"kr\": \"프랑스령 폴리네시아\", \"nl\": \"Frans-Polynesië\", \"pt\": \"Polinésia Francesa\", \"tr\": \"Fransiz Polinezyasi\", \"pt-BR\": \"Polinésia Francesa\"}', '[{\"tzName\": \"Gambier Islands Time\", \"zoneName\": \"Pacific/Gambier\", \"gmtOffset\": -32400, \"abbreviation\": \"GAMT\", \"gmtOffsetName\": \"UTC-09:00\"}, {\"tzName\": \"Marquesas Islands Time\", \"zoneName\": \"Pacific/Marquesas\", \"gmtOffset\": -34200, \"abbreviation\": \"MART\", \"gmtOffsetName\": \"UTC-09:30\"}, {\"tzName\": \"Tahiti Time\", \"zoneName\": \"Pacific/Tahiti\", \"gmtOffset\": -36000, \"abbreviation\": \"TAHT\", \"gmtOffsetName\": \"UTC-10:00\"}]', '258', 'PYF', 'French Polynesia', 'Papeete', '.pf', 'Polynésie française', 'Oceania', 'XPF', 'CFP franc', '₣', NULL, -15.00, -140.00, '🇵🇫', 'U+1F1F5 U+1F1EB', 1, 1, 0),
(78, 'French Southern Territories', 'TF', '260', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"French Southern Territories\", \"cn\": \"法属南部领地\", \"de\": \"Französische Süd- und Antarktisgebiete\", \"es\": \"Tierras Australes y Antárticas Francesas\", \"fa\": \"سرزمین‌های جنوبی و جنوبگانی فرانسه\", \"fr\": \"Terres australes et antarctiques françaises\", \"hr\": \"Francuski južni i antarktički teritoriji\", \"it\": \"Territori Francesi del Sud\", \"ja\": \"フランス領南方・南極地域\", \"kr\": \"프랑스령 남방 및 남극\", \"nl\": \"Franse Gebieden in de zuidelijke Indische Oceaan\", \"pt\": \"Terras Austrais e Antárticas Francesas\", \"tr\": \"Fransiz Güney Topraklari\", \"pt-BR\": \"Terras Austrais e Antárticas Francesas\"}', '[{\"tzName\": \"French Southern and Antarctic Time\", \"zoneName\": \"Indian/Kerguelen\", \"gmtOffset\": 18000, \"abbreviation\": \"TFT\", \"gmtOffsetName\": \"UTC+05:00\"}]', '260', 'ATF', 'French Southern Territories', 'Port-aux-Francais', '.tf', 'Territoire des Terres australes et antarctiques fr', 'Africa', 'EUR', 'Euro', '€', NULL, -49.25, 69.17, '🇹🇫', 'U+1F1F9 U+1F1EB', 1, 1, 0),
(79, 'Gabon', 'GA', '266', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Gabon\", \"cn\": \"加蓬\", \"de\": \"Gabun\", \"es\": \"Gabón\", \"fa\": \"گابن\", \"fr\": \"Gabon\", \"hr\": \"Gabon\", \"it\": \"Gabon\", \"ja\": \"ガボン\", \"kr\": \"가봉\", \"nl\": \"Gabon\", \"pt\": \"Gabão\", \"tr\": \"Gabon\", \"pt-BR\": \"Gabão\"}', '[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Libreville\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}]', '266', 'GAB', 'Gabonese', 'Libreville', '.ga', 'Gabon', 'Africa', 'XAF', 'Central African CFA franc', 'FCFA', NULL, -1.00, 11.75, '🇬🇦', 'U+1F1EC U+1F1E6', 1, 1, 0),
(80, 'Gambia The', 'GM', '270', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Gambia The\", \"cn\": \"冈比亚\", \"de\": \"Gambia\", \"es\": \"Gambia\", \"fa\": \"گامبیا\", \"fr\": \"Gambie\", \"hr\": \"Gambija\", \"it\": \"Gambia\", \"ja\": \"ガンビア\", \"kr\": \"감비아\", \"nl\": \"Gambia\", \"pt\": \"Gâmbia\", \"tr\": \"Gambiya\", \"pt-BR\": \"Gâmbia\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Banjul\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]', '270', 'GMB', 'Gambian', 'Banjul', '.gm', 'Gambia', 'Africa', 'GMD', 'Gambian dalasi', 'D', NULL, 13.47, -16.57, '🇬🇲', 'U+1F1EC U+1F1F2', 1, 1, 0),
(81, 'Georgia', 'GE', '268', '2020-06-17 10:37:04', '2024-01-21 12:54:28', '{\"ar\": \"Georgia\", \"cn\": \"格鲁吉亚\", \"de\": \"Georgien\", \"es\": \"Georgia\", \"fa\": \"گرجستان\", \"fr\": \"Géorgie\", \"hr\": \"Gruzija\", \"it\": \"Georgia\", \"ja\": \"グルジア\", \"kr\": \"조지아\", \"nl\": \"Georgië\", \"pt\": \"Geórgia\", \"tr\": \"Gürcistan\", \"pt-BR\": \"Geórgia\"}', '[{\"tzName\": \"Georgia Standard Time\", \"zoneName\": \"Asia/Tbilisi\", \"gmtOffset\": 14400, \"abbreviation\": \"GET\", \"gmtOffsetName\": \"UTC+04:00\"}]', '268', 'GEO', 'Georgian', 'Tbilisi', '.ge', 'საქართველო', 'Asia', 'GEL', 'Georgian lari', 'ლ', NULL, 42.00, 43.50, '🇬🇪', 'U+1F1EC U+1F1EA', 1, 1, 0),
(82, 'Germany', 'DE', '276', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Germany\", \"cn\": \"德国\", \"de\": \"Deutschland\", \"es\": \"Alemania\", \"fa\": \"آلمان\", \"fr\": \"Allemagne\", \"hr\": \"Njemačka\", \"it\": \"Germania\", \"ja\": \"ドイツ\", \"kr\": \"독일\", \"nl\": \"Duitsland\", \"pt\": \"Alemanha\", \"tr\": \"Almanya\", \"pt-BR\": \"Alemanha\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Berlin\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}, {\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Busingen\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '276', 'DEU', 'German', 'Berlin', '.de', 'Deutschland', 'Europe', 'EUR', 'Euro', '€', NULL, 51.00, 9.00, '🇩🇪', 'U+1F1E9 U+1F1EA', 1, 1, 0),
(83, 'Ghana', 'GH', '288', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Ghana\", \"cn\": \"加纳\", \"de\": \"Ghana\", \"es\": \"Ghana\", \"fa\": \"غنا\", \"fr\": \"Ghana\", \"hr\": \"Gana\", \"it\": \"Ghana\", \"ja\": \"ガーナ\", \"kr\": \"가나\", \"nl\": \"Ghana\", \"pt\": \"Gana\", \"tr\": \"Gana\", \"pt-BR\": \"Gana\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Accra\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]', '288', 'GHA', 'Ghanaian', 'Accra', '.gh', 'Ghana', 'Africa', 'GHS', 'Ghanaian cedi', 'GH₵', NULL, 8.00, -2.00, '🇬🇭', 'U+1F1EC U+1F1ED', 1, 1, 0),
(84, 'Gibraltar', 'GI', '292', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Gibraltar\", \"cn\": \"直布罗陀\", \"de\": \"Gibraltar\", \"es\": \"Gibraltar\", \"fa\": \"جبل‌طارق\", \"fr\": \"Gibraltar\", \"hr\": \"Gibraltar\", \"it\": \"Gibilterra\", \"ja\": \"ジブラルタル\", \"kr\": \"지브롤터\", \"nl\": \"Gibraltar\", \"pt\": \"Gibraltar\", \"tr\": \"Cebelitarik\", \"pt-BR\": \"Gibraltar\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Gibraltar\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '292', 'GIB', 'Gibraltar', 'Gibraltar', '.gi', 'Gibraltar', 'Europe', 'GIP', 'Gibraltar pound', '£', NULL, 36.13, -5.35, '🇬🇮', 'U+1F1EC U+1F1EE', 1, 1, 0),
(85, 'Greece', 'GR', '300', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Greece\", \"cn\": \"希腊\", \"de\": \"Griechenland\", \"es\": \"Grecia\", \"fa\": \"یونان\", \"fr\": \"Grèce\", \"hr\": \"Grčka\", \"it\": \"Grecia\", \"ja\": \"ギリシャ\", \"kr\": \"그리스\", \"nl\": \"Griekenland\", \"pt\": \"Grécia\", \"tr\": \"Yunanistan\", \"pt-BR\": \"Grécia\"}', '[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Athens\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]', '300', 'GRC', 'Greek, Hellenic', 'Athens', '.gr', 'Ελλάδα', 'Europe', 'EUR', 'Euro', '€', NULL, 39.00, 22.00, '🇬🇷', 'U+1F1EC U+1F1F7', 1, 1, 0),
(86, 'Greenland', 'GL', '304', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Greenland\", \"cn\": \"格陵兰岛\", \"de\": \"Grönland\", \"es\": \"Groenlandia\", \"fa\": \"گرینلند\", \"fr\": \"Groenland\", \"hr\": \"Grenland\", \"it\": \"Groenlandia\", \"ja\": \"グリーンランド\", \"kr\": \"그린란드\", \"nl\": \"Groenland\", \"pt\": \"Gronelândia\", \"tr\": \"Grönland\", \"pt-BR\": \"Groelândia\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"America/Danmarkshavn\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}, {\"tzName\": \"West Greenland Time\", \"zoneName\": \"America/Nuuk\", \"gmtOffset\": -10800, \"abbreviation\": \"WGT\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Eastern Greenland Time\", \"zoneName\": \"America/Scoresbysund\", \"gmtOffset\": -3600, \"abbreviation\": \"EGT\", \"gmtOffsetName\": \"UTC-01:00\"}, {\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Thule\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '304', 'GRL', 'Greenlandic', 'Nuuk', '.gl', 'Kalaallit Nunaat', 'Americas', 'DKK', 'Danish krone', 'Kr.', NULL, 72.00, -40.00, '🇬🇱', 'U+1F1EC U+1F1F1', 1, 1, 0),
(87, 'Grenada', 'GD', '308', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Grenada\", \"cn\": \"格林纳达\", \"de\": \"Grenada\", \"es\": \"Grenada\", \"fa\": \"گرنادا\", \"fr\": \"Grenade\", \"hr\": \"Grenada\", \"it\": \"Grenada\", \"ja\": \"グレナダ\", \"kr\": \"그레나다\", \"nl\": \"Grenada\", \"pt\": \"Granada\", \"tr\": \"Grenada\", \"pt-BR\": \"Granada\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Grenada\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '308', 'GRD', 'Grenadian', 'St. Georges', '.gd', 'Grenada', 'Americas', 'XCD', 'Eastern Caribbean dollar', '$', NULL, 12.12, -61.67, '🇬🇩', 'U+1F1EC U+1F1E9', 1, 1, 0),
(88, 'Guadeloupe', 'GP', '312', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Guadeloupe\", \"cn\": \"瓜德罗普岛\", \"de\": \"Guadeloupe\", \"es\": \"Guadalupe\", \"fa\": \"جزیره گوادلوپ\", \"fr\": \"Guadeloupe\", \"hr\": \"Gvadalupa\", \"it\": \"Guadeloupa\", \"ja\": \"グアドループ\", \"kr\": \"과들루프\", \"nl\": \"Guadeloupe\", \"pt\": \"Guadalupe\", \"tr\": \"Guadeloupe\", \"pt-BR\": \"Guadalupe\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Guadeloupe\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '312', 'GLP', 'Guadeloupe', 'Basse-Terre', '.gp', 'Guadeloupe', 'Americas', 'EUR', 'Euro', '€', NULL, 16.25, -61.58, '🇬🇵', 'U+1F1EC U+1F1F5', 1, 1, 0),
(89, 'Guam', 'GU', '316', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Guam\", \"cn\": \"关岛\", \"de\": \"Guam\", \"es\": \"Guam\", \"fa\": \"گوام\", \"fr\": \"Guam\", \"hr\": \"Guam\", \"it\": \"Guam\", \"ja\": \"グアム\", \"kr\": \"괌\", \"nl\": \"Guam\", \"pt\": \"Guame\", \"tr\": \"Guam\", \"pt-BR\": \"Guam\"}', '[{\"tzName\": \"Chamorro Standard Time\", \"zoneName\": \"Pacific/Guam\", \"gmtOffset\": 36000, \"abbreviation\": \"CHST\", \"gmtOffsetName\": \"UTC+10:00\"}]', '316', 'GUM', 'Guamanian, Guambat', 'Hagatna', '.gu', 'Guam', 'Oceania', 'USD', 'US Dollar', '$', NULL, 13.47, 144.78, '🇬🇺', 'U+1F1EC U+1F1FA', 1, 1, 0),
(90, 'Guatemala', 'GT', '320', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Guatemala\", \"cn\": \"危地马拉\", \"de\": \"Guatemala\", \"es\": \"Guatemala\", \"fa\": \"گواتمالا\", \"fr\": \"Guatemala\", \"hr\": \"Gvatemala\", \"it\": \"Guatemala\", \"ja\": \"グアテマラ\", \"kr\": \"과테말라\", \"nl\": \"Guatemala\", \"pt\": \"Guatemala\", \"tr\": \"Guatemala\", \"pt-BR\": \"Guatemala\"}', '[{\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Guatemala\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}]', '320', 'GTM', 'Guatemalan', 'Guatemala City', '.gt', 'Guatemala', 'Americas', 'GTQ', 'Guatemalan quetzal', 'Q', NULL, 15.50, -90.25, '🇬🇹', 'U+1F1EC U+1F1F9', 1, 1, 0),
(91, 'Guernsey and Alderney', 'GG', '831', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Guernsey and Alderney\", \"cn\": \"根西岛\", \"de\": \"Guernsey\", \"es\": \"Guernsey\", \"fa\": \"گرنزی\", \"fr\": \"Guernesey\", \"hr\": \"Guernsey\", \"it\": \"Guernsey\", \"ja\": \"ガーンジー\", \"kr\": \"건지, 올더니\", \"nl\": \"Guernsey\", \"pt\": \"Guernsey\", \"tr\": \"Alderney\", \"pt-BR\": \"Guernsey\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Europe/Guernsey\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]', '831', 'GGY', 'Channel Island', 'St Peter Port', '.gg', 'Guernsey', 'Europe', 'GBP', 'British pound', '£', NULL, 49.47, -2.58, '🇬🇬', 'U+1F1EC U+1F1EC', 1, 1, 0),
(92, 'Guinea', 'GN', '324', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Guinea\", \"cn\": \"几内亚\", \"de\": \"Guinea\", \"es\": \"Guinea\", \"fa\": \"گینه\", \"fr\": \"Guinée\", \"hr\": \"Gvineja\", \"it\": \"Guinea\", \"ja\": \"ギニア\", \"kr\": \"기니\", \"nl\": \"Guinee\", \"pt\": \"Guiné\", \"tr\": \"Gine\", \"pt-BR\": \"Guiné\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Conakry\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]', '324', 'GIN', 'Guinean', 'Conakry', '.gn', 'Guinée', 'Africa', 'GNF', 'Guinean franc', 'FG', NULL, 11.00, -10.00, '🇬🇳', 'U+1F1EC U+1F1F3', 1, 1, 0),
(93, 'Guinea-Bissau', 'GW', '624', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Guinea-Bissau\", \"cn\": \"几内亚比绍\", \"de\": \"Guinea-Bissau\", \"es\": \"Guinea-Bisáu\", \"fa\": \"گینه بیسائو\", \"fr\": \"Guinée-Bissau\", \"hr\": \"Gvineja Bisau\", \"it\": \"Guinea-Bissau\", \"ja\": \"ギニアビサウ\", \"kr\": \"기니비사우\", \"nl\": \"Guinee-Bissau\", \"pt\": \"Guiné-Bissau\", \"tr\": \"Gine-bissau\", \"pt-BR\": \"Guiné-Bissau\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Bissau\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]', '624', 'GNB', 'Bissau-Guinean', 'Bissau', '.gw', 'Guiné-Bissau', 'Africa', 'XOF', 'West African CFA franc', 'CFA', NULL, 12.00, -15.00, '🇬🇼', 'U+1F1EC U+1F1FC', 1, 1, 0),
(94, 'Guyana', 'GY', '328', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Guyana\", \"cn\": \"圭亚那\", \"de\": \"Guyana\", \"es\": \"Guyana\", \"fa\": \"گویان\", \"fr\": \"Guyane\", \"hr\": \"Gvajana\", \"it\": \"Guyana\", \"ja\": \"ガイアナ\", \"kr\": \"가이아나\", \"nl\": \"Guyana\", \"pt\": \"Guiana\", \"tr\": \"Guyana\", \"pt-BR\": \"Guiana\"}', '[{\"tzName\": \"Guyana Time\", \"zoneName\": \"America/Guyana\", \"gmtOffset\": -14400, \"abbreviation\": \"GYT\", \"gmtOffsetName\": \"UTC-04:00\"}]', '328', 'GUY', 'Guyanese', 'Georgetown', '.gy', 'Guyana', 'Americas', 'GYD', 'Guyanese dollar', '$', NULL, 5.00, -59.00, '🇬🇾', 'U+1F1EC U+1F1FE', 1, 1, 0),
(95, 'Haiti', 'HT', '332', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Haiti\", \"cn\": \"海地\", \"de\": \"Haiti\", \"es\": \"Haiti\", \"fa\": \"هائیتی\", \"fr\": \"Haïti\", \"hr\": \"Haiti\", \"it\": \"Haiti\", \"ja\": \"ハイチ\", \"kr\": \"아이티\", \"nl\": \"Haïti\", \"pt\": \"Haiti\", \"tr\": \"Haiti\", \"pt-BR\": \"Haiti\"}', '[{\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Port-au-Prince\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}]', '332', 'HTI', 'Haitian', 'Port-au-Prince', '.ht', 'Haïti', 'Americas', 'HTG', 'Haitian gourde', 'G', NULL, 19.00, -72.42, '🇭🇹', 'U+1F1ED U+1F1F9', 1, 1, 0),
(96, 'Heard Island and McDonald Islands', 'HM', '334', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Heard Island and McDonald Islands\", \"cn\": \"赫德·唐纳岛及麦唐纳岛\", \"de\": \"Heard und die McDonaldinseln\", \"es\": \"Islas Heard y McDonald\", \"fa\": \"جزیره هرد و جزایر مک‌دونالد\", \"fr\": \"Îles Heard-et-MacDonald\", \"hr\": \"Otok Heard i otočje McDonald\", \"it\": \"Isole Heard e McDonald\", \"ja\": \"ハード島とマクドナルド諸島\", \"kr\": \"허드 맥도날드 제도\", \"nl\": \"Heard- en McDonaldeilanden\", \"pt\": \"Ilha Heard e Ilhas McDonald\", \"tr\": \"Heard Adasi Ve Mcdonald Adalari\", \"pt-BR\": \"Ilha Heard e Ilhas McDonald\"}', '[{\"tzName\": \"French Southern and Antarctic Time\", \"zoneName\": \"Indian/Kerguelen\", \"gmtOffset\": 18000, \"abbreviation\": \"TFT\", \"gmtOffsetName\": \"UTC+05:00\"}]', '334', 'HMD', 'Heard Island or McDonald Islands', '', '.hm', 'Heard Island and McDonald Islands', '', 'AUD', 'Australian dollar', '$', NULL, -53.10, 72.52, '🇭🇲', 'U+1F1ED U+1F1F2', 1, 1, 0),
(97, 'Vatican City State (Holy See)', 'VA', '336', '2020-06-17 10:37:04', '2024-01-21 12:54:30', '{\"ar\": \"Vatican City State (Holy See)\", \"cn\": \"梵蒂冈\", \"de\": \"Heiliger Stuhl\", \"es\": \"Santa Sede\", \"fa\": \"سریر مقدس\", \"fr\": \"voir Saint\", \"hr\": \"Sveta Stolica\", \"it\": \"Santa Sede\", \"ja\": \"聖座\", \"kr\": \"바티칸 시국\", \"nl\": \"Heilige Stoel\", \"pt\": \"Vaticano\", \"tr\": \"Vatikan\", \"pt-BR\": \"Vaticano\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Vatican\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '336', 'VAT', 'Vatican', 'Vatican City', '.va', 'Vaticano', 'Europe', 'EUR', 'Euro', '€', NULL, 41.90, 12.45, '🇻🇦', 'U+1F1FB U+1F1E6', 1, 1, 0),
(98, 'Honduras', 'HN', '340', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Honduras\", \"cn\": \"洪都拉斯\", \"de\": \"Honduras\", \"es\": \"Honduras\", \"fa\": \"هندوراس\", \"fr\": \"Honduras\", \"hr\": \"Honduras\", \"it\": \"Honduras\", \"ja\": \"ホンジュラス\", \"kr\": \"온두라스\", \"nl\": \"Honduras\", \"pt\": \"Honduras\", \"tr\": \"Honduras\", \"pt-BR\": \"Honduras\"}', '[{\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Tegucigalpa\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}]', '340', 'HND', 'Honduran', 'Tegucigalpa', '.hn', 'Honduras', 'Americas', 'HNL', 'Honduran lempira', 'L', NULL, 15.00, -86.50, '🇭🇳', 'U+1F1ED U+1F1F3', 1, 1, 0),
(99, 'Hong Kong S.A.R.', 'HK', '344', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Hong Kong S.A.R.\", \"cn\": \"中国香港\", \"de\": \"Hong Kong\", \"es\": \"Hong Kong\", \"fa\": \"هنگ‌کنگ\", \"fr\": \"Hong Kong\", \"hr\": \"Hong Kong\", \"it\": \"Hong Kong\", \"ja\": \"香港\", \"kr\": \"홍콩\", \"nl\": \"Hongkong\", \"pt\": \"Hong Kong\", \"tr\": \"Hong Kong\", \"pt-BR\": \"Hong Kong\"}', '[{\"tzName\": \"Hong Kong Time\", \"zoneName\": \"Asia/Hong_Kong\", \"gmtOffset\": 28800, \"abbreviation\": \"HKT\", \"gmtOffsetName\": \"UTC+08:00\"}]', '344', 'HKG', 'Hong Kong, Hong Kongese', 'Hong Kong', '.hk', '香港', 'Asia', 'HKD', 'Hong Kong dollar', '$', NULL, 22.25, 114.17, '🇭🇰', 'U+1F1ED U+1F1F0', 1, 1, 0),
(100, 'Hungary', 'HU', '348', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Hungary\", \"cn\": \"匈牙利\", \"de\": \"Ungarn\", \"es\": \"Hungría\", \"fa\": \"مجارستان\", \"fr\": \"Hongrie\", \"hr\": \"Mađarska\", \"it\": \"Ungheria\", \"ja\": \"ハンガリー\", \"kr\": \"헝가리\", \"nl\": \"Hongarije\", \"pt\": \"Hungria\", \"tr\": \"Macaristan\", \"pt-BR\": \"Hungria\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Budapest\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '348', 'HUN', 'Hungarian, Magyar', 'Budapest', '.hu', 'Magyarország', 'Europe', 'HUF', 'Hungarian forint', 'Ft', NULL, 47.00, 20.00, '🇭🇺', 'U+1F1ED U+1F1FA', 1, 1, 0),
(101, 'Iceland', 'IS', '352', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Iceland\", \"cn\": \"冰岛\", \"de\": \"Island\", \"es\": \"Islandia\", \"fa\": \"ایسلند\", \"fr\": \"Islande\", \"hr\": \"Island\", \"it\": \"Islanda\", \"ja\": \"アイスランド\", \"kr\": \"아이슬란드\", \"nl\": \"IJsland\", \"pt\": \"Islândia\", \"tr\": \"İzlanda\", \"pt-BR\": \"Islândia\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Atlantic/Reykjavik\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]', '352', 'ISL', 'Icelandic', 'Reykjavik', '.is', 'Ísland', 'Europe', 'ISK', 'Icelandic króna', 'kr', NULL, 65.00, -18.00, '🇮🇸', 'U+1F1EE U+1F1F8', 1, 1, 0),
(102, 'India', 'IN', '356', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"India\", \"cn\": \"印度\", \"de\": \"Indien\", \"es\": \"India\", \"fa\": \"هند\", \"fr\": \"Inde\", \"hr\": \"Indija\", \"it\": \"India\", \"ja\": \"インド\", \"kr\": \"인도\", \"nl\": \"India\", \"pt\": \"Índia\", \"tr\": \"Hindistan\", \"pt-BR\": \"Índia\"}', '[{\"tzName\": \"Indian Standard Time\", \"zoneName\": \"Asia/Kolkata\", \"gmtOffset\": 19800, \"abbreviation\": \"IST\", \"gmtOffsetName\": \"UTC+05:30\"}]', '356', 'IND', 'Indian', 'New Delhi', '.in', 'भारत', 'Asia', 'INR', 'Indian rupee', '₹', NULL, 20.00, 77.00, '🇮🇳', 'U+1F1EE U+1F1F3', 1, 1, 0),
(103, 'Indonesia', 'ID', '360', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Indonesia\", \"cn\": \"印度尼西亚\", \"de\": \"Indonesien\", \"es\": \"Indonesia\", \"fa\": \"اندونزی\", \"fr\": \"Indonésie\", \"hr\": \"Indonezija\", \"it\": \"Indonesia\", \"ja\": \"インドネシア\", \"kr\": \"인도네시아\", \"nl\": \"Indonesië\", \"pt\": \"Indonésia\", \"tr\": \"Endonezya\", \"pt-BR\": \"Indonésia\"}', '[{\"tzName\": \"Western Indonesian Time\", \"zoneName\": \"Asia/Jakarta\", \"gmtOffset\": 25200, \"abbreviation\": \"WIB\", \"gmtOffsetName\": \"UTC+07:00\"}, {\"tzName\": \"Eastern Indonesian Time\", \"zoneName\": \"Asia/Jayapura\", \"gmtOffset\": 32400, \"abbreviation\": \"WIT\", \"gmtOffsetName\": \"UTC+09:00\"}, {\"tzName\": \"Central Indonesia Time\", \"zoneName\": \"Asia/Makassar\", \"gmtOffset\": 28800, \"abbreviation\": \"WITA\", \"gmtOffsetName\": \"UTC+08:00\"}, {\"tzName\": \"Western Indonesian Time\", \"zoneName\": \"Asia/Pontianak\", \"gmtOffset\": 25200, \"abbreviation\": \"WIB\", \"gmtOffsetName\": \"UTC+07:00\"}]', '360', 'IDN', 'Indonesian', 'Jakarta', '.id', 'Indonesia', 'Asia', 'IDR', 'Indonesian rupiah', 'Rp', NULL, -5.00, 120.00, '🇮🇩', 'U+1F1EE U+1F1E9', 1, 1, 0),
(104, 'Iran', 'IR', '364', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Iran\", \"cn\": \"伊朗\", \"de\": \"Iran\", \"es\": \"Iran\", \"fa\": \"ایران\", \"fr\": \"Iran\", \"hr\": \"Iran\", \"ja\": \"イラン・イスラム共和国\", \"kr\": \"이란\", \"nl\": \"Iran\", \"pt\": \"Irão\", \"tr\": \"İran\", \"pt-BR\": \"Irã\"}', '[{\"tzName\": \"Iran Daylight Time\", \"zoneName\": \"Asia/Tehran\", \"gmtOffset\": 12600, \"abbreviation\": \"IRDT\", \"gmtOffsetName\": \"UTC+03:30\"}]', '364', 'IRN', 'Iranian, Persian', 'Tehran', '.ir', 'ایران', 'Asia', 'IRR', 'Iranian rial', '﷼', NULL, 32.00, 53.00, '🇮🇷', 'U+1F1EE U+1F1F7', 1, 1, 0),
(105, 'Iraq', 'IQ', '368', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Iraq\", \"cn\": \"伊拉克\", \"de\": \"Irak\", \"es\": \"Irak\", \"fa\": \"عراق\", \"fr\": \"Irak\", \"hr\": \"Irak\", \"it\": \"Iraq\", \"ja\": \"イラク\", \"kr\": \"이라크\", \"nl\": \"Irak\", \"pt\": \"Iraque\", \"tr\": \"Irak\", \"pt-BR\": \"Iraque\"}', '[{\"tzName\": \"Arabia Standard Time\", \"zoneName\": \"Asia/Baghdad\", \"gmtOffset\": 10800, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC+03:00\"}]', '368', 'IRQ', 'Iraqi', 'Baghdad', '.iq', 'العراق', 'Asia', 'IQD', 'Iraqi dinar', 'د.ع', NULL, 33.00, 44.00, '🇮🇶', 'U+1F1EE U+1F1F6', 1, 1, 0),
(106, 'Ireland', 'IE', '372', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Ireland\", \"cn\": \"爱尔兰\", \"de\": \"Irland\", \"es\": \"Irlanda\", \"fa\": \"ایرلند\", \"fr\": \"Irlande\", \"hr\": \"Irska\", \"it\": \"Irlanda\", \"ja\": \"アイルランド\", \"kr\": \"아일랜드\", \"nl\": \"Ierland\", \"pt\": \"Irlanda\", \"tr\": \"İrlanda\", \"pt-BR\": \"Irlanda\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Europe/Dublin\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]', '372', 'IRL', 'Irish', 'Dublin', '.ie', 'Éire', 'Europe', 'EUR', 'Euro', '€', NULL, 53.00, -8.00, '🇮🇪', 'U+1F1EE U+1F1EA', 1, 1, 0),
(107, 'Man (Isle of)', 'IM', '833', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Man (Isle of)\", \"cn\": \"马恩岛\", \"de\": \"Insel Man\", \"es\": \"Isla de Man\", \"fa\": \"جزیره من\", \"fr\": \"Île de Man\", \"hr\": \"Otok Man\", \"it\": \"Isola di Man\", \"ja\": \"マン島\", \"kr\": \"맨 섬\", \"nl\": \"Isle of Man\", \"pt\": \"Ilha de Man\", \"tr\": \"Man Adasi\", \"pt-BR\": \"Ilha de Man\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Europe/Isle_of_Man\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]', '833', 'IMN', 'Manx', 'Douglas, Isle of Man', '.im', 'Isle of Man', 'Europe', 'GBP', 'British pound', '£', NULL, 54.25, -4.50, '🇮🇲', 'U+1F1EE U+1F1F2', 1, 1, 0),
(108, 'Israel', 'IL', '376', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Israel\", \"cn\": \"以色列\", \"de\": \"Israel\", \"es\": \"Israel\", \"fa\": \"اسرائیل\", \"fr\": \"Israël\", \"hr\": \"Izrael\", \"it\": \"Israele\", \"ja\": \"イスラエル\", \"kr\": \"이스라엘\", \"nl\": \"Israël\", \"pt\": \"Israel\", \"tr\": \"İsrail\", \"pt-BR\": \"Israel\"}', '[{\"tzName\": \"Israel Standard Time\", \"zoneName\": \"Asia/Jerusalem\", \"gmtOffset\": 7200, \"abbreviation\": \"IST\", \"gmtOffsetName\": \"UTC+02:00\"}]', '376', 'ISR', 'Israeli', 'Jerusalem', '.il', 'יִשְׂרָאֵל', 'Asia', 'ILS', 'Israeli new shekel', '₪', NULL, 31.50, 34.75, '🇮🇱', 'U+1F1EE U+1F1F1', 1, 1, 0),
(109, 'Italy', 'IT', '380', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Italy\", \"cn\": \"意大利\", \"de\": \"Italien\", \"es\": \"Italia\", \"fa\": \"ایتالیا\", \"fr\": \"Italie\", \"hr\": \"Italija\", \"it\": \"Italia\", \"ja\": \"イタリア\", \"kr\": \"이탈리아\", \"nl\": \"Italië\", \"pt\": \"Itália\", \"tr\": \"İtalya\", \"pt-BR\": \"Itália\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Rome\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '380', 'ITA', 'Italian', 'Rome', '.it', 'Italia', 'Europe', 'EUR', 'Euro', '€', NULL, 42.83, 12.83, '🇮🇹', 'U+1F1EE U+1F1F9', 1, 1, 0),
(110, 'Jamaica', 'JM', '388', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Jamaica\", \"cn\": \"牙买加\", \"de\": \"Jamaika\", \"es\": \"Jamaica\", \"fa\": \"جامائیکا\", \"fr\": \"Jamaïque\", \"hr\": \"Jamajka\", \"it\": \"Giamaica\", \"ja\": \"ジャマイカ\", \"kr\": \"자메이카\", \"nl\": \"Jamaica\", \"pt\": \"Jamaica\", \"tr\": \"Jamaika\", \"pt-BR\": \"Jamaica\"}', '[{\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Jamaica\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}]', '388', 'JAM', 'Jamaican', 'Kingston', '.jm', 'Jamaica', 'Americas', 'JMD', 'Jamaican dollar', 'J$', NULL, 18.25, -77.50, '🇯🇲', 'U+1F1EF U+1F1F2', 1, 1, 0),
(111, 'Japan', 'JP', '392', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Japan\", \"cn\": \"日本\", \"de\": \"Japan\", \"es\": \"Japón\", \"fa\": \"ژاپن\", \"fr\": \"Japon\", \"hr\": \"Japan\", \"it\": \"Giappone\", \"ja\": \"日本\", \"kr\": \"일본\", \"nl\": \"Japan\", \"pt\": \"Japão\", \"tr\": \"Japonya\", \"pt-BR\": \"Japão\"}', '[{\"tzName\": \"Japan Standard Time\", \"zoneName\": \"Asia/Tokyo\", \"gmtOffset\": 32400, \"abbreviation\": \"JST\", \"gmtOffsetName\": \"UTC+09:00\"}]', '392', 'JPN', 'Japanese', 'Tokyo', '.jp', '日本', 'Asia', 'JPY', 'Japanese yen', '¥', NULL, 36.00, 138.00, '🇯🇵', 'U+1F1EF U+1F1F5', 1, 1, 0),
(112, 'Jersey', 'JE', '832', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Jersey\", \"cn\": \"泽西岛\", \"de\": \"Jersey\", \"es\": \"Jersey\", \"fa\": \"جرزی\", \"fr\": \"Jersey\", \"hr\": \"Jersey\", \"it\": \"Isola di Jersey\", \"ja\": \"ジャージー\", \"kr\": \"저지 섬\", \"nl\": \"Jersey\", \"pt\": \"Jersey\", \"tr\": \"Jersey\", \"pt-BR\": \"Jersey\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Europe/Jersey\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]', '832', 'JEY', 'Channel Island', 'Saint Helier', '.je', 'Jersey', 'Europe', 'GBP', 'British pound', '£', NULL, 49.25, -2.17, '🇯🇪', 'U+1F1EF U+1F1EA', 1, 1, 0),
(113, 'Jordan', 'JO', '400', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Jordan\", \"cn\": \"约旦\", \"de\": \"Jordanien\", \"es\": \"Jordania\", \"fa\": \"اردن\", \"fr\": \"Jordanie\", \"hr\": \"Jordan\", \"it\": \"Giordania\", \"ja\": \"ヨルダン\", \"kr\": \"요르단\", \"nl\": \"Jordanië\", \"pt\": \"Jordânia\", \"tr\": \"Ürdün\", \"pt-BR\": \"Jordânia\"}', '[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Asia/Amman\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]', '400', 'JOR', 'Jordanian', 'Amman', '.jo', 'الأردن', 'Asia', 'JOD', 'Jordanian dinar', 'ا.د', NULL, 31.00, 36.00, '🇯🇴', 'U+1F1EF U+1F1F4', 1, 1, 0),
(114, 'Kazakhstan', 'KZ', '398', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Kazakhstan\", \"cn\": \"哈萨克斯坦\", \"de\": \"Kasachstan\", \"es\": \"Kazajistán\", \"fa\": \"قزاقستان\", \"fr\": \"Kazakhstan\", \"hr\": \"Kazahstan\", \"it\": \"Kazakistan\", \"ja\": \"カザフスタン\", \"kr\": \"카자흐스탄\", \"nl\": \"Kazachstan\", \"pt\": \"Cazaquistão\", \"tr\": \"Kazakistan\", \"pt-BR\": \"Cazaquistão\"}', '[{\"tzName\": \"Alma-Ata Time[1\", \"zoneName\": \"Asia/Almaty\", \"gmtOffset\": 21600, \"abbreviation\": \"ALMT\", \"gmtOffsetName\": \"UTC+06:00\"}, {\"tzName\": \"Aqtobe Time\", \"zoneName\": \"Asia/Aqtau\", \"gmtOffset\": 18000, \"abbreviation\": \"AQTT\", \"gmtOffsetName\": \"UTC+05:00\"}, {\"tzName\": \"Aqtobe Time\", \"zoneName\": \"Asia/Aqtobe\", \"gmtOffset\": 18000, \"abbreviation\": \"AQTT\", \"gmtOffsetName\": \"UTC+05:00\"}, {\"tzName\": \"Moscow Daylight Time+1\", \"zoneName\": \"Asia/Atyrau\", \"gmtOffset\": 18000, \"abbreviation\": \"MSD+1\", \"gmtOffsetName\": \"UTC+05:00\"}, {\"tzName\": \"Oral Time\", \"zoneName\": \"Asia/Oral\", \"gmtOffset\": 18000, \"abbreviation\": \"ORAT\", \"gmtOffsetName\": \"UTC+05:00\"}, {\"tzName\": \"Qyzylorda Summer Time\", \"zoneName\": \"Asia/Qostanay\", \"gmtOffset\": 21600, \"abbreviation\": \"QYZST\", \"gmtOffsetName\": \"UTC+06:00\"}, {\"tzName\": \"Qyzylorda Summer Time\", \"zoneName\": \"Asia/Qyzylorda\", \"gmtOffset\": 18000, \"abbreviation\": \"QYZT\", \"gmtOffsetName\": \"UTC+05:00\"}]', '398', 'KAZ', 'Kazakhstani, Kazakh', 'Astana', '.kz', 'Қазақстан', 'Asia', 'KZT', 'Kazakhstani tenge', 'лв', NULL, 48.00, 68.00, '🇰🇿', 'U+1F1F0 U+1F1FF', 1, 1, 0),
(115, 'Kenya', 'KE', '404', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Kenya\", \"cn\": \"肯尼亚\", \"de\": \"Kenia\", \"es\": \"Kenia\", \"fa\": \"کنیا\", \"fr\": \"Kenya\", \"hr\": \"Kenija\", \"it\": \"Kenya\", \"ja\": \"ケニア\", \"kr\": \"케냐\", \"nl\": \"Kenia\", \"pt\": \"Quénia\", \"tr\": \"Kenya\", \"pt-BR\": \"Quênia\"}', '[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Africa/Nairobi\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]', '404', 'KEN', 'Kenyan', 'Nairobi', '.ke', 'Kenya', 'Africa', 'KES', 'Kenyan shilling', 'KSh', NULL, 1.00, 38.00, '🇰🇪', 'U+1F1F0 U+1F1EA', 1, 1, 0),
(116, 'Kiribati', 'KI', '296', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Kiribati\", \"cn\": \"基里巴斯\", \"de\": \"Kiribati\", \"es\": \"Kiribati\", \"fa\": \"کیریباتی\", \"fr\": \"Kiribati\", \"hr\": \"Kiribati\", \"it\": \"Kiribati\", \"ja\": \"キリバス\", \"kr\": \"키리바시\", \"nl\": \"Kiribati\", \"pt\": \"Quiribáti\", \"tr\": \"Kiribati\", \"pt-BR\": \"Kiribati\"}', '[{\"tzName\": \"Phoenix Island Time\", \"zoneName\": \"Pacific/Enderbury\", \"gmtOffset\": 46800, \"abbreviation\": \"PHOT\", \"gmtOffsetName\": \"UTC+13:00\"}, {\"tzName\": \"Line Islands Time\", \"zoneName\": \"Pacific/Kiritimati\", \"gmtOffset\": 50400, \"abbreviation\": \"LINT\", \"gmtOffsetName\": \"UTC+14:00\"}, {\"tzName\": \"Gilbert Island Time\", \"zoneName\": \"Pacific/Tarawa\", \"gmtOffset\": 43200, \"abbreviation\": \"GILT\", \"gmtOffsetName\": \"UTC+12:00\"}]', '296', 'KIR', 'I-Kiribati', 'Tarawa', '.ki', 'Kiribati', 'Oceania', 'AUD', 'Australian dollar', '$', NULL, 1.42, 173.00, '🇰🇮', 'U+1F1F0 U+1F1EE', 1, 1, 0),
(117, 'North Korea', 'KP', '408', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"North Korea\", \"cn\": \"朝鲜\", \"de\": \"Nordkorea\", \"es\": \"Corea del Norte\", \"fa\": \"کره جنوبی\", \"fr\": \"Corée du Nord\", \"hr\": \"Sjeverna Koreja\", \"it\": \"Corea del Nord\", \"ja\": \"朝鮮民主主義人民共和国\", \"kr\": \"조선민주주의인민공화국\", \"nl\": \"Noord-Korea\", \"pt\": \"Coreia do Norte\", \"tr\": \"Kuzey Kore\", \"pt-BR\": \"Coreia do Norte\"}', '[{\"tzName\": \"Korea Standard Time\", \"zoneName\": \"Asia/Pyongyang\", \"gmtOffset\": 32400, \"abbreviation\": \"KST\", \"gmtOffsetName\": \"UTC+09:00\"}]', '408', 'PRK', 'North Korean', 'Pyongyang', '.kp', '북한', 'Asia', 'KPW', 'North Korean Won', '₩', NULL, 40.00, 127.00, '🇰🇵', 'U+1F1F0 U+1F1F5', 1, 1, 0),
(118, 'South Korea', 'KR', '410', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"South Korea\", \"cn\": \"韩国\", \"de\": \"Südkorea\", \"es\": \"Corea del Sur\", \"fa\": \"کره شمالی\", \"fr\": \"Corée du Sud\", \"hr\": \"Južna Koreja\", \"it\": \"Corea del Sud\", \"ja\": \"大韓民国\", \"kr\": \"대한민국\", \"nl\": \"Zuid-Korea\", \"pt\": \"Coreia do Sul\", \"tr\": \"Güney Kore\", \"pt-BR\": \"Coreia do Sul\"}', '[{\"tzName\": \"Korea Standard Time\", \"zoneName\": \"Asia/Seoul\", \"gmtOffset\": 32400, \"abbreviation\": \"KST\", \"gmtOffsetName\": \"UTC+09:00\"}]', '410', 'KOR', 'South Korean', 'Seoul', '.kr', '대한민국', 'Asia', 'KRW', 'Won', '₩', NULL, 37.00, 127.50, '🇰🇷', 'U+1F1F0 U+1F1F7', 1, 1, 0),
(119, 'Kuwait', 'KW', '414', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Kuwait\", \"cn\": \"科威特\", \"de\": \"Kuwait\", \"es\": \"Kuwait\", \"fa\": \"کویت\", \"fr\": \"Koweït\", \"hr\": \"Kuvajt\", \"it\": \"Kuwait\", \"ja\": \"クウェート\", \"kr\": \"쿠웨이트\", \"nl\": \"Koeweit\", \"pt\": \"Kuwait\", \"tr\": \"Kuveyt\", \"pt-BR\": \"Kuwait\"}', '[{\"tzName\": \"Arabia Standard Time\", \"zoneName\": \"Asia/Kuwait\", \"gmtOffset\": 10800, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC+03:00\"}]', '414', 'KWT', 'Kuwaiti', 'Kuwait City', '.kw', 'الكويت', 'Asia', 'KWD', 'Kuwaiti dinar', 'ك.د', NULL, 29.50, 45.75, '🇰🇼', 'U+1F1F0 U+1F1FC', 1, 1, 4),
(120, 'Kyrgyzstan', 'KG', '417', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Kyrgyzstan\", \"cn\": \"吉尔吉斯斯坦\", \"de\": \"Kirgisistan\", \"es\": \"Kirguizistán\", \"fa\": \"قرقیزستان\", \"fr\": \"Kirghizistan\", \"hr\": \"Kirgistan\", \"it\": \"Kirghizistan\", \"ja\": \"キルギス\", \"kr\": \"키르기스스탄\", \"nl\": \"Kirgizië\", \"pt\": \"Quirguizistão\", \"tr\": \"Kirgizistan\", \"pt-BR\": \"Quirguistão\"}', '[{\"tzName\": \"Kyrgyzstan Time\", \"zoneName\": \"Asia/Bishkek\", \"gmtOffset\": 21600, \"abbreviation\": \"KGT\", \"gmtOffsetName\": \"UTC+06:00\"}]', '417', 'KGZ', 'Kyrgyzstani, Kyrgyz, Kirgiz, Kirghiz', 'Bishkek', '.kg', 'Кыргызстан', 'Asia', 'KGS', 'Kyrgyzstani som', 'лв', NULL, 41.00, 75.00, '🇰🇬', 'U+1F1F0 U+1F1EC', 1, 1, 0),
(121, 'Laos', 'LA', '418', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Laos\", \"cn\": \"寮人民民主共和国\", \"de\": \"Laos\", \"es\": \"Laos\", \"fa\": \"لائوس\", \"fr\": \"Laos\", \"hr\": \"Laos\", \"it\": \"Laos\", \"ja\": \"ラオス人民民主共和国\", \"kr\": \"라오스\", \"nl\": \"Laos\", \"pt\": \"Laos\", \"tr\": \"Laos\", \"pt-BR\": \"Laos\"}', '[{\"tzName\": \"Indochina Time\", \"zoneName\": \"Asia/Vientiane\", \"gmtOffset\": 25200, \"abbreviation\": \"ICT\", \"gmtOffsetName\": \"UTC+07:00\"}]', '418', 'LAO', 'Lao, Laotian', 'Vientiane', '.la', 'ສປປລາວ', 'Asia', 'LAK', 'Lao kip', '₭', NULL, 18.00, 105.00, '🇱🇦', 'U+1F1F1 U+1F1E6', 1, 1, 0),
(122, 'Latvia', 'LV', '428', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Latvia\", \"cn\": \"拉脱维亚\", \"de\": \"Lettland\", \"es\": \"Letonia\", \"fa\": \"لتونی\", \"fr\": \"Lettonie\", \"hr\": \"Latvija\", \"it\": \"Lettonia\", \"ja\": \"ラトビア\", \"kr\": \"라트비아\", \"nl\": \"Letland\", \"pt\": \"Letónia\", \"tr\": \"Letonya\", \"pt-BR\": \"Letônia\"}', '[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Riga\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]', '428', 'LVA', 'Latvian', 'Riga', '.lv', 'Latvija', 'Europe', 'EUR', 'Euro', '€', NULL, 57.00, 25.00, '🇱🇻', 'U+1F1F1 U+1F1FB', 1, 1, 0),
(123, 'Lebanon', 'LB', '422', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Lebanon\", \"cn\": \"黎巴嫩\", \"de\": \"Libanon\", \"es\": \"Líbano\", \"fa\": \"لبنان\", \"fr\": \"Liban\", \"hr\": \"Libanon\", \"it\": \"Libano\", \"ja\": \"レバノン\", \"kr\": \"레바논\", \"nl\": \"Libanon\", \"pt\": \"Líbano\", \"tr\": \"Lübnan\", \"pt-BR\": \"Líbano\"}', '[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Asia/Beirut\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]', '422', 'LBN', 'Lebanese', 'Beirut', '.lb', 'لبنان', 'Asia', 'LBP', 'Lebanese pound', '£', NULL, 33.83, 35.83, '🇱🇧', 'U+1F1F1 U+1F1E7', 1, 1, 7),
(124, 'Lesotho', 'LS', '426', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Lesotho\", \"cn\": \"莱索托\", \"de\": \"Lesotho\", \"es\": \"Lesotho\", \"fa\": \"لسوتو\", \"fr\": \"Lesotho\", \"hr\": \"Lesoto\", \"it\": \"Lesotho\", \"ja\": \"レソト\", \"kr\": \"레소토\", \"nl\": \"Lesotho\", \"pt\": \"Lesoto\", \"tr\": \"Lesotho\", \"pt-BR\": \"Lesoto\"}', '[{\"tzName\": \"South African Standard Time\", \"zoneName\": \"Africa/Maseru\", \"gmtOffset\": 7200, \"abbreviation\": \"SAST\", \"gmtOffsetName\": \"UTC+02:00\"}]', '426', 'LSO', 'Basotho', 'Maseru', '.ls', 'Lesotho', 'Africa', 'LSL', 'Lesotho loti', 'L', NULL, -29.50, 28.50, '🇱🇸', 'U+1F1F1 U+1F1F8', 1, 1, 0),
(125, 'Liberia', 'LR', '430', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Liberia\", \"cn\": \"利比里亚\", \"de\": \"Liberia\", \"es\": \"Liberia\", \"fa\": \"لیبریا\", \"fr\": \"Liberia\", \"hr\": \"Liberija\", \"it\": \"Liberia\", \"ja\": \"リベリア\", \"kr\": \"라이베리아\", \"nl\": \"Liberia\", \"pt\": \"Libéria\", \"tr\": \"Liberya\", \"pt-BR\": \"Libéria\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Monrovia\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]', '430', 'LBR', 'Liberian', 'Monrovia', '.lr', 'Liberia', 'Africa', 'LRD', 'Liberian dollar', '$', NULL, 6.50, -9.50, '🇱🇷', 'U+1F1F1 U+1F1F7', 1, 1, 0),
(126, 'Libya', 'LY', '434', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Libya\", \"cn\": \"利比亚\", \"de\": \"Libyen\", \"es\": \"Libia\", \"fa\": \"لیبی\", \"fr\": \"Libye\", \"hr\": \"Libija\", \"it\": \"Libia\", \"ja\": \"リビア\", \"kr\": \"리비아\", \"nl\": \"Libië\", \"pt\": \"Líbia\", \"tr\": \"Libya\", \"pt-BR\": \"Líbia\"}', '[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Africa/Tripoli\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]', '434', 'LBY', 'Libyan', 'Tripolis', '.ly', '‏ليبيا', 'Africa', 'LYD', 'Libyan dinar', 'د.ل', NULL, 25.00, 17.00, '🇱🇾', 'U+1F1F1 U+1F1FE', 1, 1, 0),
(127, 'Liechtenstein', 'LI', '438', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Liechtenstein\", \"cn\": \"列支敦士登\", \"de\": \"Liechtenstein\", \"es\": \"Liechtenstein\", \"fa\": \"لیختن‌اشتاین\", \"fr\": \"Liechtenstein\", \"hr\": \"Lihtenštajn\", \"it\": \"Liechtenstein\", \"ja\": \"リヒテンシュタイン\", \"kr\": \"리히텐슈타인\", \"nl\": \"Liechtenstein\", \"pt\": \"Listenstaine\", \"tr\": \"Lihtenştayn\", \"pt-BR\": \"Liechtenstein\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Vaduz\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '438', 'LIE', 'Liechtenstein', 'Vaduz', '.li', 'Liechtenstein', 'Europe', 'CHF', 'Swiss franc', 'CHf', NULL, 47.27, 9.53, '🇱🇮', 'U+1F1F1 U+1F1EE', 1, 1, 0),
(128, 'Lithuania', 'LT', '440', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Lithuania\", \"cn\": \"立陶宛\", \"de\": \"Litauen\", \"es\": \"Lituania\", \"fa\": \"لیتوانی\", \"fr\": \"Lituanie\", \"hr\": \"Litva\", \"it\": \"Lituania\", \"ja\": \"リトアニア\", \"kr\": \"리투아니아\", \"nl\": \"Litouwen\", \"pt\": \"Lituânia\", \"tr\": \"Litvanya\", \"pt-BR\": \"Lituânia\"}', '[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Vilnius\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]', '440', 'LTU', 'Lithuanian', 'Vilnius', '.lt', 'Lietuva', 'Europe', 'EUR', 'Euro', '€', NULL, 56.00, 24.00, '🇱🇹', 'U+1F1F1 U+1F1F9', 1, 1, 0),
(129, 'Luxembourg', 'LU', '442', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Luxembourg\", \"cn\": \"卢森堡\", \"de\": \"Luxemburg\", \"es\": \"Luxemburgo\", \"fa\": \"لوکزامبورگ\", \"fr\": \"Luxembourg\", \"hr\": \"Luksemburg\", \"it\": \"Lussemburgo\", \"ja\": \"ルクセンブルク\", \"kr\": \"룩셈부르크\", \"nl\": \"Luxemburg\", \"pt\": \"Luxemburgo\", \"tr\": \"Lüksemburg\", \"pt-BR\": \"Luxemburgo\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Luxembourg\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '442', 'LUX', 'Luxembourg, Luxembourgish', 'Luxembourg', '.lu', 'Luxembourg', 'Europe', 'EUR', 'Euro', '€', NULL, 49.75, 6.17, '🇱🇺', 'U+1F1F1 U+1F1FA', 1, 1, 0),
(130, 'Macau S.A.R.', 'MO', '446', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Macau S.A.R.\", \"cn\": \"中国澳门\", \"de\": \"Macao\", \"es\": \"Macao\", \"fa\": \"مکائو\", \"fr\": \"Macao\", \"hr\": \"Makao\", \"it\": \"Macao\", \"ja\": \"マカオ\", \"kr\": \"마카오\", \"nl\": \"Macao\", \"pt\": \"Macau\", \"tr\": \"Makao\", \"pt-BR\": \"Macau\"}', '[{\"tzName\": \"China Standard Time\", \"zoneName\": \"Asia/Macau\", \"gmtOffset\": 28800, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC+08:00\"}]', '446', 'MAC', 'Macanese, Chinese', 'Macao', '.mo', '澳門', 'Asia', 'MOP', 'Macanese pataca', '$', NULL, 22.17, 113.55, '🇲🇴', 'U+1F1F2 U+1F1F4', 1, 1, 0),
(131, 'North Macedonia', 'MK', '807', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"North Macedonia\", \"cn\": \"北馬其頓\", \"de\": \"Nordmazedonien\", \"es\": \"Macedonia del Norte\", \"fa\": \"ﻢﻗﺩﻮﻨﯿﻫ ﺶﻣﺎﻠﯾ\", \"fr\": \"Macédoine du Nord\", \"hr\": \"Sjeverna Makedonija\", \"it\": \"Macedonia del Nord\", \"ja\": \"北マケドニア\", \"kr\": \"북마케도니아\", \"nl\": \"Noord-Macedonië\", \"pt\": \"Macedónia do Norte\", \"tr\": \"Kuzey Makedonya\", \"pt-BR\": \"Macedônia do Norte\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Skopje\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '807', 'MKD', 'Macedonian', 'Skopje', '.mk', 'Северна Македонија', 'Europe', 'MKD', 'Denar', 'ден', NULL, 41.83, 22.00, '🇲🇰', 'U+1F1F2 U+1F1F0', 1, 1, 0),
(132, 'Madagascar', 'MG', '450', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Madagascar\", \"cn\": \"马达加斯加\", \"de\": \"Madagaskar\", \"es\": \"Madagascar\", \"fa\": \"ماداگاسکار\", \"fr\": \"Madagascar\", \"hr\": \"Madagaskar\", \"it\": \"Madagascar\", \"ja\": \"マダガスカル\", \"kr\": \"마다가스카르\", \"nl\": \"Madagaskar\", \"pt\": \"Madagáscar\", \"tr\": \"Madagaskar\", \"pt-BR\": \"Madagascar\"}', '[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Indian/Antananarivo\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]', '450', 'MDG', 'Malagasy', 'Antananarivo', '.mg', 'Madagasikara', 'Africa', 'MGA', 'Malagasy ariary', 'Ar', NULL, -20.00, 47.00, '🇲🇬', 'U+1F1F2 U+1F1EC', 1, 1, 0),
(133, 'Malawi', 'MW', '454', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Malawi\", \"cn\": \"马拉维\", \"de\": \"Malawi\", \"es\": \"Malawi\", \"fa\": \"مالاوی\", \"fr\": \"Malawi\", \"hr\": \"Malavi\", \"it\": \"Malawi\", \"ja\": \"マラウイ\", \"kr\": \"말라위\", \"nl\": \"Malawi\", \"pt\": \"Malávi\", \"tr\": \"Malavi\", \"pt-BR\": \"Malawi\"}', '[{\"tzName\": \"Central Africa Time\", \"zoneName\": \"Africa/Blantyre\", \"gmtOffset\": 7200, \"abbreviation\": \"CAT\", \"gmtOffsetName\": \"UTC+02:00\"}]', '454', 'MWI', 'Malawian', 'Lilongwe', '.mw', 'Malawi', 'Africa', 'MWK', 'Malawian kwacha', 'MK', NULL, -13.50, 34.00, '🇲🇼', 'U+1F1F2 U+1F1FC', 1, 1, 0),
(134, 'Malaysia', 'MY', '458', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Malaysia\", \"cn\": \"马来西亚\", \"de\": \"Malaysia\", \"es\": \"Malasia\", \"fa\": \"مالزی\", \"fr\": \"Malaisie\", \"hr\": \"Malezija\", \"it\": \"Malesia\", \"ja\": \"マレーシア\", \"kr\": \"말레이시아\", \"nl\": \"Maleisië\", \"pt\": \"Malásia\", \"tr\": \"Malezya\", \"pt-BR\": \"Malásia\"}', '[{\"tzName\": \"Malaysia Time\", \"zoneName\": \"Asia/Kuala_Lumpur\", \"gmtOffset\": 28800, \"abbreviation\": \"MYT\", \"gmtOffsetName\": \"UTC+08:00\"}, {\"tzName\": \"Malaysia Time\", \"zoneName\": \"Asia/Kuching\", \"gmtOffset\": 28800, \"abbreviation\": \"MYT\", \"gmtOffsetName\": \"UTC+08:00\"}]', '458', 'MYS', 'Malaysian', 'Kuala Lumpur', '.my', 'Malaysia', 'Asia', 'MYR', 'Malaysian ringgit', 'RM', NULL, 2.50, 112.50, '🇲🇾', 'U+1F1F2 U+1F1FE', 1, 1, 0),
(135, 'Maldives', 'MV', '462', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Maldives\", \"cn\": \"马尔代夫\", \"de\": \"Malediven\", \"es\": \"Maldivas\", \"fa\": \"مالدیو\", \"fr\": \"Maldives\", \"hr\": \"Maldivi\", \"it\": \"Maldive\", \"ja\": \"モルディブ\", \"kr\": \"몰디브\", \"nl\": \"Maldiven\", \"pt\": \"Maldivas\", \"tr\": \"Maldivler\", \"pt-BR\": \"Maldivas\"}', '[{\"tzName\": \"Maldives Time\", \"zoneName\": \"Indian/Maldives\", \"gmtOffset\": 18000, \"abbreviation\": \"MVT\", \"gmtOffsetName\": \"UTC+05:00\"}]', '462', 'MDV', 'Maldivian', 'Male', '.mv', 'Maldives', 'Asia', 'MVR', 'Maldivian rufiyaa', 'Rf', NULL, 3.25, 73.00, '🇲🇻', 'U+1F1F2 U+1F1FB', 1, 1, 0),
(136, 'Mali', 'ML', '466', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Mali\", \"cn\": \"马里\", \"de\": \"Mali\", \"es\": \"Mali\", \"fa\": \"مالی\", \"fr\": \"Mali\", \"hr\": \"Mali\", \"it\": \"Mali\", \"ja\": \"マリ\", \"kr\": \"말리\", \"nl\": \"Mali\", \"pt\": \"Mali\", \"tr\": \"Mali\", \"pt-BR\": \"Mali\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Bamako\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]', '466', 'MLI', 'Malian, Malinese', 'Bamako', '.ml', 'Mali', 'Africa', 'XOF', 'West African CFA franc', 'CFA', NULL, 17.00, -4.00, '🇲🇱', 'U+1F1F2 U+1F1F1', 1, 1, 0),
(137, 'Malta', 'MT', '470', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Malta\", \"cn\": \"马耳他\", \"de\": \"Malta\", \"es\": \"Malta\", \"fa\": \"مالت\", \"fr\": \"Malte\", \"hr\": \"Malta\", \"it\": \"Malta\", \"ja\": \"マルタ\", \"kr\": \"몰타\", \"nl\": \"Malta\", \"pt\": \"Malta\", \"tr\": \"Malta\", \"pt-BR\": \"Malta\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Malta\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '470', 'MLT', 'Maltese', 'Valletta', '.mt', 'Malta', 'Europe', 'EUR', 'Euro', '€', NULL, 35.83, 14.58, '🇲🇹', 'U+1F1F2 U+1F1F9', 1, 1, 0),
(138, 'Marshall Islands', 'MH', '584', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Marshall Islands\", \"cn\": \"马绍尔群岛\", \"de\": \"Marshallinseln\", \"es\": \"Islas Marshall\", \"fa\": \"جزایر مارشال\", \"fr\": \"Îles Marshall\", \"hr\": \"Maršalovi Otoci\", \"it\": \"Isole Marshall\", \"ja\": \"マーシャル諸島\", \"kr\": \"마셜 제도\", \"nl\": \"Marshalleilanden\", \"pt\": \"Ilhas Marshall\", \"tr\": \"Marşal Adalari\", \"pt-BR\": \"Ilhas Marshall\"}', '[{\"tzName\": \"Marshall Islands Time\", \"zoneName\": \"Pacific/Kwajalein\", \"gmtOffset\": 43200, \"abbreviation\": \"MHT\", \"gmtOffsetName\": \"UTC+12:00\"}, {\"tzName\": \"Marshall Islands Time\", \"zoneName\": \"Pacific/Majuro\", \"gmtOffset\": 43200, \"abbreviation\": \"MHT\", \"gmtOffsetName\": \"UTC+12:00\"}]', '584', 'MHL', 'Marshallese', 'Majuro', '.mh', 'M̧ajeļ', 'Oceania', 'USD', 'United States dollar', '$', NULL, 9.00, 168.00, '🇲🇭', 'U+1F1F2 U+1F1ED', 1, 1, 0),
(139, 'Martinique', 'MQ', '474', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Martinique\", \"cn\": \"马提尼克岛\", \"de\": \"Martinique\", \"es\": \"Martinica\", \"fa\": \"مونتسرات\", \"fr\": \"Martinique\", \"hr\": \"Martinique\", \"it\": \"Martinica\", \"ja\": \"マルティニーク\", \"kr\": \"마르티니크\", \"nl\": \"Martinique\", \"pt\": \"Martinica\", \"tr\": \"Martinik\", \"pt-BR\": \"Martinica\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Martinique\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '474', 'MTQ', 'Martiniquais, Martinican', 'Fort-de-France', '.mq', 'Martinique', 'Americas', 'EUR', 'Euro', '€', NULL, 14.67, -61.00, '🇲🇶', 'U+1F1F2 U+1F1F6', 1, 1, 0),
(140, 'Mauritania', 'MR', '478', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Mauritania\", \"cn\": \"毛里塔尼亚\", \"de\": \"Mauretanien\", \"es\": \"Mauritania\", \"fa\": \"موریتانی\", \"fr\": \"Mauritanie\", \"hr\": \"Mauritanija\", \"it\": \"Mauritania\", \"ja\": \"モーリタニア\", \"kr\": \"모리타니\", \"nl\": \"Mauritanië\", \"pt\": \"Mauritânia\", \"tr\": \"Moritanya\", \"pt-BR\": \"Mauritânia\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Nouakchott\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]', '478', 'MRT', 'Mauritanian', 'Nouakchott', '.mr', 'موريتانيا', 'Africa', 'MRO', 'Mauritanian ouguiya', 'MRU', NULL, 20.00, -12.00, '🇲🇷', 'U+1F1F2 U+1F1F7', 1, 1, 0),
(141, 'Mauritius', 'MU', '480', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Mauritius\", \"cn\": \"毛里求斯\", \"de\": \"Mauritius\", \"es\": \"Mauricio\", \"fa\": \"موریس\", \"fr\": \"Île Maurice\", \"hr\": \"Mauricijus\", \"it\": \"Mauritius\", \"ja\": \"モーリシャス\", \"kr\": \"모리셔스\", \"nl\": \"Mauritius\", \"pt\": \"Maurícia\", \"tr\": \"Morityus\", \"pt-BR\": \"Maurício\"}', '[{\"tzName\": \"Mauritius Time\", \"zoneName\": \"Indian/Mauritius\", \"gmtOffset\": 14400, \"abbreviation\": \"MUT\", \"gmtOffsetName\": \"UTC+04:00\"}]', '480', 'MUS', 'Mauritian', 'Port Louis', '.mu', 'Maurice', 'Africa', 'MUR', 'Mauritian rupee', '₨', NULL, -20.28, 57.55, '🇲🇺', 'U+1F1F2 U+1F1FA', 1, 1, 0),
(142, 'Mayotte', 'YT', '175', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Mayotte\", \"cn\": \"马约特\", \"de\": \"Mayotte\", \"es\": \"Mayotte\", \"fa\": \"مایوت\", \"fr\": \"Mayotte\", \"hr\": \"Mayotte\", \"it\": \"Mayotte\", \"ja\": \"マヨット\", \"kr\": \"마요트\", \"nl\": \"Mayotte\", \"pt\": \"Mayotte\", \"tr\": \"Mayotte\", \"pt-BR\": \"Mayotte\"}', '[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Indian/Mayotte\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]', '175', 'MYT', 'Mahoran', 'Mamoudzou', '.yt', 'Mayotte', 'Africa', 'EUR', 'Euro', '€', NULL, -12.83, 45.17, '🇾🇹', 'U+1F1FE U+1F1F9', 1, 1, 0),
(143, 'Mexico', 'MX', '484', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Mexico\", \"cn\": \"墨西哥\", \"de\": \"Mexiko\", \"es\": \"México\", \"fa\": \"مکزیک\", \"fr\": \"Mexique\", \"hr\": \"Meksiko\", \"it\": \"Messico\", \"ja\": \"メキシコ\", \"kr\": \"멕시코\", \"nl\": \"Mexico\", \"pt\": \"México\", \"tr\": \"Meksika\", \"pt-BR\": \"México\"}', '[{\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Bahia_Banderas\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Cancun\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Mountain Standard Time (North America\", \"zoneName\": \"America/Chihuahua\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Mountain Standard Time (North America\", \"zoneName\": \"America/Hermosillo\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Matamoros\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Mountain Standard Time (North America\", \"zoneName\": \"America/Mazatlan\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Merida\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Mexico_City\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Monterrey\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Mountain Standard Time (North America\", \"zoneName\": \"America/Ojinaga\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Pacific Standard Time (North America\", \"zoneName\": \"America/Tijuana\", \"gmtOffset\": -28800, \"abbreviation\": \"PST\", \"gmtOffsetName\": \"UTC-08:00\"}]', '484', 'MEX', 'Mexican', 'Ciudad de México', '.mx', 'México', 'Americas', 'MXN', 'Mexican peso', '$', NULL, 23.00, -102.00, '🇲🇽', 'U+1F1F2 U+1F1FD', 1, 1, 0),
(144, 'Micronesia', 'FM', '583', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Micronesia\", \"cn\": \"密克罗尼西亚\", \"de\": \"Mikronesien\", \"es\": \"Micronesia\", \"fa\": \"ایالات فدرال میکرونزی\", \"fr\": \"Micronésie\", \"hr\": \"Mikronezija\", \"it\": \"Micronesia\", \"ja\": \"ミクロネシア連邦\", \"kr\": \"미크로네시아 연방\", \"nl\": \"Micronesië\", \"pt\": \"Micronésia\", \"tr\": \"Mikronezya\", \"pt-BR\": \"Micronésia\"}', '[{\"tzName\": \"Chuuk Time\", \"zoneName\": \"Pacific/Chuuk\", \"gmtOffset\": 36000, \"abbreviation\": \"CHUT\", \"gmtOffsetName\": \"UTC+10:00\"}, {\"tzName\": \"Kosrae Time\", \"zoneName\": \"Pacific/Kosrae\", \"gmtOffset\": 39600, \"abbreviation\": \"KOST\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Pohnpei Standard Time\", \"zoneName\": \"Pacific/Pohnpei\", \"gmtOffset\": 39600, \"abbreviation\": \"PONT\", \"gmtOffsetName\": \"UTC+11:00\"}]', '583', 'FSM', 'Micronesian', 'Palikir', '.fm', 'Micronesia', 'Oceania', 'USD', 'United States dollar', '$', NULL, 6.92, 158.25, '🇫🇲', 'U+1F1EB U+1F1F2', 1, 1, 0),
(145, 'Moldova', 'MD', '498', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Moldova\", \"cn\": \"摩尔多瓦\", \"de\": \"Moldawie\", \"es\": \"Moldavia\", \"fa\": \"مولداوی\", \"fr\": \"Moldavie\", \"hr\": \"Moldova\", \"it\": \"Moldavia\", \"ja\": \"モルドバ共和国\", \"kr\": \"몰도바\", \"nl\": \"Moldavië\", \"pt\": \"Moldávia\", \"tr\": \"Moldova\", \"pt-BR\": \"Moldávia\"}', '[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Chisinau\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]', '498', 'MDA', 'Moldovan', 'Chisinau', '.md', 'Moldova', 'Europe', 'MDL', 'Moldovan leu', 'L', NULL, 47.00, 29.00, '🇲🇩', 'U+1F1F2 U+1F1E9', 1, 1, 0),
(146, 'Monaco', 'MC', '492', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Monaco\", \"cn\": \"摩纳哥\", \"de\": \"Monaco\", \"es\": \"Mónaco\", \"fa\": \"موناکو\", \"fr\": \"Monaco\", \"hr\": \"Monako\", \"it\": \"Principato di Monaco\", \"ja\": \"モナコ\", \"kr\": \"모나코\", \"nl\": \"Monaco\", \"pt\": \"Mónaco\", \"tr\": \"Monako\", \"pt-BR\": \"Mônaco\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Monaco\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '492', 'MCO', 'Monegasque, Monacan', 'Monaco', '.mc', 'Monaco', 'Europe', 'EUR', 'Euro', '€', NULL, 43.73, 7.40, '🇲🇨', 'U+1F1F2 U+1F1E8', 1, 1, 0),
(147, 'Mongolia', 'MN', '496', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Mongolia\", \"cn\": \"蒙古\", \"de\": \"Mongolei\", \"es\": \"Mongolia\", \"fa\": \"مغولستان\", \"fr\": \"Mongolie\", \"hr\": \"Mongolija\", \"it\": \"Mongolia\", \"ja\": \"モンゴル\", \"kr\": \"몽골\", \"nl\": \"Mongolië\", \"pt\": \"Mongólia\", \"tr\": \"Moğolistan\", \"pt-BR\": \"Mongólia\"}', '[{\"tzName\": \"Choibalsan Standard Time\", \"zoneName\": \"Asia/Choibalsan\", \"gmtOffset\": 28800, \"abbreviation\": \"CHOT\", \"gmtOffsetName\": \"UTC+08:00\"}, {\"tzName\": \"Hovd Time\", \"zoneName\": \"Asia/Hovd\", \"gmtOffset\": 25200, \"abbreviation\": \"HOVT\", \"gmtOffsetName\": \"UTC+07:00\"}, {\"tzName\": \"Ulaanbaatar Standard Time\", \"zoneName\": \"Asia/Ulaanbaatar\", \"gmtOffset\": 28800, \"abbreviation\": \"ULAT\", \"gmtOffsetName\": \"UTC+08:00\"}]', '496', 'MNG', 'Mongolian', 'Ulan Bator', '.mn', 'Монгол улс', 'Asia', 'MNT', 'Mongolian tögrög', '₮', NULL, 46.00, 105.00, '🇲🇳', 'U+1F1F2 U+1F1F3', 1, 1, 0),
(148, 'Montenegro', 'ME', '499', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Montenegro\", \"cn\": \"黑山\", \"de\": \"Montenegro\", \"es\": \"Montenegro\", \"fa\": \"مونته‌نگرو\", \"fr\": \"Monténégro\", \"hr\": \"Crna Gora\", \"it\": \"Montenegro\", \"ja\": \"モンテネグロ\", \"kr\": \"몬테네그로\", \"nl\": \"Montenegro\", \"pt\": \"Montenegro\", \"tr\": \"Karadağ\", \"pt-BR\": \"Montenegro\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Podgorica\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '499', 'MNE', 'Montenegrin', 'Podgorica', '.me', 'Црна Гора', 'Europe', 'EUR', 'Euro', '€', NULL, 42.50, 19.30, '🇲🇪', 'U+1F1F2 U+1F1EA', 1, 1, 0),
(149, 'Montserrat', 'MS', '500', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Montserrat\", \"cn\": \"蒙特塞拉特\", \"de\": \"Montserrat\", \"es\": \"Montserrat\", \"fa\": \"مایوت\", \"fr\": \"Montserrat\", \"hr\": \"Montserrat\", \"it\": \"Montserrat\", \"ja\": \"モントセラト\", \"kr\": \"몬트세랫\", \"nl\": \"Montserrat\", \"pt\": \"Monserrate\", \"tr\": \"Montserrat\", \"pt-BR\": \"Montserrat\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Montserrat\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '500', 'MSR', 'Montserratian', 'Plymouth', '.ms', 'Montserrat', 'Americas', 'XCD', 'Eastern Caribbean dollar', '$', NULL, 16.75, -62.20, '🇲🇸', 'U+1F1F2 U+1F1F8', 1, 1, 0),
(150, 'Morocco', 'MA', '504', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Morocco\", \"cn\": \"摩洛哥\", \"de\": \"Marokko\", \"es\": \"Marruecos\", \"fa\": \"مراکش\", \"fr\": \"Maroc\", \"hr\": \"Maroko\", \"it\": \"Marocco\", \"ja\": \"モロッコ\", \"kr\": \"모로코\", \"nl\": \"Marokko\", \"pt\": \"Marrocos\", \"tr\": \"Fas\", \"pt-BR\": \"Marrocos\"}', '[{\"tzName\": \"Western European Summer Time\", \"zoneName\": \"Africa/Casablanca\", \"gmtOffset\": 3600, \"abbreviation\": \"WEST\", \"gmtOffsetName\": \"UTC+01:00\"}]', '504', 'MAR', 'Moroccan', 'Rabat', '.ma', 'المغرب', 'Africa', 'MAD', 'Moroccan dirham', 'DH', NULL, 32.00, -5.00, '🇲🇦', 'U+1F1F2 U+1F1E6', 1, 1, 0),
(151, 'Mozambique', 'MZ', '508', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Mozambique\", \"cn\": \"莫桑比克\", \"de\": \"Mosambik\", \"es\": \"Mozambique\", \"fa\": \"موزامبیک\", \"fr\": \"Mozambique\", \"hr\": \"Mozambik\", \"it\": \"Mozambico\", \"ja\": \"モザンビーク\", \"kr\": \"모잠비크\", \"nl\": \"Mozambique\", \"pt\": \"Moçambique\", \"tr\": \"Mozambik\", \"pt-BR\": \"Moçambique\"}', '[{\"tzName\": \"Central Africa Time\", \"zoneName\": \"Africa/Maputo\", \"gmtOffset\": 7200, \"abbreviation\": \"CAT\", \"gmtOffsetName\": \"UTC+02:00\"}]', '508', 'MOZ', 'Mozambican', 'Maputo', '.mz', 'Moçambique', 'Africa', 'MZN', 'Mozambican metical', 'MT', NULL, -18.25, 35.00, '🇲🇿', 'U+1F1F2 U+1F1FF', 1, 1, 0),
(152, 'Myanmar', 'MM', '104', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Myanmar\", \"cn\": \"缅甸\", \"de\": \"Myanmar\", \"es\": \"Myanmar\", \"fa\": \"میانمار\", \"fr\": \"Myanmar\", \"hr\": \"Mijanmar\", \"it\": \"Birmania\", \"ja\": \"ミャンマー\", \"kr\": \"미얀마\", \"nl\": \"Myanmar\", \"pt\": \"Myanmar\", \"tr\": \"Myanmar\", \"pt-BR\": \"Myanmar\"}', '[{\"tzName\": \"Myanmar Standard Time\", \"zoneName\": \"Asia/Yangon\", \"gmtOffset\": 23400, \"abbreviation\": \"MMT\", \"gmtOffsetName\": \"UTC+06:30\"}]', '104', 'MMR', 'Burmese', 'Nay Pyi Taw', '.mm', 'မြန်မာ', 'Asia', 'MMK', 'Burmese kyat', 'K', NULL, 22.00, 98.00, '🇲🇲', 'U+1F1F2 U+1F1F2', 1, 1, 0),
(153, 'Namibia', 'NA', '516', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Namibia\", \"cn\": \"纳米比亚\", \"de\": \"Namibia\", \"es\": \"Namibia\", \"fa\": \"نامیبیا\", \"fr\": \"Namibie\", \"hr\": \"Namibija\", \"it\": \"Namibia\", \"ja\": \"ナミビア\", \"kr\": \"나미비아\", \"nl\": \"Namibië\", \"pt\": \"Namíbia\", \"tr\": \"Namibya\", \"pt-BR\": \"Namíbia\"}', '[{\"tzName\": \"West Africa Summer Time\", \"zoneName\": \"Africa/Windhoek\", \"gmtOffset\": 7200, \"abbreviation\": \"WAST\", \"gmtOffsetName\": \"UTC+02:00\"}]', '516', 'NAM', 'Namibian', 'Windhoek', '.na', 'Namibia', 'Africa', 'NAD', 'Namibian dollar', '$', NULL, -22.00, 17.00, '🇳🇦', 'U+1F1F3 U+1F1E6', 1, 1, 0),
(154, 'Nauru', 'NR', '520', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Nauru\", \"cn\": \"瑙鲁\", \"de\": \"Nauru\", \"es\": \"Nauru\", \"fa\": \"نائورو\", \"fr\": \"Nauru\", \"hr\": \"Nauru\", \"it\": \"Nauru\", \"ja\": \"ナウル\", \"kr\": \"나우루\", \"nl\": \"Nauru\", \"pt\": \"Nauru\", \"tr\": \"Nauru\", \"pt-BR\": \"Nauru\"}', '[{\"tzName\": \"Nauru Time\", \"zoneName\": \"Pacific/Nauru\", \"gmtOffset\": 43200, \"abbreviation\": \"NRT\", \"gmtOffsetName\": \"UTC+12:00\"}]', '520', 'NRU', 'Nauruan', 'Yaren', '.nr', 'Nauru', 'Oceania', 'AUD', 'Australian dollar', '$', NULL, -0.53, 166.92, '🇳🇷', 'U+1F1F3 U+1F1F7', 1, 1, 0),
(155, 'Nepal', 'NP', '524', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Nepal\", \"cn\": \"尼泊尔\", \"de\": \"Népal\", \"es\": \"Nepal\", \"fa\": \"نپال\", \"fr\": \"Népal\", \"hr\": \"Nepal\", \"it\": \"Nepal\", \"ja\": \"ネパール\", \"kr\": \"네팔\", \"nl\": \"Nepal\", \"pt\": \"Nepal\", \"tr\": \"Nepal\", \"pt-BR\": \"Nepal\"}', '[{\"tzName\": \"Nepal Time\", \"zoneName\": \"Asia/Kathmandu\", \"gmtOffset\": 20700, \"abbreviation\": \"NPT\", \"gmtOffsetName\": \"UTC+05:45\"}]', '524', 'NPL', 'Nepali, Nepalese', 'Kathmandu', '.np', 'नपल', 'Asia', 'NPR', 'Nepalese rupee', '₨', NULL, 28.00, 84.00, '🇳🇵', 'U+1F1F3 U+1F1F5', 1, 1, 0),
(156, 'Netherlands', 'NL', '528', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Netherlands\", \"cn\": \"荷兰\", \"de\": \"Niederlande\", \"es\": \"Países Bajos\", \"fa\": \"پادشاهی هلند\", \"fr\": \"Pays-Bas\", \"hr\": \"Nizozemska\", \"it\": \"Paesi Bassi\", \"ja\": \"オランダ\", \"kr\": \"네덜란드 \", \"nl\": \"Nederland\", \"pt\": \"Países Baixos\", \"tr\": \"Hollanda\", \"pt-BR\": \"Holanda\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Amsterdam\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '528', 'NLD', 'Dutch, Netherlandic', 'Amsterdam', '.nl', 'Nederland', 'Europe', 'EUR', 'Euro', '€', NULL, 52.50, 5.75, '🇳🇱', 'U+1F1F3 U+1F1F1', 1, 1, 0),
(157, 'New Caledonia', 'NC', '540', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"New Caledonia\", \"cn\": \"新喀里多尼亚\", \"de\": \"Neukaledonien\", \"es\": \"Nueva Caledonia\", \"fa\": \"کالدونیای جدید\", \"fr\": \"Nouvelle-Calédonie\", \"hr\": \"Nova Kaledonija\", \"it\": \"Nuova Caledonia\", \"ja\": \"ニューカレドニア\", \"kr\": \"누벨칼레도니\", \"nl\": \"Nieuw-Caledonië\", \"pt\": \"Nova Caledónia\", \"tr\": \"Yeni Kaledonya\", \"pt-BR\": \"Nova Caledônia\"}', '[{\"tzName\": \"New Caledonia Time\", \"zoneName\": \"Pacific/Noumea\", \"gmtOffset\": 39600, \"abbreviation\": \"NCT\", \"gmtOffsetName\": \"UTC+11:00\"}]', '540', 'NCL', 'New Caledonian', 'Noumea', '.nc', 'Nouvelle-Calédonie', 'Oceania', 'XPF', 'CFP franc', '₣', NULL, -21.50, 165.50, '🇳🇨', 'U+1F1F3 U+1F1E8', 1, 1, 0),
(158, 'New Zealand', 'NZ', '554', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"New Zealand\", \"cn\": \"新西兰\", \"de\": \"Neuseeland\", \"es\": \"Nueva Zelanda\", \"fa\": \"نیوزیلند\", \"fr\": \"Nouvelle-Zélande\", \"hr\": \"Novi Zeland\", \"it\": \"Nuova Zelanda\", \"ja\": \"ニュージーランド\", \"kr\": \"뉴질랜드\", \"nl\": \"Nieuw-Zeeland\", \"pt\": \"Nova Zelândia\", \"tr\": \"Yeni Zelanda\", \"pt-BR\": \"Nova Zelândia\"}', '[{\"tzName\": \"New Zealand Daylight Time\", \"zoneName\": \"Pacific/Auckland\", \"gmtOffset\": 46800, \"abbreviation\": \"NZDT\", \"gmtOffsetName\": \"UTC+13:00\"}, {\"tzName\": \"Chatham Standard Time\", \"zoneName\": \"Pacific/Chatham\", \"gmtOffset\": 49500, \"abbreviation\": \"CHAST\", \"gmtOffsetName\": \"UTC+13:45\"}]', '554', 'NZL', 'New Zealand, NZ', 'Wellington', '.nz', 'New Zealand', 'Oceania', 'NZD', 'New Zealand dollar', '$', NULL, -41.00, 174.00, '🇳🇿', 'U+1F1F3 U+1F1FF', 1, 1, 0),
(159, 'Nicaragua', 'NI', '558', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Nicaragua\", \"cn\": \"尼加拉瓜\", \"de\": \"Nicaragua\", \"es\": \"Nicaragua\", \"fa\": \"نیکاراگوئه\", \"fr\": \"Nicaragua\", \"hr\": \"Nikaragva\", \"it\": \"Nicaragua\", \"ja\": \"ニカラグア\", \"kr\": \"니카라과\", \"nl\": \"Nicaragua\", \"pt\": \"Nicarágua\", \"tr\": \"Nikaragua\", \"pt-BR\": \"Nicarágua\"}', '[{\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Managua\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}]', '558', 'NIC', 'Nicaraguan', 'Managua', '.ni', 'Nicaragua', 'Americas', 'NIO', 'Nicaraguan córdoba', 'C$', NULL, 13.00, -85.00, '🇳🇮', 'U+1F1F3 U+1F1EE', 1, 1, 0),
(160, 'Niger', 'NE', '562', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Niger\", \"cn\": \"尼日尔\", \"de\": \"Niger\", \"es\": \"Níger\", \"fa\": \"نیجر\", \"fr\": \"Niger\", \"hr\": \"Niger\", \"it\": \"Niger\", \"ja\": \"ニジェール\", \"kr\": \"니제르\", \"nl\": \"Niger\", \"pt\": \"Níger\", \"tr\": \"Nijer\", \"pt-BR\": \"Níger\"}', '[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Niamey\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}]', '562', 'NER', 'Nigerien', 'Niamey', '.ne', 'Niger', 'Africa', 'XOF', 'West African CFA franc', 'CFA', NULL, 16.00, 8.00, '🇳🇪', 'U+1F1F3 U+1F1EA', 1, 1, 0),
(161, 'Nigeria', 'NG', '566', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Nigeria\", \"cn\": \"尼日利亚\", \"de\": \"Nigeria\", \"es\": \"Nigeria\", \"fa\": \"نیجریه\", \"fr\": \"Nigéria\", \"hr\": \"Nigerija\", \"it\": \"Nigeria\", \"ja\": \"ナイジェリア\", \"kr\": \"나이지리아\", \"nl\": \"Nigeria\", \"pt\": \"Nigéria\", \"tr\": \"Nijerya\", \"pt-BR\": \"Nigéria\"}', '[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Lagos\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}]', '566', 'NGA', 'Nigerian', 'Abuja', '.ng', 'Nigeria', 'Africa', 'NGN', 'Nigerian naira', '₦', NULL, 10.00, 8.00, '🇳🇬', 'U+1F1F3 U+1F1EC', 1, 1, 0),
(162, 'Niue', 'NU', '570', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Niue\", \"cn\": \"纽埃\", \"de\": \"Niue\", \"es\": \"Niue\", \"fa\": \"نیووی\", \"fr\": \"Niue\", \"hr\": \"Niue\", \"it\": \"Niue\", \"ja\": \"ニウエ\", \"kr\": \"니우에\", \"nl\": \"Niue\", \"pt\": \"Niue\", \"tr\": \"Niue\", \"pt-BR\": \"Niue\"}', '[{\"tzName\": \"Niue Time\", \"zoneName\": \"Pacific/Niue\", \"gmtOffset\": -39600, \"abbreviation\": \"NUT\", \"gmtOffsetName\": \"UTC-11:00\"}]', '570', 'NIU', 'Niuean', 'Alofi', '.nu', 'Niuē', 'Oceania', 'NZD', 'New Zealand dollar', '$', NULL, -19.03, -169.87, '🇳🇺', 'U+1F1F3 U+1F1FA', 1, 1, 0),
(163, 'Norfolk Island', 'NF', '574', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Norfolk Island\", \"cn\": \"诺福克岛\", \"de\": \"Norfolkinsel\", \"es\": \"Isla de Norfolk\", \"fa\": \"جزیره نورفک\", \"fr\": \"Île de Norfolk\", \"hr\": \"Otok Norfolk\", \"it\": \"Isola Norfolk\", \"ja\": \"ノーフォーク島\", \"kr\": \"노퍽 섬\", \"nl\": \"Norfolkeiland\", \"pt\": \"Ilha Norfolk\", \"tr\": \"Norfolk Adasi\", \"pt-BR\": \"Ilha Norfolk\"}', '[{\"tzName\": \"Norfolk Time\", \"zoneName\": \"Pacific/Norfolk\", \"gmtOffset\": 43200, \"abbreviation\": \"NFT\", \"gmtOffsetName\": \"UTC+12:00\"}]', '574', 'NFK', 'Norfolk Island', 'Kingston', '.nf', 'Norfolk Island', 'Oceania', 'AUD', 'Australian dollar', '$', NULL, -29.03, 167.95, '🇳🇫', 'U+1F1F3 U+1F1EB', 1, 1, 0),
(164, 'Northern Mariana Islands', 'MP', '580', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Northern Mariana Islands\", \"cn\": \"北马里亚纳群岛\", \"de\": \"Nördliche Marianen\", \"es\": \"Islas Marianas del Norte\", \"fa\": \"جزایر ماریانای شمالی\", \"fr\": \"Îles Mariannes du Nord\", \"hr\": \"Sjevernomarijanski otoci\", \"it\": \"Isole Marianne Settentrionali\", \"ja\": \"北マリアナ諸島\", \"kr\": \"북마리아나 제도\", \"nl\": \"Noordelijke Marianeneilanden\", \"pt\": \"Ilhas Marianas\", \"tr\": \"Kuzey Mariana Adalari\", \"pt-BR\": \"Ilhas Marianas\"}', '[{\"tzName\": \"Chamorro Standard Time\", \"zoneName\": \"Pacific/Saipan\", \"gmtOffset\": 36000, \"abbreviation\": \"ChST\", \"gmtOffsetName\": \"UTC+10:00\"}]', '580', 'MNP', 'Northern Marianan', 'Saipan', '.mp', 'Northern Mariana Islands', 'Oceania', 'USD', 'United States dollar', '$', NULL, 15.20, 145.75, '🇲🇵', 'U+1F1F2 U+1F1F5', 1, 1, 0),
(165, 'Norway', 'NO', '578', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Norway\", \"cn\": \"挪威\", \"de\": \"Norwegen\", \"es\": \"Noruega\", \"fa\": \"نروژ\", \"fr\": \"Norvège\", \"hr\": \"Norveška\", \"it\": \"Norvegia\", \"ja\": \"ノルウェー\", \"kr\": \"노르웨이\", \"nl\": \"Noorwegen\", \"pt\": \"Noruega\", \"tr\": \"Norveç\", \"pt-BR\": \"Noruega\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Oslo\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '578', 'NOR', 'Norwegian', 'Oslo', '.no', 'Norge', 'Europe', 'NOK', 'Norwegian krone', 'kr', NULL, 62.00, 10.00, '🇳🇴', 'U+1F1F3 U+1F1F4', 1, 1, 0),
(166, 'Oman', 'OM', '512', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Oman\", \"cn\": \"阿曼\", \"de\": \"Oman\", \"es\": \"Omán\", \"fa\": \"عمان\", \"fr\": \"Oman\", \"hr\": \"Oman\", \"it\": \"oman\", \"ja\": \"オマーン\", \"kr\": \"오만\", \"nl\": \"Oman\", \"pt\": \"Omã\", \"tr\": \"Umman\", \"pt-BR\": \"Omã\"}', '[{\"tzName\": \"Gulf Standard Time\", \"zoneName\": \"Asia/Muscat\", \"gmtOffset\": 14400, \"abbreviation\": \"GST\", \"gmtOffsetName\": \"UTC+04:00\"}]', '512', 'OMN', 'Omani', 'Muscat', '.om', 'عمان', 'Asia', 'OMR', 'Omani rial', '.ع.ر', NULL, 21.00, 57.00, '🇴🇲', 'U+1F1F4 U+1F1F2', 1, 1, 8),
(167, 'Pakistan', 'PK', '586', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Pakistan\", \"cn\": \"巴基斯坦\", \"de\": \"Pakistan\", \"es\": \"Pakistán\", \"fa\": \"پاکستان\", \"fr\": \"Pakistan\", \"hr\": \"Pakistan\", \"it\": \"Pakistan\", \"ja\": \"パキスタン\", \"kr\": \"파키스탄\", \"nl\": \"Pakistan\", \"pt\": \"Paquistão\", \"tr\": \"Pakistan\", \"pt-BR\": \"Paquistão\"}', '[{\"tzName\": \"Pakistan Standard Time\", \"zoneName\": \"Asia/Karachi\", \"gmtOffset\": 18000, \"abbreviation\": \"PKT\", \"gmtOffsetName\": \"UTC+05:00\"}]', '586', 'PAK', 'Pakistani', 'Islamabad', '.pk', 'Pakistan', 'Asia', 'PKR', 'Pakistani rupee', '₨', NULL, 30.00, 70.00, '🇵🇰', 'U+1F1F5 U+1F1F0', 1, 1, 0),
(168, 'Palau', 'PW', '585', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Palau\", \"cn\": \"帕劳\", \"de\": \"Palau\", \"es\": \"Palau\", \"fa\": \"پالائو\", \"fr\": \"Palaos\", \"hr\": \"Palau\", \"it\": \"Palau\", \"ja\": \"パラオ\", \"kr\": \"팔라우\", \"nl\": \"Palau\", \"pt\": \"Palau\", \"tr\": \"Palau\", \"pt-BR\": \"Palau\"}', '[{\"tzName\": \"Palau Time\", \"zoneName\": \"Pacific/Palau\", \"gmtOffset\": 32400, \"abbreviation\": \"PWT\", \"gmtOffsetName\": \"UTC+09:00\"}]', '585', 'PLW', 'Palauan', 'Melekeok', '.pw', 'Palau', 'Oceania', 'USD', 'United States dollar', '$', NULL, 7.50, 134.50, '🇵🇼', 'U+1F1F5 U+1F1FC', 1, 1, 0),
(169, 'Palestinian Territory Occupied', 'PS', '275', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Palestinian Territory Occupied\", \"cn\": \"巴勒斯坦\", \"de\": \"Palästina\", \"es\": \"Palestina\", \"fa\": \"فلسطین\", \"fr\": \"Palestine\", \"hr\": \"Palestina\", \"it\": \"Palestina\", \"ja\": \"パレスチナ\", \"kr\": \"팔레스타인 영토\", \"nl\": \"Palestijnse gebieden\", \"pt\": \"Palestina\", \"tr\": \"Filistin\", \"pt-BR\": \"Palestina\"}', '[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Asia/Gaza\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}, {\"tzName\": \"Eastern European Time\", \"zoneName\": \"Asia/Hebron\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]', '275', 'PSE', 'Palestinian', 'East Jerusalem', '.ps', 'فلسطين', 'Asia', 'ILS', 'Israeli new shekel', '₪', NULL, 31.90, 35.20, '🇵🇸', 'U+1F1F5 U+1F1F8', 1, 1, 0),
(170, 'Panama', 'PA', '591', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Panama\", \"cn\": \"巴拿马\", \"de\": \"Panama\", \"es\": \"Panamá\", \"fa\": \"پاناما\", \"fr\": \"Panama\", \"hr\": \"Panama\", \"it\": \"Panama\", \"ja\": \"パナマ\", \"kr\": \"파나마\", \"nl\": \"Panama\", \"pt\": \"Panamá\", \"tr\": \"Panama\", \"pt-BR\": \"Panamá\"}', '[{\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Panama\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}]', '591', 'PAN', 'Panamanian', 'Panama City', '.pa', 'Panamá', 'Americas', 'PAB', 'Panamanian balboa', 'B/.', NULL, 9.00, -80.00, '🇵🇦', 'U+1F1F5 U+1F1E6', 1, 1, 0),
(171, 'Papua new Guinea', 'PG', '598', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Papua new Guinea\", \"cn\": \"巴布亚新几内亚\", \"de\": \"Papua-Neuguinea\", \"es\": \"Papúa Nueva Guinea\", \"fa\": \"پاپوآ گینه نو\", \"fr\": \"Papouasie-Nouvelle-Guinée\", \"hr\": \"Papua Nova Gvineja\", \"it\": \"Papua Nuova Guinea\", \"ja\": \"パプアニューギニア\", \"kr\": \"파푸아뉴기니\", \"nl\": \"Papoea-Nieuw-Guinea\", \"pt\": \"Papua Nova Guiné\", \"tr\": \"Papua Yeni Gine\", \"pt-BR\": \"Papua Nova Guiné\"}', '[{\"tzName\": \"Bougainville Standard Time[6\", \"zoneName\": \"Pacific/Bougainville\", \"gmtOffset\": 39600, \"abbreviation\": \"BST\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Papua New Guinea Time\", \"zoneName\": \"Pacific/Port_Moresby\", \"gmtOffset\": 36000, \"abbreviation\": \"PGT\", \"gmtOffsetName\": \"UTC+10:00\"}]', '598', 'PNG', 'Papua New Guinean, Papuan', 'Port Moresby', '.pg', 'Papua Niugini', 'Oceania', 'PGK', 'Papua New Guinean kina', 'K', NULL, -6.00, 147.00, '🇵🇬', 'U+1F1F5 U+1F1EC', 1, 1, 0),
(172, 'Paraguay', 'PY', '600', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Paraguay\", \"cn\": \"巴拉圭\", \"de\": \"Paraguay\", \"es\": \"Paraguay\", \"fa\": \"پاراگوئه\", \"fr\": \"Paraguay\", \"hr\": \"Paragvaj\", \"it\": \"Paraguay\", \"ja\": \"パラグアイ\", \"kr\": \"파라과이\", \"nl\": \"Paraguay\", \"pt\": \"Paraguai\", \"tr\": \"Paraguay\", \"pt-BR\": \"Paraguai\"}', '[{\"tzName\": \"Paraguay Summer Time\", \"zoneName\": \"America/Asuncion\", \"gmtOffset\": -10800, \"abbreviation\": \"PYST\", \"gmtOffsetName\": \"UTC-03:00\"}]', '600', 'PRY', 'Paraguayan', 'Asuncion', '.py', 'Paraguay', 'Americas', 'PYG', 'Paraguayan guarani', '₲', NULL, -23.00, -58.00, '🇵🇾', 'U+1F1F5 U+1F1FE', 1, 1, 0),
(173, 'Peru', 'PE', '604', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Peru\", \"cn\": \"秘鲁\", \"de\": \"Peru\", \"es\": \"Perú\", \"fa\": \"پرو\", \"fr\": \"Pérou\", \"hr\": \"Peru\", \"it\": \"Perù\", \"ja\": \"ペルー\", \"kr\": \"페루\", \"nl\": \"Peru\", \"pt\": \"Peru\", \"tr\": \"Peru\", \"pt-BR\": \"Peru\"}', '[{\"tzName\": \"Peru Time\", \"zoneName\": \"America/Lima\", \"gmtOffset\": -18000, \"abbreviation\": \"PET\", \"gmtOffsetName\": \"UTC-05:00\"}]', '604', 'PER', 'Peruvian', 'Lima', '.pe', 'Perú', 'Americas', 'PEN', 'Peruvian sol', 'S/.', NULL, -10.00, -76.00, '🇵🇪', 'U+1F1F5 U+1F1EA', 1, 1, 0),
(174, 'Philippines', 'PH', '608', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Philippines\", \"cn\": \"菲律宾\", \"de\": \"Philippinen\", \"es\": \"Filipinas\", \"fa\": \"جزایر الندفیلیپین\", \"fr\": \"Philippines\", \"hr\": \"Filipini\", \"it\": \"Filippine\", \"ja\": \"フィリピン\", \"kr\": \"필리핀\", \"nl\": \"Filipijnen\", \"pt\": \"Filipinas\", \"tr\": \"Filipinler\", \"pt-BR\": \"Filipinas\"}', '[{\"tzName\": \"Philippine Time\", \"zoneName\": \"Asia/Manila\", \"gmtOffset\": 28800, \"abbreviation\": \"PHT\", \"gmtOffsetName\": \"UTC+08:00\"}]', '608', 'PHL', 'Philippine, Filipino', 'Manila', '.ph', 'Pilipinas', 'Asia', 'PHP', 'Philippine peso', '₱', NULL, 13.00, 122.00, '🇵🇭', 'U+1F1F5 U+1F1ED', 1, 1, 0),
(175, 'Pitcairn Island', 'PN', '612', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Pitcairn Island\", \"cn\": \"皮特凯恩群岛\", \"de\": \"Pitcairn\", \"es\": \"Islas Pitcairn\", \"fa\": \"پیتکرن\", \"fr\": \"Îles Pitcairn\", \"hr\": \"Pitcairnovo otočje\", \"it\": \"Isole Pitcairn\", \"ja\": \"ピトケアン\", \"kr\": \"핏케언 제도\", \"nl\": \"Pitcairneilanden\", \"pt\": \"Ilhas Picárnia\", \"tr\": \"Pitcairn Adalari\", \"pt-BR\": \"Ilhas Pitcairn\"}', '[{\"tzName\": \"Pacific Standard Time (North America\", \"zoneName\": \"Pacific/Pitcairn\", \"gmtOffset\": -28800, \"abbreviation\": \"PST\", \"gmtOffsetName\": \"UTC-08:00\"}]', '612', 'PCN', 'Pitcairn Island', 'Adamstown', '.pn', 'Pitcairn Islands', 'Oceania', 'NZD', 'New Zealand dollar', '$', NULL, -25.07, -130.10, '🇵🇳', 'U+1F1F5 U+1F1F3', 1, 1, 0),
(176, 'Poland', 'PL', '616', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Poland\", \"cn\": \"波兰\", \"de\": \"Polen\", \"es\": \"Polonia\", \"fa\": \"لهستان\", \"fr\": \"Pologne\", \"hr\": \"Poljska\", \"it\": \"Polonia\", \"ja\": \"ポーランド\", \"kr\": \"폴란드\", \"nl\": \"Polen\", \"pt\": \"Polónia\", \"tr\": \"Polonya\", \"pt-BR\": \"Polônia\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Warsaw\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '616', 'POL', 'Polish', 'Warsaw', '.pl', 'Polska', 'Europe', 'PLN', 'Polish złoty', 'zł', NULL, 52.00, 20.00, '🇵🇱', 'U+1F1F5 U+1F1F1', 1, 1, 0),
(177, 'Portugal', 'PT', '620', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Portugal\", \"cn\": \"葡萄牙\", \"de\": \"Portugal\", \"es\": \"Portugal\", \"fa\": \"پرتغال\", \"fr\": \"Portugal\", \"hr\": \"Portugal\", \"it\": \"Portogallo\", \"ja\": \"ポルトガル\", \"kr\": \"포르투갈\", \"nl\": \"Portugal\", \"pt\": \"Portugal\", \"tr\": \"Portekiz\", \"pt-BR\": \"Portugal\"}', '[{\"tzName\": \"Azores Standard Time\", \"zoneName\": \"Atlantic/Azores\", \"gmtOffset\": -3600, \"abbreviation\": \"AZOT\", \"gmtOffsetName\": \"UTC-01:00\"}, {\"tzName\": \"Western European Time\", \"zoneName\": \"Atlantic/Madeira\", \"gmtOffset\": 0, \"abbreviation\": \"WET\", \"gmtOffsetName\": \"UTC±00\"}, {\"tzName\": \"Western European Time\", \"zoneName\": \"Europe/Lisbon\", \"gmtOffset\": 0, \"abbreviation\": \"WET\", \"gmtOffsetName\": \"UTC±00\"}]', '620', 'PRT', 'Portuguese', 'Lisbon', '.pt', 'Portugal', 'Europe', 'EUR', 'Euro', '€', NULL, 39.50, -8.00, '🇵🇹', 'U+1F1F5 U+1F1F9', 1, 1, 0),
(178, 'Puerto Rico', 'PR', '630', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Puerto Rico\", \"cn\": \"波多黎各\", \"de\": \"Puerto Rico\", \"es\": \"Puerto Rico\", \"fa\": \"پورتو ریکو\", \"fr\": \"Porto Rico\", \"hr\": \"Portoriko\", \"it\": \"Porto Rico\", \"ja\": \"プエルトリコ\", \"kr\": \"푸에르토리코\", \"nl\": \"Puerto Rico\", \"pt\": \"Porto Rico\", \"tr\": \"Porto Riko\", \"pt-BR\": \"Porto Rico\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Puerto_Rico\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '630', 'PRI', 'Puerto Rican', 'San Juan', '.pr', 'Puerto Rico', 'Americas', 'USD', 'United States dollar', '$', NULL, 18.25, -66.50, '🇵🇷', 'U+1F1F5 U+1F1F7', 1, 1, 0),
(179, 'Qatar', 'QA', '634', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Qatar\", \"cn\": \"卡塔尔\", \"de\": \"Katar\", \"es\": \"Catar\", \"fa\": \"قطر\", \"fr\": \"Qatar\", \"hr\": \"Katar\", \"it\": \"Qatar\", \"ja\": \"カタール\", \"kr\": \"카타르\", \"nl\": \"Qatar\", \"pt\": \"Catar\", \"tr\": \"Katar\", \"pt-BR\": \"Catar\"}', '[{\"tzName\": \"Arabia Standard Time\", \"zoneName\": \"Asia/Qatar\", \"gmtOffset\": 10800, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC+03:00\"}]', '634', 'QAT', 'Qatari', 'Doha', '.qa', 'قطر', 'Asia', 'QAR', 'Qatari riyal', 'ق.ر', NULL, 25.50, 51.25, '🇶🇦', 'U+1F1F6 U+1F1E6', 1, 1, 3),
(180, 'Reunion', 'RE', '638', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Reunion\", \"cn\": \"留尼汪岛\", \"de\": \"Réunion\", \"es\": \"Reunión\", \"fa\": \"رئونیون\", \"fr\": \"Réunion\", \"hr\": \"Réunion\", \"it\": \"Riunione\", \"ja\": \"レユニオン\", \"kr\": \"레위니옹\", \"nl\": \"Réunion\", \"pt\": \"Reunião\", \"tr\": \"Réunion\", \"pt-BR\": \"Reunião\"}', '[{\"tzName\": \"Réunion Time\", \"zoneName\": \"Indian/Reunion\", \"gmtOffset\": 14400, \"abbreviation\": \"RET\", \"gmtOffsetName\": \"UTC+04:00\"}]', '638', 'REU', 'Reunionese, Reunionnais', 'Saint-Denis', '.re', 'La Réunion', 'Africa', 'EUR', 'Euro', '€', NULL, -21.15, 55.50, '🇷🇪', 'U+1F1F7 U+1F1EA', 1, 1, 0),
(181, 'Romania', 'RO', '642', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Romania\", \"cn\": \"罗马尼亚\", \"de\": \"Rumänien\", \"es\": \"Rumania\", \"fa\": \"رومانی\", \"fr\": \"Roumanie\", \"hr\": \"Rumunjska\", \"it\": \"Romania\", \"ja\": \"ルーマニア\", \"kr\": \"루마니아\", \"nl\": \"Roemenië\", \"pt\": \"Roménia\", \"tr\": \"Romanya\", \"pt-BR\": \"Romênia\"}', '[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Bucharest\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]', '642', 'ROU', 'Romanian', 'Bucharest', '.ro', 'România', 'Europe', 'RON', 'Romanian leu', 'lei', NULL, 46.00, 25.00, '🇷🇴', 'U+1F1F7 U+1F1F4', 1, 1, 0),
(182, 'Russia', 'RU', '643', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Russia\", \"cn\": \"俄罗斯联邦\", \"de\": \"Russland\", \"es\": \"Rusia\", \"fa\": \"روسیه\", \"fr\": \"Russie\", \"hr\": \"Rusija\", \"it\": \"Russia\", \"ja\": \"ロシア連邦\", \"kr\": \"러시아\", \"nl\": \"Rusland\", \"pt\": \"Rússia\", \"tr\": \"Rusya\", \"pt-BR\": \"Rússia\"}', '[{\"tzName\": \"Anadyr Time[4\", \"zoneName\": \"Asia/Anadyr\", \"gmtOffset\": 43200, \"abbreviation\": \"ANAT\", \"gmtOffsetName\": \"UTC+12:00\"}, {\"tzName\": \"Krasnoyarsk Time\", \"zoneName\": \"Asia/Barnaul\", \"gmtOffset\": 25200, \"abbreviation\": \"KRAT\", \"gmtOffsetName\": \"UTC+07:00\"}, {\"tzName\": \"Yakutsk Time\", \"zoneName\": \"Asia/Chita\", \"gmtOffset\": 32400, \"abbreviation\": \"YAKT\", \"gmtOffsetName\": \"UTC+09:00\"}, {\"tzName\": \"Irkutsk Time\", \"zoneName\": \"Asia/Irkutsk\", \"gmtOffset\": 28800, \"abbreviation\": \"IRKT\", \"gmtOffsetName\": \"UTC+08:00\"}, {\"tzName\": \"Kamchatka Time\", \"zoneName\": \"Asia/Kamchatka\", \"gmtOffset\": 43200, \"abbreviation\": \"PETT\", \"gmtOffsetName\": \"UTC+12:00\"}, {\"tzName\": \"Yakutsk Time\", \"zoneName\": \"Asia/Khandyga\", \"gmtOffset\": 32400, \"abbreviation\": \"YAKT\", \"gmtOffsetName\": \"UTC+09:00\"}, {\"tzName\": \"Krasnoyarsk Time\", \"zoneName\": \"Asia/Krasnoyarsk\", \"gmtOffset\": 25200, \"abbreviation\": \"KRAT\", \"gmtOffsetName\": \"UTC+07:00\"}, {\"tzName\": \"Magadan Time\", \"zoneName\": \"Asia/Magadan\", \"gmtOffset\": 39600, \"abbreviation\": \"MAGT\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Krasnoyarsk Time\", \"zoneName\": \"Asia/Novokuznetsk\", \"gmtOffset\": 25200, \"abbreviation\": \"KRAT\", \"gmtOffsetName\": \"UTC+07:00\"}, {\"tzName\": \"Novosibirsk Time\", \"zoneName\": \"Asia/Novosibirsk\", \"gmtOffset\": 25200, \"abbreviation\": \"NOVT\", \"gmtOffsetName\": \"UTC+07:00\"}, {\"tzName\": \"Omsk Time\", \"zoneName\": \"Asia/Omsk\", \"gmtOffset\": 21600, \"abbreviation\": \"OMST\", \"gmtOffsetName\": \"UTC+06:00\"}, {\"tzName\": \"Sakhalin Island Time\", \"zoneName\": \"Asia/Sakhalin\", \"gmtOffset\": 39600, \"abbreviation\": \"SAKT\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Srednekolymsk Time\", \"zoneName\": \"Asia/Srednekolymsk\", \"gmtOffset\": 39600, \"abbreviation\": \"SRET\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Moscow Daylight Time+3\", \"zoneName\": \"Asia/Tomsk\", \"gmtOffset\": 25200, \"abbreviation\": \"MSD+3\", \"gmtOffsetName\": \"UTC+07:00\"}, {\"tzName\": \"Vladivostok Time\", \"zoneName\": \"Asia/Ust-Nera\", \"gmtOffset\": 36000, \"abbreviation\": \"VLAT\", \"gmtOffsetName\": \"UTC+10:00\"}, {\"tzName\": \"Vladivostok Time\", \"zoneName\": \"Asia/Vladivostok\", \"gmtOffset\": 36000, \"abbreviation\": \"VLAT\", \"gmtOffsetName\": \"UTC+10:00\"}, {\"tzName\": \"Yakutsk Time\", \"zoneName\": \"Asia/Yakutsk\", \"gmtOffset\": 32400, \"abbreviation\": \"YAKT\", \"gmtOffsetName\": \"UTC+09:00\"}, {\"tzName\": \"Yekaterinburg Time\", \"zoneName\": \"Asia/Yekaterinburg\", \"gmtOffset\": 18000, \"abbreviation\": \"YEKT\", \"gmtOffsetName\": \"UTC+05:00\"}, {\"tzName\": \"Samara Time\", \"zoneName\": \"Europe/Astrakhan\", \"gmtOffset\": 14400, \"abbreviation\": \"SAMT\", \"gmtOffsetName\": \"UTC+04:00\"}, {\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Kaliningrad\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}, {\"tzName\": \"Moscow Time\", \"zoneName\": \"Europe/Kirov\", \"gmtOffset\": 10800, \"abbreviation\": \"MSK\", \"gmtOffsetName\": \"UTC+03:00\"}, {\"tzName\": \"Moscow Time\", \"zoneName\": \"Europe/Moscow\", \"gmtOffset\": 10800, \"abbreviation\": \"MSK\", \"gmtOffsetName\": \"UTC+03:00\"}, {\"tzName\": \"Samara Time\", \"zoneName\": \"Europe/Samara\", \"gmtOffset\": 14400, \"abbreviation\": \"SAMT\", \"gmtOffsetName\": \"UTC+04:00\"}, {\"tzName\": \"Moscow Daylight Time+4\", \"zoneName\": \"Europe/Saratov\", \"gmtOffset\": 14400, \"abbreviation\": \"MSD\", \"gmtOffsetName\": \"UTC+04:00\"}, {\"tzName\": \"Samara Time\", \"zoneName\": \"Europe/Ulyanovsk\", \"gmtOffset\": 14400, \"abbreviation\": \"SAMT\", \"gmtOffsetName\": \"UTC+04:00\"}, {\"tzName\": \"Moscow Standard Time\", \"zoneName\": \"Europe/Volgograd\", \"gmtOffset\": 14400, \"abbreviation\": \"MSK\", \"gmtOffsetName\": \"UTC+04:00\"}]', '643', 'RUS', 'Russian', 'Moscow', '.ru', 'Россия', 'Europe', 'RUB', 'Russian ruble', '₽', NULL, 60.00, 100.00, '🇷🇺', 'U+1F1F7 U+1F1FA', 1, 1, 0),
(183, 'Rwanda', 'RW', '646', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Rwanda\", \"cn\": \"卢旺达\", \"de\": \"Ruanda\", \"es\": \"Ruanda\", \"fa\": \"رواندا\", \"fr\": \"Rwanda\", \"hr\": \"Ruanda\", \"it\": \"Ruanda\", \"ja\": \"ルワンダ\", \"kr\": \"르완다\", \"nl\": \"Rwanda\", \"pt\": \"Ruanda\", \"tr\": \"Ruanda\", \"pt-BR\": \"Ruanda\"}', '[{\"tzName\": \"Central Africa Time\", \"zoneName\": \"Africa/Kigali\", \"gmtOffset\": 7200, \"abbreviation\": \"CAT\", \"gmtOffsetName\": \"UTC+02:00\"}]', '646', 'RWA', 'Rwandan', 'Kigali', '.rw', 'Rwanda', 'Africa', 'RWF', 'Rwandan franc', 'FRw', NULL, -2.00, 30.00, '🇷🇼', 'U+1F1F7 U+1F1FC', 1, 1, 0),
(184, 'Saint-Barthelemy', 'BL', '652', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Saint-Barthelemy\", \"cn\": \"圣巴泰勒米\", \"de\": \"Saint-Barthélemy\", \"es\": \"San Bartolomé\", \"fa\": \"سن-بارتلمی\", \"fr\": \"Saint-Barthélemy\", \"hr\": \"Saint Barthélemy\", \"it\": \"Antille Francesi\", \"ja\": \"サン・バルテルミー\", \"kr\": \"생바르텔레미\", \"nl\": \"Saint Barthélemy\", \"pt\": \"São Bartolomeu\", \"tr\": \"Saint Barthélemy\", \"pt-BR\": \"São Bartolomeu\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/St_Barthelemy\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '652', 'BLM', 'Barthelemois', 'Gustavia', '.bl', 'Saint-Barthélemy', 'Americas', 'EUR', 'Euro', '€', NULL, 18.50, -63.42, '🇧🇱', 'U+1F1E7 U+1F1F1', 1, 1, 0),
(185, 'Saint Helena', 'SH', '654', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Saint Helena\", \"cn\": \"圣赫勒拿\", \"de\": \"Sankt Helena\", \"es\": \"Santa Helena\", \"fa\": \"سنت هلنا، اسنشن و تریستان دا کونا\", \"fr\": \"Sainte-Hélène\", \"hr\": \"Sveta Helena\", \"it\": \"Sant Elena\", \"ja\": \"セントヘレナ・アセンションおよびトリスタンダクーニャ\", \"kr\": \"세인트헬레나\", \"nl\": \"Sint-Helena\", \"pt\": \"Santa Helena\", \"tr\": \"Saint Helena\", \"pt-BR\": \"Santa Helena\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Atlantic/St_Helena\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]', '654', 'SHN', 'Saint Helenian', 'Jamestown', '.sh', 'Saint Helena', 'Africa', 'SHP', 'Saint Helena pound', '£', NULL, -15.95, -5.70, '🇸🇭', 'U+1F1F8 U+1F1ED', 1, 1, 0),
(186, 'Saint Kitts And Nevis', 'KN', '659', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Saint Kitts And Nevis\", \"cn\": \"圣基茨和尼维斯\", \"de\": \"St. Kitts und Nevis\", \"es\": \"San Cristóbal y Nieves\", \"fa\": \"سنت کیتس و نویس\", \"fr\": \"Saint-Christophe-et-Niévès\", \"hr\": \"Sveti Kristof i Nevis\", \"it\": \"Saint Kitts e Nevis\", \"ja\": \"セントクリストファー・ネイビス\", \"kr\": \"세인트키츠 네비스\", \"nl\": \"Saint Kitts en Nevis\", \"pt\": \"São Cristóvão e Neves\", \"tr\": \"Saint Kitts Ve Nevis\", \"pt-BR\": \"São Cristóvão e Neves\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/St_Kitts\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '659', 'KNA', 'Kittitian or Nevisian', 'Basseterre', '.kn', 'Saint Kitts and Nevis', 'Americas', 'XCD', 'Eastern Caribbean dollar', '$', NULL, 17.33, -62.75, '🇰🇳', 'U+1F1F0 U+1F1F3', 1, 1, 0),
(187, 'Saint Lucia', 'LC', '662', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Saint Lucia\", \"cn\": \"圣卢西亚\", \"de\": \"Saint Lucia\", \"es\": \"Santa Lucía\", \"fa\": \"سنت لوسیا\", \"fr\": \"Saint-Lucie\", \"hr\": \"Sveta Lucija\", \"it\": \"Santa Lucia\", \"ja\": \"セントルシア\", \"kr\": \"세인트루시아\", \"nl\": \"Saint Lucia\", \"pt\": \"Santa Lúcia\", \"tr\": \"Saint Lucia\", \"pt-BR\": \"Santa Lúcia\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/St_Lucia\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '662', 'LCA', 'Saint Lucian', 'Castries', '.lc', 'Saint Lucia', 'Americas', 'XCD', 'Eastern Caribbean dollar', '$', NULL, 13.88, -60.97, '🇱🇨', 'U+1F1F1 U+1F1E8', 1, 1, 0),
(188, 'Saint-Martin (French part)', 'MF', '663', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Saint-Martin (French part)\", \"cn\": \"密克罗尼西亚\", \"de\": \"Saint Martin\", \"es\": \"Saint Martin\", \"fa\": \"سینت مارتن\", \"fr\": \"Saint-Martin\", \"hr\": \"Sveti Martin\", \"it\": \"Saint Martin\", \"ja\": \"サン・マルタン（フランス領）\", \"kr\": \"세인트마틴 섬\", \"nl\": \"Saint-Martin\", \"pt\": \"Ilha São Martinho\", \"tr\": \"Saint Martin\", \"pt-BR\": \"Saint Martin\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Marigot\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '663', 'MAF', 'Saint-Martinoise', 'Marigot', '.mf', 'Saint-Martin', 'Americas', 'EUR', 'Euro', '€', NULL, 18.08, -63.95, '🇲🇫', 'U+1F1F2 U+1F1EB', 1, 1, 0),
(189, 'Saint Pierre and Miquelon', 'PM', '666', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Saint Pierre and Miquelon\", \"cn\": \"圣皮埃尔和密克隆\", \"de\": \"Saint-Pierre und Miquelon\", \"es\": \"San Pedro y Miquelón\", \"fa\": \"سن پیر و میکلن\", \"fr\": \"Saint-Pierre-et-Miquelon\", \"hr\": \"Sveti Petar i Mikelon\", \"it\": \"Saint-Pierre e Miquelon\", \"ja\": \"サンピエール島・ミクロン島\", \"kr\": \"생피에르 미클롱\", \"nl\": \"Saint Pierre en Miquelon\", \"pt\": \"São Pedro e Miquelon\", \"tr\": \"Saint Pierre Ve Miquelon\", \"pt-BR\": \"Saint-Pierre e Miquelon\"}', '[{\"tzName\": \"Pierre & Miquelon Daylight Time\", \"zoneName\": \"America/Miquelon\", \"gmtOffset\": -10800, \"abbreviation\": \"PMDT\", \"gmtOffsetName\": \"UTC-03:00\"}]', '666', 'SPM', 'Saint-Pierrais or Miquelonnais', 'Saint-Pierre', '.pm', 'Saint-Pierre-et-Miquelon', 'Americas', 'EUR', 'Euro', '€', NULL, 46.83, -56.33, '🇵🇲', 'U+1F1F5 U+1F1F2', 1, 1, 0),
(190, 'Saint Vincent And The Grenadines', 'VC', '670', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Saint Vincent And The Grenadines\", \"cn\": \"圣文森特和格林纳丁斯\", \"de\": \"Saint Vincent und die Grenadinen\", \"es\": \"San Vicente y Granadinas\", \"fa\": \"سنت وینسنت و گرنادین‌ها\", \"fr\": \"Saint-Vincent-et-les-Grenadines\", \"hr\": \"Sveti Vincent i Grenadini\", \"it\": \"Saint Vincent e Grenadine\", \"ja\": \"セントビンセントおよびグレナディーン諸島\", \"kr\": \"세인트빈센트 그레나딘\", \"nl\": \"Saint Vincent en de Grenadines\", \"pt\": \"São Vicente e Granadinas\", \"tr\": \"Saint Vincent Ve Grenadinler\", \"pt-BR\": \"São Vicente e Granadinas\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/St_Vincent\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '670', 'VCT', 'Saint Vincentian, Vincentian', 'Kingstown', '.vc', 'Saint Vincent and the Grenadines', 'Americas', 'XCD', 'Eastern Caribbean dollar', '$', NULL, 13.25, -61.20, '🇻🇨', 'U+1F1FB U+1F1E8', 1, 1, 0),
(191, 'Samoa', 'WS', '882', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"Samoa\", \"cn\": \"萨摩亚\", \"de\": \"Samoa\", \"es\": \"Samoa\", \"fa\": \"ساموآ\", \"fr\": \"Samoa\", \"hr\": \"Samoa\", \"it\": \"Samoa\", \"ja\": \"サモア\", \"kr\": \"사모아\", \"nl\": \"Samoa\", \"pt\": \"Samoa\", \"tr\": \"Samoa\", \"pt-BR\": \"Samoa\"}', '[{\"tzName\": \"West Samoa Time\", \"zoneName\": \"Pacific/Apia\", \"gmtOffset\": 50400, \"abbreviation\": \"WST\", \"gmtOffsetName\": \"UTC+14:00\"}]', '882', 'WSM', 'Samoan', 'Apia', '.ws', 'Samoa', 'Oceania', 'WST', 'Samoan tālā', 'SAT', NULL, -13.58, -172.33, '🇼🇸', 'U+1F1FC U+1F1F8', 1, 1, 0),
(192, 'San Marino', 'SM', '674', '2020-06-17 10:37:04', '2024-01-21 12:54:29', '{\"ar\": \"San Marino\", \"cn\": \"圣马力诺\", \"de\": \"San Marino\", \"es\": \"San Marino\", \"fa\": \"سان مارینو\", \"fr\": \"Saint-Marin\", \"hr\": \"San Marino\", \"it\": \"San Marino\", \"ja\": \"サンマリノ\", \"kr\": \"산마리노\", \"nl\": \"San Marino\", \"pt\": \"São Marinho\", \"tr\": \"San Marino\", \"pt-BR\": \"San Marino\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/San_Marino\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '674', 'SMR', 'Sammarinese', 'San Marino', '.sm', 'San Marino', 'Europe', 'EUR', 'Euro', '€', NULL, 43.77, 12.42, '🇸🇲', 'U+1F1F8 U+1F1F2', 1, 1, 0),
(193, 'Sao Tome and Principe', 'ST', '678', '2020-06-17 10:37:04', '2024-01-21 12:54:30', '{\"ar\": \"Sao Tome and Principe\", \"cn\": \"圣多美和普林西比\", \"de\": \"São Tomé und Príncipe\", \"es\": \"Santo Tomé y Príncipe\", \"fa\": \"کواترو دو فرویرو\", \"fr\": \"Sao Tomé-et-Principe\", \"hr\": \"Sveti Toma i Princip\", \"it\": \"São Tomé e Príncipe\", \"ja\": \"サントメ・プリンシペ\", \"kr\": \"상투메 프린시페\", \"nl\": \"Sao Tomé en Principe\", \"pt\": \"São Tomé e Príncipe\", \"tr\": \"Sao Tome Ve Prinsipe\", \"pt-BR\": \"São Tomé e Príncipe\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Sao_Tome\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]', '678', 'STP', 'Sao Tomean', 'Sao Tome', '.st', 'São Tomé e Príncipe', 'Africa', 'STD', 'Dobra', 'Db', NULL, 1.00, 7.00, '🇸🇹', 'U+1F1F8 U+1F1F9', 1, 1, 0),
(194, 'Saudi Arabia', 'SA', '682', '2020-06-17 10:37:04', '2024-01-21 12:54:30', '{\"ar\": \"Saudi Arabia\", \"cn\": \"沙特阿拉伯\", \"de\": \"Saudi-Arabien\", \"es\": \"Arabia Saudí\", \"fa\": \"عربستان سعودی\", \"fr\": \"Arabie Saoudite\", \"hr\": \"Saudijska Arabija\", \"it\": \"Arabia Saudita\", \"ja\": \"サウジアラビア\", \"kr\": \"사우디아라비아\", \"nl\": \"Saoedi-Arabië\", \"pt\": \"Arábia Saudita\", \"tr\": \"Suudi Arabistan\", \"pt-BR\": \"Arábia Saudita\"}', '[{\"tzName\": \"Arabia Standard Time\", \"zoneName\": \"Asia/Riyadh\", \"gmtOffset\": 10800, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC+03:00\"}]', '682', 'SAU', 'Saudi, Saudi Arabian', 'Riyadh', '.sa', 'المملكة العربية السعودية', 'Asia', 'SAR', 'Saudi riyal', '﷼', NULL, 25.00, 45.00, '🇸🇦', 'U+1F1F8 U+1F1E6', 1, 1, 1),
(195, 'Senegal', 'SN', '686', '2020-06-17 10:37:04', '2024-01-21 12:54:30', '{\"ar\": \"Senegal\", \"cn\": \"塞内加尔\", \"de\": \"Senegal\", \"es\": \"Senegal\", \"fa\": \"سنگال\", \"fr\": \"Sénégal\", \"hr\": \"Senegal\", \"it\": \"Senegal\", \"ja\": \"セネガル\", \"kr\": \"세네갈\", \"nl\": \"Senegal\", \"pt\": \"Senegal\", \"tr\": \"Senegal\", \"pt-BR\": \"Senegal\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Dakar\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]', '686', 'SEN', 'Senegalese', 'Dakar', '.sn', 'Sénégal', 'Africa', 'XOF', 'West African CFA franc', 'CFA', NULL, 14.00, -14.00, '🇸🇳', 'U+1F1F8 U+1F1F3', 1, 1, 0),
(196, 'Serbia', 'RS', '688', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Serbia\", \"cn\": \"塞尔维亚\", \"de\": \"Serbien\", \"es\": \"Serbia\", \"fa\": \"صربستان\", \"fr\": \"Serbie\", \"hr\": \"Srbija\", \"it\": \"Serbia\", \"ja\": \"セルビア\", \"kr\": \"세르비아\", \"nl\": \"Servië\", \"pt\": \"Sérvia\", \"tr\": \"Sirbistan\", \"pt-BR\": \"Sérvia\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Belgrade\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '688', 'SRB', 'Serbian', 'Belgrade', '.rs', 'Србија', 'Europe', 'RSD', 'Serbian dinar', 'din', NULL, 44.00, 21.00, '🇷🇸', 'U+1F1F7 U+1F1F8', 1, 1, 0),
(197, 'Seychelles', 'SC', '690', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Seychelles\", \"cn\": \"塞舌尔\", \"de\": \"Seychellen\", \"es\": \"Seychelles\", \"fa\": \"سیشل\", \"fr\": \"Seychelles\", \"hr\": \"Sejšeli\", \"it\": \"Seychelles\", \"ja\": \"セーシェル\", \"kr\": \"세이셸\", \"nl\": \"Seychellen\", \"pt\": \"Seicheles\", \"tr\": \"Seyşeller\", \"pt-BR\": \"Seicheles\"}', '[{\"tzName\": \"Seychelles Time\", \"zoneName\": \"Indian/Mahe\", \"gmtOffset\": 14400, \"abbreviation\": \"SCT\", \"gmtOffsetName\": \"UTC+04:00\"}]', '690', 'SYC', 'Seychellois', 'Victoria', '.sc', 'Seychelles', 'Africa', 'SCR', 'Seychellois rupee', 'SRe', NULL, -4.58, 55.67, '🇸🇨', 'U+1F1F8 U+1F1E8', 1, 1, 0),
(198, 'Sierra Leone', 'SL', '694', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Sierra Leone\", \"cn\": \"塞拉利昂\", \"de\": \"Sierra Leone\", \"es\": \"Sierra Leone\", \"fa\": \"سیرالئون\", \"fr\": \"Sierra Leone\", \"hr\": \"Sijera Leone\", \"it\": \"Sierra Leone\", \"ja\": \"シエラレオネ\", \"kr\": \"시에라리온\", \"nl\": \"Sierra Leone\", \"pt\": \"Serra Leoa\", \"tr\": \"Sierra Leone\", \"pt-BR\": \"Serra Leoa\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Freetown\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]', '694', 'SLE', 'Sierra Leonean', 'Freetown', '.sl', 'Sierra Leone', 'Africa', 'SLL', 'Sierra Leonean leone', 'Le', NULL, 8.50, -11.50, '🇸🇱', 'U+1F1F8 U+1F1F1', 1, 1, 0),
(199, 'Singapore', 'SG', '702', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Singapore\", \"cn\": \"新加坡\", \"de\": \"Singapur\", \"es\": \"Singapur\", \"fa\": \"سنگاپور\", \"fr\": \"Singapour\", \"hr\": \"Singapur\", \"it\": \"Singapore\", \"ja\": \"シンガポール\", \"kr\": \"싱가포르\", \"nl\": \"Singapore\", \"pt\": \"Singapura\", \"tr\": \"Singapur\", \"pt-BR\": \"Singapura\"}', '[{\"tzName\": \"Singapore Time\", \"zoneName\": \"Asia/Singapore\", \"gmtOffset\": 28800, \"abbreviation\": \"SGT\", \"gmtOffsetName\": \"UTC+08:00\"}]', '702', 'SGP', 'Singaporean', 'Singapur', '.sg', 'Singapore', 'Asia', 'SGD', 'Singapore dollar', '$', NULL, 1.37, 103.80, '🇸🇬', 'U+1F1F8 U+1F1EC', 1, 1, 0),
(200, 'Sint Maarten (Dutch part)', 'SX', '534', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Sint Maarten (Dutch part)\", \"cn\": \"圣马丁岛（荷兰部分）\", \"de\": \"Sint Maarten (niederl. Teil)\", \"fa\": \"سینت مارتن\", \"fr\": \"Saint Martin (partie néerlandaise)\", \"it\": \"Saint Martin (parte olandese)\", \"kr\": \"신트마르턴\", \"nl\": \"Sint Maarten\", \"pt\": \"São Martinho\", \"tr\": \"Sint Maarten\", \"pt-BR\": \"Sint Maarten\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Anguilla\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '534', 'SXM', 'Sint Maarten', 'Philipsburg', '.sx', 'Sint Maarten', 'Americas', 'ANG', 'Netherlands Antillean guilder', 'ƒ', NULL, 18.03, -63.05, '🇸🇽', 'U+1F1F8 U+1F1FD', 1, 1, 0),
(201, 'Slovakia', 'SK', '703', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Slovakia\", \"cn\": \"斯洛伐克\", \"de\": \"Slowakei\", \"es\": \"República Eslovaca\", \"fa\": \"اسلواکی\", \"fr\": \"Slovaquie\", \"hr\": \"Slovačka\", \"it\": \"Slovacchia\", \"ja\": \"スロバキア\", \"kr\": \"슬로바키아\", \"nl\": \"Slowakije\", \"pt\": \"Eslováquia\", \"tr\": \"Slovakya\", \"pt-BR\": \"Eslováquia\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Bratislava\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '703', 'SVK', 'Slovak', 'Bratislava', '.sk', 'Slovensko', 'Europe', 'EUR', 'Euro', '€', NULL, 48.67, 19.50, '🇸🇰', 'U+1F1F8 U+1F1F0', 1, 1, 0),
(202, 'Slovenia', 'SI', '705', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Slovenia\", \"cn\": \"斯洛文尼亚\", \"de\": \"Slowenien\", \"es\": \"Eslovenia\", \"fa\": \"اسلوونی\", \"fr\": \"Slovénie\", \"hr\": \"Slovenija\", \"it\": \"Slovenia\", \"ja\": \"スロベニア\", \"kr\": \"슬로베니아\", \"nl\": \"Slovenië\", \"pt\": \"Eslovénia\", \"tr\": \"Slovenya\", \"pt-BR\": \"Eslovênia\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Ljubljana\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '705', 'SVN', 'Slovenian, Slovene', 'Ljubljana', '.si', 'Slovenija', 'Europe', 'EUR', 'Euro', '€', NULL, 46.12, 14.82, '🇸🇮', 'U+1F1F8 U+1F1EE', 1, 1, 0),
(203, 'Solomon Islands', 'SB', '090', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Solomon Islands\", \"cn\": \"所罗门群岛\", \"de\": \"Salomonen\", \"es\": \"Islas Salomón\", \"fa\": \"جزایر سلیمان\", \"fr\": \"Îles Salomon\", \"hr\": \"Solomonski Otoci\", \"it\": \"Isole Salomone\", \"ja\": \"ソロモン諸島\", \"kr\": \"솔로몬 제도\", \"nl\": \"Salomonseilanden\", \"pt\": \"Ilhas Salomão\", \"tr\": \"Solomon Adalari\", \"pt-BR\": \"Ilhas Salomão\"}', '[{\"tzName\": \"Solomon Islands Time\", \"zoneName\": \"Pacific/Guadalcanal\", \"gmtOffset\": 39600, \"abbreviation\": \"SBT\", \"gmtOffsetName\": \"UTC+11:00\"}]', '090', 'SLB', 'Solomon Island', 'Honiara', '.sb', 'Solomon Islands', 'Oceania', 'SBD', 'Solomon Islands dollar', 'Si$', NULL, -8.00, 159.00, '🇸🇧', 'U+1F1F8 U+1F1E7', 1, 1, 0),
(204, 'Somalia', 'SO', '706', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Somalia\", \"cn\": \"索马里\", \"de\": \"Somalia\", \"es\": \"Somalia\", \"fa\": \"سومالی\", \"fr\": \"Somalie\", \"hr\": \"Somalija\", \"it\": \"Somalia\", \"ja\": \"ソマリア\", \"kr\": \"소말리아\", \"nl\": \"Somalië\", \"pt\": \"Somália\", \"tr\": \"Somali\", \"pt-BR\": \"Somália\"}', '[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Africa/Mogadishu\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]', '706', 'SOM', 'Somali, Somalian', 'Mogadishu', '.so', 'Soomaaliya', 'Africa', 'SOS', 'Somali shilling', 'Sh.so.', NULL, 10.00, 49.00, '🇸🇴', 'U+1F1F8 U+1F1F4', 1, 1, 0),
(205, 'South Africa', 'ZA', '710', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"South Africa\", \"cn\": \"南非\", \"de\": \"Republik Südafrika\", \"es\": \"República de Sudáfrica\", \"fa\": \"آفریقای جنوبی\", \"fr\": \"Afrique du Sud\", \"hr\": \"Južnoafrička Republika\", \"it\": \"Sud Africa\", \"ja\": \"南アフリカ\", \"kr\": \"남아프리카 공화국\", \"nl\": \"Zuid-Afrika\", \"pt\": \"República Sul-Africana\", \"tr\": \"Güney Afrika Cumhuriyeti\", \"pt-BR\": \"República Sul-Africana\"}', '[{\"tzName\": \"South African Standard Time\", \"zoneName\": \"Africa/Johannesburg\", \"gmtOffset\": 7200, \"abbreviation\": \"SAST\", \"gmtOffsetName\": \"UTC+02:00\"}]', '710', 'ZAF', 'South African', 'Pretoria', '.za', 'South Africa', 'Africa', 'ZAR', 'South African rand', 'R', NULL, -29.00, 24.00, '🇿🇦', 'U+1F1FF U+1F1E6', 1, 1, 0),
(206, 'South Georgia', 'GS', '239', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"South Georgia\", \"cn\": \"南乔治亚\", \"de\": \"Südgeorgien und die Südlichen Sandwichinseln\", \"es\": \"Islas Georgias del Sur y Sandwich del Sur\", \"fa\": \"جزایر جورجیای جنوبی و ساندویچ جنوبی\", \"fr\": \"Géorgie du Sud-et-les Îles Sandwich du Sud\", \"hr\": \"Južna Georgija i otočje Južni Sandwich\", \"it\": \"Georgia del Sud e Isole Sandwich Meridionali\", \"ja\": \"サウスジョージア・サウスサンドウィッチ諸島\", \"kr\": \"사우스조지아\", \"nl\": \"Zuid-Georgia en Zuidelijke Sandwicheilanden\", \"pt\": \"Ilhas Geórgia do Sul e Sanduíche do Sul\", \"tr\": \"Güney Georgia\", \"pt-BR\": \"Ilhas Geórgias do Sul e Sandwich do Sul\"}', '[{\"tzName\": \"South Georgia and the South Sandwich Islands Time\", \"zoneName\": \"Atlantic/South_Georgia\", \"gmtOffset\": -7200, \"abbreviation\": \"GST\", \"gmtOffsetName\": \"UTC-02:00\"}]', '239', 'SGS', 'South Georgia or South Sandwich Islands', 'Grytviken', '.gs', 'South Georgia', 'Americas', 'GBP', 'British pound', '£', NULL, -54.50, -37.00, '🇬🇸', 'U+1F1EC U+1F1F8', 1, 1, 0),
(207, 'South Sudan', 'SS', '728', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"South Sudan\", \"cn\": \"南苏丹\", \"de\": \"Südsudan\", \"es\": \"Sudán del Sur\", \"fa\": \"سودان جنوبی\", \"fr\": \"Soudan du Sud\", \"hr\": \"Južni Sudan\", \"it\": \"Sudan del sud\", \"ja\": \"南スーダン\", \"kr\": \"남수단\", \"nl\": \"Zuid-Soedan\", \"pt\": \"Sudão do Sul\", \"tr\": \"Güney Sudan\", \"pt-BR\": \"Sudão do Sul\"}', '[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Africa/Juba\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]', '728', 'SSD', 'South Sudanese', 'Juba', '.ss', 'South Sudan', 'Africa', 'SSP', 'South Sudanese pound', '£', NULL, 7.00, 30.00, '🇸🇸', 'U+1F1F8 U+1F1F8', 1, 1, 0),
(208, 'Spain', 'ES', '724', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Spain\", \"cn\": \"西班牙\", \"de\": \"Spanien\", \"es\": \"España\", \"fa\": \"اسپانیا\", \"fr\": \"Espagne\", \"hr\": \"Španjolska\", \"it\": \"Spagna\", \"ja\": \"スペイン\", \"kr\": \"스페인\", \"nl\": \"Spanje\", \"pt\": \"Espanha\", \"tr\": \"İspanya\", \"pt-BR\": \"Espanha\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Africa/Ceuta\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}, {\"tzName\": \"Western European Time\", \"zoneName\": \"Atlantic/Canary\", \"gmtOffset\": 0, \"abbreviation\": \"WET\", \"gmtOffsetName\": \"UTC±00\"}, {\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Madrid\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '724', 'ESP', 'Spanish', 'Madrid', '.es', 'España', 'Europe', 'EUR', 'Euro', '€', NULL, 40.00, -4.00, '🇪🇸', 'U+1F1EA U+1F1F8', 1, 1, 0),
(209, 'Sri Lanka', 'LK', '144', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Sri Lanka\", \"cn\": \"斯里兰卡\", \"de\": \"Sri Lanka\", \"es\": \"Sri Lanka\", \"fa\": \"سری‌لانکا\", \"fr\": \"Sri Lanka\", \"hr\": \"Šri Lanka\", \"it\": \"Sri Lanka\", \"ja\": \"スリランカ\", \"kr\": \"스리랑카\", \"nl\": \"Sri Lanka\", \"pt\": \"Sri Lanka\", \"tr\": \"Sri Lanka\", \"pt-BR\": \"Sri Lanka\"}', '[{\"tzName\": \"Indian Standard Time\", \"zoneName\": \"Asia/Colombo\", \"gmtOffset\": 19800, \"abbreviation\": \"IST\", \"gmtOffsetName\": \"UTC+05:30\"}]', '144', 'LKA', 'Sri Lankan', 'Colombo', '.lk', 'śrī laṃkāva', 'Asia', 'LKR', 'Sri Lankan rupee', 'Rs', NULL, 7.00, 81.00, '🇱🇰', 'U+1F1F1 U+1F1F0', 1, 1, 0),
(210, 'Sudan', 'SD', '729', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Sudan\", \"cn\": \"苏丹\", \"de\": \"Sudan\", \"es\": \"Sudán\", \"fa\": \"سودان\", \"fr\": \"Soudan\", \"hr\": \"Sudan\", \"it\": \"Sudan\", \"ja\": \"スーダン\", \"kr\": \"수단\", \"nl\": \"Soedan\", \"pt\": \"Sudão\", \"tr\": \"Sudan\", \"pt-BR\": \"Sudão\"}', '[{\"tzName\": \"Eastern African Time\", \"zoneName\": \"Africa/Khartoum\", \"gmtOffset\": 7200, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+02:00\"}]', '729', 'SDN', 'Sudanese', 'Khartoum', '.sd', 'السودان', 'Africa', 'SDG', 'Sudanese pound', '.س.ج', NULL, 15.00, 30.00, '🇸🇩', 'U+1F1F8 U+1F1E9', 1, 1, 0),
(211, 'Suriname', 'SR', '740', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Suriname\", \"cn\": \"苏里南\", \"de\": \"Suriname\", \"es\": \"Surinam\", \"fa\": \"سورینام\", \"fr\": \"Surinam\", \"hr\": \"Surinam\", \"it\": \"Suriname\", \"ja\": \"スリナム\", \"kr\": \"수리남\", \"nl\": \"Suriname\", \"pt\": \"Suriname\", \"tr\": \"Surinam\", \"pt-BR\": \"Suriname\"}', '[{\"tzName\": \"Suriname Time\", \"zoneName\": \"America/Paramaribo\", \"gmtOffset\": -10800, \"abbreviation\": \"SRT\", \"gmtOffsetName\": \"UTC-03:00\"}]', '740', 'SUR', 'Surinamese', 'Paramaribo', '.sr', 'Suriname', 'Americas', 'SRD', 'Surinamese dollar', '$', NULL, 4.00, -56.00, '🇸🇷', 'U+1F1F8 U+1F1F7', 1, 1, 0),
(212, 'Svalbard And Jan Mayen Islands', 'SJ', '744', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Svalbard And Jan Mayen Islands\", \"cn\": \"斯瓦尔巴和扬马延群岛\", \"de\": \"Svalbard und Jan Mayen\", \"es\": \"Islas Svalbard y Jan Mayen\", \"fa\": \"سوالبارد و یان ماین\", \"fr\": \"Svalbard et Jan Mayen\", \"hr\": \"Svalbard i Jan Mayen\", \"it\": \"Svalbard e Jan Mayen\", \"ja\": \"スヴァールバル諸島およびヤンマイエン島\", \"kr\": \"스발바르 얀마옌 제도\", \"nl\": \"Svalbard en Jan Mayen\", \"pt\": \"Svalbard\", \"tr\": \"Svalbard Ve Jan Mayen\", \"pt-BR\": \"Svalbard\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Arctic/Longyearbyen\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '744', 'SJM', 'Svalbard', 'Longyearbyen', '.sj', 'Svalbard og Jan Mayen', 'Europe', 'NOK', 'Norwegian Krone', 'kr', NULL, 78.00, 20.00, '🇸🇯', 'U+1F1F8 U+1F1EF', 1, 1, 0),
(213, 'Swaziland', 'SZ', '748', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Swaziland\", \"cn\": \"斯威士兰\", \"de\": \"Swasiland\", \"es\": \"Suazilandia\", \"fa\": \"سوازیلند\", \"fr\": \"Swaziland\", \"hr\": \"Svazi\", \"it\": \"Swaziland\", \"ja\": \"スワジランド\", \"kr\": \"에스와티니\", \"nl\": \"Swaziland\", \"pt\": \"Suazilândia\", \"tr\": \"Esvatini\", \"pt-BR\": \"Suazilândia\"}', '[{\"tzName\": \"South African Standard Time\", \"zoneName\": \"Africa/Mbabane\", \"gmtOffset\": 7200, \"abbreviation\": \"SAST\", \"gmtOffsetName\": \"UTC+02:00\"}]', '748', 'SWZ', 'Swazi', 'Mbabane', '.sz', 'Swaziland', 'Africa', 'SZL', 'Lilangeni', 'E', NULL, -26.50, 31.50, '🇸🇿', 'U+1F1F8 U+1F1FF', 1, 1, 0),
(214, 'Sweden', 'SE', '752', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Sweden\", \"cn\": \"瑞典\", \"de\": \"Schweden\", \"es\": \"Suecia\", \"fa\": \"سوئد\", \"fr\": \"Suède\", \"hr\": \"Švedska\", \"it\": \"Svezia\", \"ja\": \"スウェーデン\", \"kr\": \"스웨덴\", \"nl\": \"Zweden\", \"pt\": \"Suécia\", \"tr\": \"İsveç\", \"pt-BR\": \"Suécia\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Stockholm\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '752', 'SWE', 'Swedish', 'Stockholm', '.se', 'Sverige', 'Europe', 'SEK', 'Swedish krona', 'kr', NULL, 62.00, 15.00, '🇸🇪', 'U+1F1F8 U+1F1EA', 1, 1, 0),
(215, 'Switzerland', 'CH', '756', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Switzerland\", \"cn\": \"瑞士\", \"de\": \"Schweiz\", \"es\": \"Suiza\", \"fa\": \"سوئیس\", \"fr\": \"Suisse\", \"hr\": \"Švicarska\", \"it\": \"Svizzera\", \"ja\": \"スイス\", \"kr\": \"스위스\", \"nl\": \"Zwitserland\", \"pt\": \"Suíça\", \"tr\": \"İsviçre\", \"pt-BR\": \"Suíça\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Zurich\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '756', 'CHE', 'Swiss', 'Bern', '.ch', 'Schweiz', 'Europe', 'CHF', 'Swiss franc', 'CHf', NULL, 47.00, 8.00, '🇨🇭', 'U+1F1E8 U+1F1ED', 1, 1, 0),
(216, 'Syria', 'SY', '760', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Syria\", \"cn\": \"叙利亚\", \"de\": \"Syrien\", \"es\": \"Siria\", \"fa\": \"سوریه\", \"fr\": \"Syrie\", \"hr\": \"Sirija\", \"it\": \"Siria\", \"ja\": \"シリア・アラブ共和国\", \"kr\": \"시리아\", \"nl\": \"Syrië\", \"pt\": \"Síria\", \"tr\": \"Suriye\", \"pt-BR\": \"Síria\"}', '[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Asia/Damascus\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]', '760', 'SYR', 'Syrian', 'Damascus', '.sy', 'سوريا', 'Asia', 'SYP', 'Syrian pound', 'LS', NULL, 35.00, 38.00, '🇸🇾', 'U+1F1F8 U+1F1FE', 1, 1, 0),
(217, 'Taiwan', 'TW', '158', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Taiwan\", \"cn\": \"中国台湾\", \"de\": \"Taiwan\", \"es\": \"Taiwán\", \"fa\": \"تایوان\", \"fr\": \"Taïwan\", \"hr\": \"Tajvan\", \"it\": \"Taiwan\", \"ja\": \"台湾（中華民国）\", \"kr\": \"대만\", \"nl\": \"Taiwan\", \"pt\": \"Taiwan\", \"tr\": \"Tayvan\", \"pt-BR\": \"Taiwan\"}', '[{\"tzName\": \"China Standard Time\", \"zoneName\": \"Asia/Taipei\", \"gmtOffset\": 28800, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC+08:00\"}]', '158', 'TWN', 'Chinese, Taiwanese', 'Taipei', '.tw', '臺灣', 'Asia', 'TWD', 'New Taiwan dollar', '$', NULL, 23.50, 121.00, '🇹🇼', 'U+1F1F9 U+1F1FC', 1, 1, 0),
(218, 'Tajikistan', 'TJ', '762', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Tajikistan\", \"cn\": \"塔吉克斯坦\", \"de\": \"Tadschikistan\", \"es\": \"Tayikistán\", \"fa\": \"تاجیکستان\", \"fr\": \"Tadjikistan\", \"hr\": \"Tađikistan\", \"it\": \"Tagikistan\", \"ja\": \"タジキスタン\", \"kr\": \"타지키스탄\", \"nl\": \"Tadzjikistan\", \"pt\": \"Tajiquistão\", \"tr\": \"Tacikistan\", \"pt-BR\": \"Tajiquistão\"}', '[{\"tzName\": \"Tajikistan Time\", \"zoneName\": \"Asia/Dushanbe\", \"gmtOffset\": 18000, \"abbreviation\": \"TJT\", \"gmtOffsetName\": \"UTC+05:00\"}]', '762', 'TJK', 'Tajikistani', 'Dushanbe', '.tj', 'Тоҷикистон', 'Asia', 'TJS', 'Tajikistani somoni', 'SM', NULL, 39.00, 71.00, '🇹🇯', 'U+1F1F9 U+1F1EF', 1, 1, 0),
(219, 'Tanzania', 'TZ', '834', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Tanzania\", \"cn\": \"坦桑尼亚\", \"de\": \"Tansania\", \"es\": \"Tanzania\", \"fa\": \"تانزانیا\", \"fr\": \"Tanzanie\", \"hr\": \"Tanzanija\", \"it\": \"Tanzania\", \"ja\": \"タンザニア\", \"kr\": \"탄자니아\", \"nl\": \"Tanzania\", \"pt\": \"Tanzânia\", \"tr\": \"Tanzanya\", \"pt-BR\": \"Tanzânia\"}', '[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Africa/Dar_es_Salaam\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]', '834', 'TZA', 'Tanzanian', 'Dodoma', '.tz', 'Tanzania', 'Africa', 'TZS', 'Tanzanian shilling', 'TSh', NULL, -6.00, 35.00, '🇹🇿', 'U+1F1F9 U+1F1FF', 1, 1, 0),
(220, 'Thailand', 'TH', '764', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Thailand\", \"cn\": \"泰国\", \"de\": \"Thailand\", \"es\": \"Tailandia\", \"fa\": \"تایلند\", \"fr\": \"Thaïlande\", \"hr\": \"Tajland\", \"it\": \"Tailandia\", \"ja\": \"タイ\", \"kr\": \"태국\", \"nl\": \"Thailand\", \"pt\": \"Tailândia\", \"tr\": \"Tayland\", \"pt-BR\": \"Tailândia\"}', '[{\"tzName\": \"Indochina Time\", \"zoneName\": \"Asia/Bangkok\", \"gmtOffset\": 25200, \"abbreviation\": \"ICT\", \"gmtOffsetName\": \"UTC+07:00\"}]', '764', 'THA', 'Thai', 'Bangkok', '.th', 'ประเทศไทย', 'Asia', 'THB', 'Thai baht', '฿', NULL, 15.00, 100.00, '🇹🇭', 'U+1F1F9 U+1F1ED', 1, 1, 0),
(221, 'East Timor', 'TL', '626', '2020-06-17 10:37:05', '2024-01-21 12:54:28', '{\"ar\": \"East Timor\", \"cn\": \"东帝汶\", \"de\": \"Timor-Leste\", \"es\": \"Timor Oriental\", \"fa\": \"تیمور شرقی\", \"fr\": \"Timor oriental\", \"hr\": \"Istočni Timor\", \"it\": \"Timor Est\", \"ja\": \"東ティモール\", \"kr\": \"동티모르\", \"nl\": \"Oost-Timor\", \"pt\": \"Timor Leste\", \"tr\": \"Doğu Timor\", \"pt-BR\": \"Timor Leste\"}', '[{\"tzName\": \"Timor Leste Time\", \"zoneName\": \"Asia/Dili\", \"gmtOffset\": 32400, \"abbreviation\": \"TLT\", \"gmtOffsetName\": \"UTC+09:00\"}]', '626', 'TLS', 'Timorese', 'Dili', '.tl', 'Timor-Leste', 'Asia', 'USD', 'United States dollar', '$', NULL, -8.83, 125.92, '🇹🇱', 'U+1F1F9 U+1F1F1', 1, 1, 0),
(222, 'Togo', 'TG', '768', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Togo\", \"cn\": \"多哥\", \"de\": \"Togo\", \"es\": \"Togo\", \"fa\": \"توگو\", \"fr\": \"Togo\", \"hr\": \"Togo\", \"it\": \"Togo\", \"ja\": \"トーゴ\", \"kr\": \"토고\", \"nl\": \"Togo\", \"pt\": \"Togo\", \"tr\": \"Togo\", \"pt-BR\": \"Togo\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Lome\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]', '768', 'TGO', 'Togolese', 'Lome', '.tg', 'Togo', 'Africa', 'XOF', 'West African CFA franc', 'CFA', NULL, 8.00, 1.17, '🇹🇬', 'U+1F1F9 U+1F1EC', 1, 1, 0),
(223, 'Tokelau', 'TK', '772', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Tokelau\", \"cn\": \"托克劳\", \"de\": \"Tokelau\", \"es\": \"Islas Tokelau\", \"fa\": \"توکلائو\", \"fr\": \"Tokelau\", \"hr\": \"Tokelau\", \"it\": \"Isole Tokelau\", \"ja\": \"トケラウ\", \"kr\": \"토켈라우\", \"nl\": \"Tokelau\", \"pt\": \"Toquelau\", \"tr\": \"Tokelau\", \"pt-BR\": \"Tokelau\"}', '[{\"tzName\": \"Tokelau Time\", \"zoneName\": \"Pacific/Fakaofo\", \"gmtOffset\": 46800, \"abbreviation\": \"TKT\", \"gmtOffsetName\": \"UTC+13:00\"}]', '772', 'TKL', 'Tokelauan', '', '.tk', 'Tokelau', 'Oceania', 'NZD', 'New Zealand dollar', '$', NULL, -9.00, -172.00, '🇹🇰', 'U+1F1F9 U+1F1F0', 1, 1, 0),
(224, 'Tonga', 'TO', '776', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Tonga\", \"cn\": \"汤加\", \"de\": \"Tonga\", \"es\": \"Tonga\", \"fa\": \"تونگا\", \"fr\": \"Tonga\", \"hr\": \"Tonga\", \"it\": \"Tonga\", \"ja\": \"トンガ\", \"kr\": \"통가\", \"nl\": \"Tonga\", \"pt\": \"Tonga\", \"tr\": \"Tonga\", \"pt-BR\": \"Tonga\"}', '[{\"tzName\": \"Tonga Time\", \"zoneName\": \"Pacific/Tongatapu\", \"gmtOffset\": 46800, \"abbreviation\": \"TOT\", \"gmtOffsetName\": \"UTC+13:00\"}]', '776', 'TON', 'Tongan', 'Nuku alofa', '.to', 'Tonga', 'Oceania', 'TOP', 'Tongan paʻanga', '$', NULL, -20.00, -175.00, '🇹🇴', 'U+1F1F9 U+1F1F4', 1, 1, 0),
(225, 'Trinidad And Tobago', 'TT', '780', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Trinidad And Tobago\", \"cn\": \"特立尼达和多巴哥\", \"de\": \"Trinidad und Tobago\", \"es\": \"Trinidad y Tobago\", \"fa\": \"ترینیداد و توباگو\", \"fr\": \"Trinité et Tobago\", \"hr\": \"Trinidad i Tobago\", \"it\": \"Trinidad e Tobago\", \"ja\": \"トリニダード・トバゴ\", \"kr\": \"트리니다드 토바고\", \"nl\": \"Trinidad en Tobago\", \"pt\": \"Trindade e Tobago\", \"tr\": \"Trinidad Ve Tobago\", \"pt-BR\": \"Trinidad e Tobago\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Port_of_Spain\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '780', 'TTO', 'Trinidadian or Tobagonian', 'Port of Spain', '.tt', 'Trinidad and Tobago', 'Americas', 'TTD', 'Trinidad and Tobago dollar', '$', NULL, 11.00, -61.00, '🇹🇹', 'U+1F1F9 U+1F1F9', 1, 1, 0),
(226, 'Tunisia', 'TN', '788', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Tunisia\", \"cn\": \"突尼斯\", \"de\": \"Tunesien\", \"es\": \"Túnez\", \"fa\": \"تونس\", \"fr\": \"Tunisie\", \"hr\": \"Tunis\", \"it\": \"Tunisia\", \"ja\": \"チュニジア\", \"kr\": \"튀니지\", \"nl\": \"Tunesië\", \"pt\": \"Tunísia\", \"tr\": \"Tunus\", \"pt-BR\": \"Tunísia\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Africa/Tunis\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '788', 'TUN', 'Tunisian', 'Tunis', '.tn', 'تونس', 'Africa', 'TND', 'Tunisian dinar', 'ت.د', NULL, 34.00, 9.00, '🇹🇳', 'U+1F1F9 U+1F1F3', 1, 1, 0),
(227, 'Turkey', 'TR', '792', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Turkey\", \"cn\": \"土耳其\", \"de\": \"Türkei\", \"es\": \"Turquía\", \"fa\": \"ترکیه\", \"fr\": \"Turquie\", \"hr\": \"Turska\", \"it\": \"Turchia\", \"ja\": \"トルコ\", \"kr\": \"터키\", \"nl\": \"Turkije\", \"pt\": \"Turquia\", \"tr\": \"Türkiye\", \"pt-BR\": \"Turquia\"}', '[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Istanbul\", \"gmtOffset\": 10800, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+03:00\"}]', '792', 'TUR', 'Turkish', 'Ankara', '.tr', 'Türkiye', 'Asia', 'TRY', 'Turkish lira', '₺', NULL, 39.00, 35.00, '🇹🇷', 'U+1F1F9 U+1F1F7', 1, 1, 0),
(228, 'Turkmenistan', 'TM', '795', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Turkmenistan\", \"cn\": \"土库曼斯坦\", \"de\": \"Turkmenistan\", \"es\": \"Turkmenistán\", \"fa\": \"ترکمنستان\", \"fr\": \"Turkménistan\", \"hr\": \"Turkmenistan\", \"it\": \"Turkmenistan\", \"ja\": \"トルクメニスタン\", \"kr\": \"투르크메니스탄\", \"nl\": \"Turkmenistan\", \"pt\": \"Turquemenistão\", \"tr\": \"Türkmenistan\", \"pt-BR\": \"Turcomenistão\"}', '[{\"tzName\": \"Turkmenistan Time\", \"zoneName\": \"Asia/Ashgabat\", \"gmtOffset\": 18000, \"abbreviation\": \"TMT\", \"gmtOffsetName\": \"UTC+05:00\"}]', '795', 'TKM', 'Turkmen', 'Ashgabat', '.tm', 'Türkmenistan', 'Asia', 'TMT', 'Turkmenistan manat', 'T', NULL, 40.00, 60.00, '🇹🇲', 'U+1F1F9 U+1F1F2', 1, 1, 0),
(229, 'Turks And Caicos Islands', 'TC', '796', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Turks And Caicos Islands\", \"cn\": \"特克斯和凯科斯群岛\", \"de\": \"Turks- und Caicosinseln\", \"es\": \"Islas Turks y Caicos\", \"fa\": \"جزایر تورکس و کایکوس\", \"fr\": \"Îles Turques-et-Caïques\", \"hr\": \"Otoci Turks i Caicos\", \"it\": \"Isole Turks e Caicos\", \"ja\": \"タークス・カイコス諸島\", \"kr\": \"터크스 케이커스 제도\", \"nl\": \"Turks- en Caicoseilanden\", \"pt\": \"Ilhas Turcas e Caicos\", \"tr\": \"Turks Ve Caicos Adalari\", \"pt-BR\": \"Ilhas Turcas e Caicos\"}', '[{\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Grand_Turk\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}]', '796', 'TCA', 'Turks and Caicos Island', 'Cockburn Town', '.tc', 'Turks and Caicos Islands', 'Americas', 'USD', 'United States dollar', '$', NULL, 21.75, -71.58, '🇹🇨', 'U+1F1F9 U+1F1E8', 1, 1, 0),
(230, 'Tuvalu', 'TV', '798', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Tuvalu\", \"cn\": \"图瓦卢\", \"de\": \"Tuvalu\", \"es\": \"Tuvalu\", \"fa\": \"تووالو\", \"fr\": \"Tuvalu\", \"hr\": \"Tuvalu\", \"it\": \"Tuvalu\", \"ja\": \"ツバル\", \"kr\": \"투발루\", \"nl\": \"Tuvalu\", \"pt\": \"Tuvalu\", \"tr\": \"Tuvalu\", \"pt-BR\": \"Tuvalu\"}', '[{\"tzName\": \"Tuvalu Time\", \"zoneName\": \"Pacific/Funafuti\", \"gmtOffset\": 43200, \"abbreviation\": \"TVT\", \"gmtOffsetName\": \"UTC+12:00\"}]', '798', 'TUV', 'Tuvaluan', 'Funafuti', '.tv', 'Tuvalu', 'Oceania', 'AUD', 'Australian dollar', '$', NULL, -8.00, 178.00, '🇹🇻', 'U+1F1F9 U+1F1FB', 1, 1, 0),
(231, 'Uganda', 'UG', '800', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Uganda\", \"cn\": \"乌干达\", \"de\": \"Uganda\", \"es\": \"Uganda\", \"fa\": \"اوگاندا\", \"fr\": \"Uganda\", \"hr\": \"Uganda\", \"it\": \"Uganda\", \"ja\": \"ウガンダ\", \"kr\": \"우간다\", \"nl\": \"Oeganda\", \"pt\": \"Uganda\", \"tr\": \"Uganda\", \"pt-BR\":\"Uganda\"}', '[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Africa/Kampala\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]', '800', 'UGA', 'Ugandan', 'Kampala', '.ug', 'Uganda', 'Africa', 'UGX', 'Ugandan shilling', 'USh', NULL, 1.00, 32.00, '🇺🇬', 'U+1F1FA U+1F1EC', 1, 1, 0),
(232, 'Ukraine', 'UA', '804', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Ukraine\", \"cn\": \"乌克兰\", \"de\": \"Ukraine\", \"es\": \"Ucrania\", \"fa\": \"وکراین\", \"fr\": \"Ukraine\", \"hr\": \"Ukrajina\", \"it\": \"Ucraina\", \"ja\": \"ウクライナ\", \"kr\": \"우크라이나\", \"nl\": \"Oekraïne\", \"pt\": \"Ucrânia\", \"tr\": \"Ukrayna\", \"pt-BR\": \"Ucrânia\"}', '[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Kiev\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}, {\"tzName\": \"Moscow Time\", \"zoneName\": \"Europe/Simferopol\", \"gmtOffset\": 10800, \"abbreviation\": \"MSK\", \"gmtOffsetName\": \"UTC+03:00\"}, {\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Uzhgorod\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}, {\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Zaporozhye\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]', '804', 'UKR', 'Ukrainian', 'Kyiv', '.ua', 'Україна', 'Europe', 'UAH', 'Ukrainian hryvnia', '₴', NULL, 49.00, 32.00, '🇺🇦', 'U+1F1FA U+1F1E6', 1, 1, 0),
(233, 'United Arab Emirates', 'AE', '784', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"United Arab Emirates\", \"cn\": \"阿拉伯联合酋长国\", \"de\": \"Vereinigte Arabische Emirate\", \"es\": \"Emiratos Árabes Unidos\", \"fa\": \"امارات متحده عربی\", \"fr\": \"Émirats arabes unis\", \"hr\": \"Ujedinjeni Arapski Emirati\", \"it\": \"Emirati Arabi Uniti\", \"ja\": \"アラブ首長国連邦\", \"kr\": \"아랍에미리트\", \"nl\": \"Verenigde Arabische Emiraten\", \"pt\": \"Emirados árabes Unidos\", \"tr\": \"Birleşik Arap Emirlikleri\", \"pt-BR\": \"Emirados árabes Unidos\"}', '[{\"tzName\": \"Gulf Standard Time\", \"zoneName\": \"Asia/Dubai\", \"gmtOffset\": 14400, \"abbreviation\": \"GST\", \"gmtOffsetName\": \"UTC+04:00\"}]', '784', 'ARE', 'Emirati, Emirian, Emiri', 'Abu Dhabi', '.ae', 'دولة الإمارات العربية المتحدة', 'Asia', 'AED', 'United Arab Emirates dirham', 'إ.د', NULL, 24.00, 54.00, '🇦🇪', 'U+1F1E6 U+1F1EA', 1, 1, 2),
(234, 'United Kingdom', 'GB', '826', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"United Kingdom\", \"cn\": \"英国\", \"de\": \"Vereinigtes Königreich\", \"es\": \"Reino Unido\", \"fa\": \"بریتانیای کبیر و ایرلند شمالی\", \"fr\": \"Royaume-Uni\", \"hr\": \"Ujedinjeno Kraljevstvo\", \"it\": \"Regno Unito\", \"ja\": \"イギリス\", \"kr\": \"영국\", \"nl\": \"Verenigd Koninkrijk\", \"pt\": \"Reino Unido\", \"tr\": \"Birleşik Krallik\", \"pt-BR\": \"Reino Unido\"}', '[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Europe/London\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]', '826', 'GBR', 'British, UK', 'London', '.uk', 'United Kingdom', 'Europe', 'GBP', 'British pound', '£', NULL, 54.00, -2.00, '🇬🇧', 'U+1F1EC U+1F1E7', 1, 1, 0),
(235, 'United States', 'US', '840', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"United States\", \"cn\": \"美国\", \"de\": \"Vereinigte Staaten von Amerika\", \"es\": \"Estados Unidos\", \"fa\": \"ایالات متحده آمریکا\", \"fr\": \"États-Unis\", \"hr\": \"Sjedinjene Američke Države\", \"it\": \"Stati Uniti D America\", \"ja\": \"アメリカ合衆国\", \"kr\": \"미국\", \"nl\": \"Verenigde Staten\", \"pt\": \"Estados Unidos\", \"tr\": \"Amerika\", \"pt-BR\": \"Estados Unidos\"}', '[{\"tzName\": \"Hawaii–Aleutian Standard Time\", \"zoneName\": \"America/Adak\", \"gmtOffset\": -36000, \"abbreviation\": \"HST\", \"gmtOffsetName\": \"UTC-10:00\"}, {\"tzName\": \"Alaska Standard Time\", \"zoneName\": \"America/Anchorage\", \"gmtOffset\": -32400, \"abbreviation\": \"AKST\", \"gmtOffsetName\": \"UTC-09:00\"}, {\"tzName\": \"Mountain Standard Time (North America\", \"zoneName\": \"America/Boise\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Chicago\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Mountain Standard Time (North America\", \"zoneName\": \"America/Denver\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Detroit\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Indiana/Indianapolis\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Indiana/Knox\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Indiana/Marengo\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Indiana/Petersburg\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Indiana/Tell_City\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Indiana/Vevay\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Indiana/Vincennes\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Indiana/Winamac\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Alaska Standard Time\", \"zoneName\": \"America/Juneau\", \"gmtOffset\": -32400, \"abbreviation\": \"AKST\", \"gmtOffsetName\": \"UTC-09:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Kentucky/Louisville\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Kentucky/Monticello\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Pacific Standard Time (North America\", \"zoneName\": \"America/Los_Angeles\", \"gmtOffset\": -28800, \"abbreviation\": \"PST\", \"gmtOffsetName\": \"UTC-08:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Menominee\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Alaska Standard Time\", \"zoneName\": \"America/Metlakatla\", \"gmtOffset\": -32400, \"abbreviation\": \"AKST\", \"gmtOffsetName\": \"UTC-09:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/New_York\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Alaska Standard Time\", \"zoneName\": \"America/Nome\", \"gmtOffset\": -32400, \"abbreviation\": \"AKST\", \"gmtOffsetName\": \"UTC-09:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/North_Dakota/Beulah\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/North_Dakota/Center\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/North_Dakota/New_Salem\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Mountain Standard Time (North America\", \"zoneName\": \"America/Phoenix\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Alaska Standard Time\", \"zoneName\": \"America/Sitka\", \"gmtOffset\": -32400, \"abbreviation\": \"AKST\", \"gmtOffsetName\": \"UTC-09:00\"}, {\"tzName\": \"Alaska Standard Time\", \"zoneName\": \"America/Yakutat\", \"gmtOffset\": -32400, \"abbreviation\": \"AKST\", \"gmtOffsetName\": \"UTC-09:00\"}, {\"tzName\": \"Hawaii–Aleutian Standard Time\", \"zoneName\": \"Pacific/Honolulu\", \"gmtOffset\": -36000, \"abbreviation\": \"HST\", \"gmtOffsetName\": \"UTC-10:00\"}]', '840', 'USA', 'American', 'Washington', '.us', 'United States', 'Americas', 'USD', 'United States dollar', '$', NULL, 38.00, -97.00, '🇺🇸', 'U+1F1FA U+1F1F8', 1, 1, 0),
(236, 'United States Minor Outlying Islands', 'UM', '581', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"United States Minor Outlying Islands\", \"cn\": \"美国本土外小岛屿\", \"de\": \"Kleinere Inselbesitzungen der Vereinigten Staaten\", \"es\": \"Islas Ultramarinas Menores de Estados Unidos\", \"fa\": \"جزایر کوچک حاشیه‌ای ایالات متحده آمریکا\", \"fr\": \"Îles mineures éloignées des États-Unis\", \"hr\": \"Mali udaljeni otoci SAD-a\", \"it\": \"Isole minori esterne degli Stati Uniti d America\", \"ja\": \"合衆国領有小離島\", \"kr\": \"미국령 군소 제도\", \"nl\": \"Kleine afgelegen eilanden van de Verenigde Staten\", \"pt\": \"Ilhas Menores Distantes dos Estados Unidos\", \"tr\": \"Abd Küçük Harici Adalari\", \"pt-BR\": \"Ilhas Menores Distantes dos Estados Unidos\"}', '[{\"tzName\": \"Samoa Standard Time\", \"zoneName\": \"Pacific/Midway\", \"gmtOffset\": -39600, \"abbreviation\": \"SST\", \"gmtOffsetName\": \"UTC-11:00\"}, {\"tzName\": \"Wake Island Time\", \"zoneName\": \"Pacific/Wake\", \"gmtOffset\": 43200, \"abbreviation\": \"WAKT\", \"gmtOffsetName\": \"UTC+12:00\"}]', '581', 'UMI', 'American', '', '.us', 'United States Minor Outlying Islands', 'Americas', 'USD', 'United States dollar', '$', NULL, 0.00, 0.00, '🇺🇲', 'U+1F1FA U+1F1F2', 1, 1, 0),
(237, 'Uruguay', 'UY', '858', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Uruguay\", \"cn\": \"乌拉圭\", \"de\": \"Uruguay\", \"es\": \"Uruguay\", \"fa\": \"اروگوئه\", \"fr\": \"Uruguay\", \"hr\": \"Urugvaj\", \"it\": \"Uruguay\", \"ja\": \"ウルグアイ\", \"kr\": \"우루과이\", \"nl\": \"Uruguay\", \"pt\": \"Uruguai\", \"tr\": \"Uruguay\", \"pt-BR\": \"Uruguai\"}', '[{\"tzName\": \"Uruguay Standard Time\", \"zoneName\": \"America/Montevideo\", \"gmtOffset\": -10800, \"abbreviation\": \"UYT\", \"gmtOffsetName\": \"UTC-03:00\"}]', '858', 'URY', 'Uruguayan', 'Montevideo', '.uy', 'Uruguay', 'Americas', 'UYU', 'Uruguayan peso', '$', NULL, -33.00, -56.00, '🇺🇾', 'U+1F1FA U+1F1FE', 1, 1, 0),
(238, 'Uzbekistan', 'UZ', '860', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Uzbekistan\", \"cn\": \"乌兹别克斯坦\", \"de\": \"Usbekistan\", \"es\": \"Uzbekistán\", \"fa\": \"ازبکستان\", \"fr\": \"Ouzbékistan\", \"hr\": \"Uzbekistan\", \"it\": \"Uzbekistan\", \"ja\": \"ウズベキスタン\", \"kr\": \"우즈베키스탄\", \"nl\": \"Oezbekistan\", \"pt\": \"Usbequistão\", \"tr\": \"Özbekistan\", \"pt-BR\": \"Uzbequistão\"}', '[{\"tzName\": \"Uzbekistan Time\", \"zoneName\": \"Asia/Samarkand\", \"gmtOffset\": 18000, \"abbreviation\": \"UZT\", \"gmtOffsetName\": \"UTC+05:00\"}, {\"tzName\": \"Uzbekistan Time\", \"zoneName\": \"Asia/Tashkent\", \"gmtOffset\": 18000, \"abbreviation\": \"UZT\", \"gmtOffsetName\": \"UTC+05:00\"}]', '860', 'UZB', 'Uzbekistani, Uzbek', 'Tashkent', '.uz', 'O‘zbekiston', 'Asia', 'UZS', 'Uzbekistani soʻm', 'лв', NULL, 41.00, 64.00, '🇺🇿', 'U+1F1FA U+1F1FF', 1, 1, 0),
(239, 'Vanuatu', 'VU', '548', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Vanuatu\", \"cn\": \"瓦努阿图\", \"de\": \"Vanuatu\", \"es\": \"Vanuatu\", \"fa\": \"وانواتو\", \"fr\": \"Vanuatu\", \"hr\": \"Vanuatu\", \"it\": \"Vanuatu\", \"ja\": \"バヌアツ\", \"kr\": \"바누아투\", \"nl\": \"Vanuatu\", \"pt\": \"Vanuatu\", \"tr\": \"Vanuatu\", \"pt-BR\": \"Vanuatu\"}', '[{\"tzName\": \"Vanuatu Time\", \"zoneName\": \"Pacific/Efate\", \"gmtOffset\": 39600, \"abbreviation\": \"VUT\", \"gmtOffsetName\": \"UTC+11:00\"}]', '548', 'VUT', 'Ni-Vanuatu, Vanuatuan', 'Port Vila', '.vu', 'Vanuatu', 'Oceania', 'VUV', 'Vanuatu vatu', 'VT', NULL, -16.00, 167.00, '🇻🇺', 'U+1F1FB U+1F1FA', 1, 1, 0),
(240, 'Venezuela', 'VE', '862', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Venezuela\", \"cn\": \"委内瑞拉\", \"de\": \"Venezuela\", \"es\": \"Venezuela\", \"fa\": \"ونزوئلا\", \"fr\": \"Venezuela\", \"hr\": \"Venezuela\", \"it\": \"Venezuela\", \"ja\": \"ベネズエラ・ボリバル共和国\", \"kr\": \"베네수엘라\", \"nl\": \"Venezuela\", \"pt\": \"Venezuela\", \"tr\": \"Venezuela\", \"pt-BR\": \"Venezuela\"}', '[{\"tzName\": \"Venezuelan Standard Time\", \"zoneName\": \"America/Caracas\", \"gmtOffset\": -14400, \"abbreviation\": \"VET\", \"gmtOffsetName\": \"UTC-04:00\"}]', '862', 'VEN', 'Venezuelan', 'Caracas', '.ve', 'Venezuela', 'Americas', 'VES', 'Bolívar', 'Bs', NULL, 8.00, -66.00, '🇻🇪', 'U+1F1FB U+1F1EA', 1, 1, 0),
(241, 'Vietnam', 'VN', '704', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Vietnam\", \"cn\": \"越南\", \"de\": \"Vietnam\", \"es\": \"Vietnam\", \"fa\": \"ویتنام\", \"fr\": \"Viêt Nam\", \"hr\": \"Vijetnam\", \"it\": \"Vietnam\", \"ja\": \"ベトナム\", \"kr\": \"베트남\", \"nl\": \"Vietnam\", \"pt\": \"Vietname\", \"tr\": \"Vietnam\", \"pt-BR\": \"Vietnã\"}', '[{\"tzName\": \"Indochina Time\", \"zoneName\": \"Asia/Ho_Chi_Minh\", \"gmtOffset\": 25200, \"abbreviation\": \"ICT\", \"gmtOffsetName\": \"UTC+07:00\"}]', '704', 'VNM', 'Vietnamese', 'Hanoi', '.vn', 'Việt Nam', 'Asia', 'VND', 'Vietnamese đồng', '₫', NULL, 16.17, 107.83, '🇻🇳', 'U+1F1FB U+1F1F3', 1, 1, 0),
(242, 'Virgin Islands (British)', 'VG', '092', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Virgin Islands (British)\", \"cn\": \"圣文森特和格林纳丁斯\", \"de\": \"Britische Jungferninseln\", \"es\": \"Islas Vírgenes del Reino Unido\", \"fa\": \"جزایر ویرجین بریتانیا\", \"fr\": \"Îles Vierges britanniques\", \"hr\": \"Britanski Djevičanski Otoci\", \"it\": \"Isole Vergini Britanniche\", \"ja\": \"イギリス領ヴァージン諸島\", \"kr\": \"영국령 버진아일랜드\", \"nl\": \"Britse Maagdeneilanden\", \"pt\": \"Ilhas Virgens Britânicas\", \"tr\": \"Britanya Virjin Adalari\", \"pt-BR\": \"Ilhas Virgens Britânicas\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Tortola\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '092', 'VGB', 'British Virgin Island', 'Road Town', '.vg', 'British Virgin Islands', 'Americas', 'USD', 'United States dollar', '$', NULL, 18.43, -64.62, '🇻🇬', 'U+1F1FB U+1F1EC', 1, 1, 0),
(243, 'Virgin Islands (US)', 'VI', '850', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Virgin Islands (US)\", \"cn\": \"维尔京群岛（美国）\", \"de\": \"Amerikanische Jungferninseln\", \"es\": \"Islas Vírgenes de los Estados Unidos\", \"fa\": \"جزایر ویرجین آمریکا\", \"fr\": \"Îles Vierges des États-Unis\", \"it\": \"Isole Vergini americane\", \"ja\": \"アメリカ領ヴァージン諸島\", \"kr\": \"미국령 버진아일랜드\", \"nl\": \"Verenigde Staten Maagdeneilanden\", \"pt\": \"Ilhas Virgens Americanas\", \"tr\": \"Abd Virjin Adalari\", \"pt-BR\": \"Ilhas Virgens Americanas\"}', '[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/St_Thomas\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]', '850', 'VIR', 'U.S. Virgin Island', 'Charlotte Amalie', '.vi', 'United States Virgin Islands', 'Americas', 'USD', 'United States dollar', '$', NULL, 18.34, -64.93, '🇻🇮', 'U+1F1FB U+1F1EE', 1, 1, 0),
(244, 'Wallis And Futuna Islands', 'WF', '876', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Wallis And Futuna Islands\", \"cn\": \"瓦利斯群岛和富图纳群岛\", \"de\": \"Wallis und Futuna\", \"es\": \"Wallis y Futuna\", \"fa\": \"والیس و فوتونا\", \"fr\": \"Wallis-et-Futuna\", \"hr\": \"Wallis i Fortuna\", \"it\": \"Wallis e Futuna\", \"ja\": \"ウォリス・フツナ\", \"kr\": \"왈리스 푸투나\", \"nl\": \"Wallis en Futuna\", \"pt\": \"Wallis e Futuna\", \"tr\": \"Wallis Ve Futuna\", \"pt-BR\": \"Wallis e Futuna\"}', '[{\"tzName\": \"Wallis & Futuna Time\", \"zoneName\": \"Pacific/Wallis\", \"gmtOffset\": 43200, \"abbreviation\": \"WFT\", \"gmtOffsetName\": \"UTC+12:00\"}]', '876', 'WLF', 'Wallis and Futuna, Wallisian or Futunan', 'Mata Utu', '.wf', 'Wallis et Futuna', 'Oceania', 'XPF', 'CFP franc', '₣', NULL, -13.30, -176.20, '🇼🇫', 'U+1F1FC U+1F1EB', 1, 1, 0),
(246, 'Yemen', 'YE', '887', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Yemen\", \"cn\": \"也门\", \"de\": \"Jemen\", \"es\": \"Yemen\", \"fa\": \"یمن\", \"fr\": \"Yémen\", \"hr\": \"Jemen\", \"it\": \"Yemen\", \"ja\": \"イエメン\", \"kr\": \"예멘\", \"nl\": \"Jemen\", \"pt\": \"Iémen\", \"tr\": \"Yemen\", \"pt-BR\": \"Iêmen\"}', '[{\"tzName\": \"Arabia Standard Time\", \"zoneName\": \"Asia/Aden\", \"gmtOffset\": 10800, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC+03:00\"}]', '887', 'YEM', 'Yemeni', 'Sanaa', '.ye', 'اليَمَن', 'Asia', 'YER', 'Yemeni rial', '﷼', NULL, 15.00, 48.00, '🇾🇪', 'U+1F1FE U+1F1EA', 1, 1, 0),
(247, 'Zambia', 'ZM', '894', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Zambia\", \"cn\": \"赞比亚\", \"de\": \"Sambia\", \"es\": \"Zambia\", \"fa\": \"زامبیا\", \"fr\": \"Zambie\", \"hr\": \"Zambija\", \"it\": \"Zambia\", \"ja\": \"ザンビア\", \"kr\": \"잠비아\", \"nl\": \"Zambia\", \"pt\": \"Zâmbia\", \"tr\": \"Zambiya\", \"pt-BR\": \"Zâmbia\"}', '[{\"tzName\": \"Central Africa Time\", \"zoneName\": \"Africa/Lusaka\", \"gmtOffset\": 7200, \"abbreviation\": \"CAT\", \"gmtOffsetName\": \"UTC+02:00\"}]', '894', 'ZMB', 'Zambian', 'Lusaka', '.zm', 'Zambia', 'Africa', 'ZMW', 'Zambian kwacha', 'ZK', NULL, -15.00, 30.00, '🇿🇲', 'U+1F1FF U+1F1F2', 1, 1, 0),
(248, 'Zimbabwe', 'ZW', '716', '2020-06-17 10:37:05', '2024-01-21 12:54:30', '{\"ar\": \"Zimbabwe\", \"cn\": \"津巴布韦\", \"de\": \"Simbabwe\", \"es\": \"Zimbabue\", \"fa\": \"زیمباوه\", \"fr\": \"Zimbabwe\", \"hr\": \"Zimbabve\", \"it\": \"Zimbabwe\", \"ja\": \"ジンバブエ\", \"kr\": \"짐바브웨\", \"nl\": \"Zimbabwe\", \"pt\": \"Zimbabué\", \"tr\": \"Zimbabve\", \"pt-BR\": \"Zimbabwe\"}', '[{\"tzName\": \"Central Africa Time\", \"zoneName\": \"Africa/Harare\", \"gmtOffset\": 7200, \"abbreviation\": \"CAT\", \"gmtOffsetName\": \"UTC+02:00\"}]', '716', 'ZWE', 'Zimbabwean', 'Harare', '.zw', 'Zimbabwe', 'Africa', 'ZWL', 'Zimbabwe Dollar', '$', NULL, -20.00, 30.00, '🇿🇼', 'U+1F1FF U+1F1FC', 1, 1, 0),
(249, 'Afghanistan', 'AF', '004', '2024-01-21 11:39:22', '2024-01-21 12:54:28', '{\"ar\": \"Afghanistan\", \"cn\": \"阿富汗\", \"de\": \"Afghanistan\", \"es\": \"Afganistán\", \"fa\": \"افغانستان\", \"fr\": \"Afghanistan\", \"hr\": \"Afganistan\", \"it\": \"Afghanistan\", \"ja\": \"アフガニスタン\", \"kr\": \"아프가니스탄\", \"nl\": \"Afghanistan\", \"pt\": \"Afeganistão\", \"tr\": \"Afganistan\", \"pt-BR\": \"Afeganistão\"}', '[{\"tzName\": \"Afghanistan Time\", \"zoneName\": \"Asia/Kabul\", \"gmtOffset\": 16200, \"abbreviation\": \"AFT\", \"gmtOffsetName\": \"UTC+04:30\"}]', '004', 'AFG', 'Afghan', 'Kabul', '.af', 'افغانستان', 'Asia', 'AFN', 'Afghan afghani', '؋', NULL, 33.00, 65.00, '🇦🇫', 'U+1F1E6 U+1F1EB', 1, 1, 0),
(250, 'Kosovo', 'XK', '926', '2024-01-21 11:39:24', '2024-01-21 12:54:30', '{\"ar\": \"Kosovo\", \"cn\": \"科索沃\", \"kr\": \"코소보\", \"tr\": \"Kosova\"}', '[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Belgrade\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]', '926', 'XKX', 'Kosovar, Kosovan', 'Pristina', '.xk', 'Republika e Kosovës', 'Europe', 'EUR', 'Euro', '€', NULL, 42.56, 20.34, '🇽🇰', 'U+1F1FD U+1F1F0', 1, 1, 0);
