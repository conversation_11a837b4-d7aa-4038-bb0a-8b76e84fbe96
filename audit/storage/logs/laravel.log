[2025-02-19 14:46:54] local.ERROR: Target class [config] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [config] does not exist. at /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Container/Container.php:961)
[stacktrace]
#0 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Container/Container.php(832): Illuminate\\Container\\Container->build('config')
#1 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1078): Illuminate\\Container\\Container->resolve('config', Array, true)
#2 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Container/Container.php(763): Illuminate\\Foundation\\Application->resolve('config', Array)
#3 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1058): Illuminate\\Container\\Container->make('config', Array)
#4 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(124): Illuminate\\Foundation\\Application->make('config', Array)
#5 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(279): app('config')
#6 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/sanctum/src/SanctumServiceProvider.php(27): config('auth.guards.san...', Array)
#7 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(896): Laravel\\Sanctum\\SanctumServiceProvider->register()
#8 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Laravel\\Sanctum\\SanctumServiceProvider))
#9 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(871): Illuminate\\Foundation\\ProviderRepository->load(Array)
#10 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#11 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#14 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 /Applications/MAMP/htdocs/ismena_sonarqube/artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#16 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"config\" does not exist at /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Container/Container.php:959)
[stacktrace]
#0 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): ReflectionClass->__construct('config')
#1 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Container/Container.php(832): Illuminate\\Container\\Container->build('config')
#2 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1078): Illuminate\\Container\\Container->resolve('config', Array, true)
#3 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Container/Container.php(763): Illuminate\\Foundation\\Application->resolve('config', Array)
#4 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1058): Illuminate\\Container\\Container->make('config', Array)
#5 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(124): Illuminate\\Foundation\\Application->make('config', Array)
#6 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(279): app('config')
#7 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/sanctum/src/SanctumServiceProvider.php(27): config('auth.guards.san...', Array)
#8 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(896): Laravel\\Sanctum\\SanctumServiceProvider->register()
#9 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Laravel\\Sanctum\\SanctumServiceProvider))
#10 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(871): Illuminate\\Foundation\\ProviderRepository->load(Array)
#11 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#12 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#13 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#14 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#15 /Applications/MAMP/htdocs/ismena_sonarqube/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 /Applications/MAMP/htdocs/ismena_sonarqube/artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#17 {main}
"} 
