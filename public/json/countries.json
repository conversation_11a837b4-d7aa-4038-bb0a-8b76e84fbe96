[{"id": "1", "name": "Aland Islands", "code": "AX", "phone": "248", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Aland Islands\", \"cn\": \"奥兰群岛\", \"de\": \"Åland\", \"es\": \"Alandia\", \"fa\": \"جزایر الند\", \"fr\": \"Åland\", \"hr\": \"Ålandski otoci\", \"it\": \"Isole Aland\", \"ja\": \"オーランド諸島\", \"kr\": \"올란드 제도\", \"nl\": \"Ålandeilanden\", \"pt\": \"<PERSON><PERSON> de Aland\", \"tr\": \"Åland Adalari\", \"pt-BR\": \"<PERSON>has de Aland\"}", "timezones": "[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Mariehamn\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "248", "iso3": "ALA", "nationality": "Aland Island", "capital": "<PERSON><PERSON><PERSON>", "tld": ".ax", "native": "Åland", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "60.12", "lng": "19.90", "emoji": "🇦🇽", "emojiU": "U+1F1E6 U+1F1FD", "flag": "1", "is_activated": "1"}, {"id": "2", "name": "Albania", "code": "AL", "phone": "008", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Albania\", \"cn\": \"阿尔巴尼亚\", \"de\": \"Albanien\", \"es\": \"Albania\", \"fa\": \"آلبانی\", \"fr\": \"Albanie\", \"hr\": \"Albanija\", \"it\": \"Albania\", \"ja\": \"アルバニア\", \"kr\": \"알바니아\", \"nl\": \"Albanië\", \"pt\": \"Albânia\", \"tr\": \"Arnavutluk\", \"pt-BR\": \"Albânia\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Tirane\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "008", "iso3": "ALB", "nationality": "Albanian ", "capital": "Tirana", "tld": ".al", "native": "Shqipëria", "region": "Europe", "currency": "ALL", "currency_name": "Albanian lek", "currency_symbol": "Lek", "wikiDataId": null, "lat": "41.00", "lng": "20.00", "emoji": "🇦🇱", "emojiU": "U+1F1E6 U+1F1F1", "flag": "1", "is_activated": "1"}, {"id": "3", "name": "Algeria", "code": "DZ", "phone": "012", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Algeria\", \"cn\": \"阿尔及利亚\", \"de\": \"Algerien\", \"es\": \"Argelia\", \"fa\": \"الجزایر\", \"fr\": \"Algérie\", \"hr\": \"<PERSON>žir\", \"it\": \"Algeria\", \"ja\": \"アルジェリア\", \"kr\": \"알제리\", \"nl\": \"Algerije\", \"pt\": \"Arg<PERSON>lia\", \"tr\": \"Cezayir\", \"pt-BR\": \"Arg<PERSON>lia\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Africa/Algiers\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "012", "iso3": "DZA", "nationality": "Algerian", "capital": "Algiers", "tld": ".dz", "native": "الجزائر", "region": "Africa", "currency": "DZD", "currency_name": "Algerian dinar", "currency_symbol": "دج", "wikiDataId": null, "lat": "28.00", "lng": "3.00", "emoji": "🇩🇿", "emojiU": "U+1F1E9 U+1F1FF", "flag": "1", "is_activated": "1"}, {"id": "4", "name": "American Samoa", "code": "AS", "phone": "016", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"American Samoa\", \"cn\": \"美属萨摩亚\", \"de\": \"Amerikanisch-Samoa\", \"es\": \"Samoa Americana\", \"fa\": \"ساموآی آمریکا\", \"fr\": \"Samoa américaines\", \"hr\": \"Američka Samoa\", \"it\": \"Samoa Americane\", \"ja\": \"アメリカ領サモア\", \"kr\": \"아메리칸사모아\", \"nl\": \"Amerikaans Samoa\", \"pt\": \"Samoa Americana\", \"tr\": \"Amerikan Samoasi\", \"pt-BR\": \"Samoa Americana\"}", "timezones": "[{\"tzName\": \"Samoa Standard Time\", \"zoneName\": \"Pacific/Pago_Pago\", \"gmtOffset\": -39600, \"abbreviation\": \"SST\", \"gmtOffsetName\": \"UTC-11:00\"}]", "numeric_code": "016", "iso3": "ASM", "nationality": "American Samoan", "capital": "Pago Pago", "tld": ".as", "native": "American Samoa", "region": "Oceania", "currency": "USD", "currency_name": "US Dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "-14.33", "lng": "-170.00", "emoji": "🇦🇸", "emojiU": "U+1F1E6 U+1F1F8", "flag": "1", "is_activated": "1"}, {"id": "5", "name": "Andorra", "code": "AD", "phone": "020", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Andorra\", \"cn\": \"安道尔\", \"de\": \"Andorra\", \"es\": \"Andorra\", \"fa\": \"آندورا\", \"fr\": \"<PERSON><PERSON><PERSON>\", \"hr\": \"<PERSON>ora\", \"it\": \"Andorra\", \"ja\": \"アンドラ\", \"kr\": \"안도라\", \"nl\": \"Andorra\", \"pt\": \"Andorra\", \"tr\": \"Andorra\", \"pt-BR\": \"Andorra\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Andorra\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "020", "iso3": "AND", "nationality": "Andorran", "capital": "Andorra la Vella", "tld": ".ad", "native": "Andorra", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "42.50", "lng": "1.50", "emoji": "🇦🇩", "emojiU": "U+1F1E6 U+1F1E9", "flag": "1", "is_activated": "1"}, {"id": "6", "name": "Angola", "code": "AO", "phone": "024", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Angola\", \"cn\": \"安哥拉\", \"de\": \"Angola\", \"es\": \"Angola\", \"fa\": \"آنگولا\", \"fr\": \"Angola\", \"hr\": \"Angola\", \"it\": \"Angola\", \"ja\": \"アンゴラ\", \"kr\": \"앙골라\", \"nl\": \"Angola\", \"pt\": \"Angola\", \"tr\": \"Angola\", \"pt-BR\": \"Angola\"}", "timezones": "[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Luanda\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "024", "iso3": "AGO", "nationality": "Angolan", "capital": "<PERSON><PERSON>", "tld": ".ao", "native": "Angola", "region": "Africa", "currency": "AOA", "currency_name": "Angolan kwanza", "currency_symbol": "Kz", "wikiDataId": null, "lat": "-12.50", "lng": "18.50", "emoji": "🇦🇴", "emojiU": "U+1F1E6 U+1F1F4", "flag": "1", "is_activated": "1"}, {"id": "7", "name": "<PERSON><PERSON><PERSON>", "code": "AI", "phone": "660", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"<PERSON><PERSON><PERSON>\", \"cn\": \"安圭拉\", \"de\": \"<PERSON><PERSON><PERSON>\", \"es\": \"<PERSON><PERSON><PERSON>\", \"fa\": \"آنگویلا\", \"fr\": \"<PERSON><PERSON><PERSON>\", \"hr\": \"<PERSON><PERSON>\", \"it\": \"<PERSON><PERSON><PERSON>\", \"ja\": \"アンギラ\", \"kr\": \"앵귈라\", \"nl\": \"<PERSON>uil<PERSON>\", \"pt\": \"Anguila\", \"tr\": \"Anguil<PERSON>\", \"pt-BR\": \"<PERSON>uila\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Anguilla\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "660", "iso3": "AIA", "nationality": "<PERSON><PERSON><PERSON>", "capital": "The Valley", "tld": ".ai", "native": "<PERSON><PERSON><PERSON>", "region": "Americas", "currency": "XCD", "currency_name": "East Caribbean dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "18.25", "lng": "-63.17", "emoji": "🇦🇮", "emojiU": "U+1F1E6 U+1F1EE", "flag": "1", "is_activated": "1"}, {"id": "8", "name": "Antarctica", "code": "AQ", "phone": "010", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Antarctica\", \"cn\": \"南极洲\", \"de\": \"Antarktika\", \"es\": \"Ant<PERSON>rt<PERSON>\", \"fa\": \"جنوبگان\", \"fr\": \"Antarctique\", \"hr\": \"Antarktika\", \"it\": \"Antartide\", \"ja\": \"南極大陸\", \"kr\": \"남극\", \"nl\": \"Antarctica\", \"pt\": \"Antárctida\", \"tr\": \"Antartika\", \"pt-BR\": \"Antártida\"}", "timezones": "[{\"tzName\": \"Australian Western Standard Time\", \"zoneName\": \"Antarctica/Casey\", \"gmtOffset\": 39600, \"abbreviation\": \"AWST\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Davis Time\", \"zoneName\": \"Antarctica/Davis\", \"gmtOffset\": 25200, \"abbreviation\": \"DAVT\", \"gmtOffsetName\": \"UTC+07:00\"}, {\"tzName\": \"Dumont dUrville Time\", \"zoneName\": \"Antarctica/DumontDUrville\", \"gmtOffset\": 36000, \"abbreviation\": \"DDUT\", \"gmtOffsetName\": \"UTC+10:00\"}, {\"tzName\": \"Mawson Station Time\", \"zoneName\": \"Antarctica/Mawson\", \"gmtOffset\": 18000, \"abbreviation\": \"MAWT\", \"gmtOffsetName\": \"UTC+05:00\"}, {\"tzName\": \"New Zealand Daylight Time\", \"zoneName\": \"Antarctica/McMurdo\", \"gmtOffset\": 46800, \"abbreviation\": \"NZDT\", \"gmtOffsetName\": \"UTC+13:00\"}, {\"tzName\": \"Chile Summer Time\", \"zoneName\": \"Antarctica/Palmer\", \"gmtOffset\": -10800, \"abbreviation\": \"CLST\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Rothera Research Station Time\", \"zoneName\": \"Antarctica/Rothera\", \"gmtOffset\": -10800, \"abbreviation\": \"ROTT\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Showa Station Time\", \"zoneName\": \"Antarctica/Syowa\", \"gmtOffset\": 10800, \"abbreviation\": \"SYOT\", \"gmtOffsetName\": \"UTC+03:00\"}, {\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Antarctica/Troll\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}, {\"tzName\": \"Vostok Station Time\", \"zoneName\": \"Antarctica/Vostok\", \"gmtOffset\": 21600, \"abbreviation\": \"VOST\", \"gmtOffsetName\": \"UTC+06:00\"}]", "numeric_code": "010", "iso3": "ATA", "nationality": "Antarctic", "capital": "", "tld": ".aq", "native": "Antarctica", "region": "Polar", "currency": "AAD", "currency_name": "Antarctican dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "-74.65", "lng": "4.48", "emoji": "🇦🇶", "emojiU": "U+1F1E6 U+1F1F6", "flag": "1", "is_activated": "1"}, {"id": "9", "name": "Antigua And Barbuda", "code": "AG", "phone": "028", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Antigua And Barbuda\", \"cn\": \"安提瓜和巴布达\", \"de\": \"Antigua und Barbuda\", \"es\": \"Antigua y Barbuda\", \"fa\": \"آنتیگوا و باربودا\", \"fr\": \"Antigua-et-Barbuda\", \"hr\": \"Antigva i Barbuda\", \"it\": \"Antigua e Barbuda\", \"ja\": \"アンティグア・バーブーダ\", \"kr\": \"앤티가 바부다\", \"nl\": \"Antigua en Barbuda\", \"pt\": \"Antígua e Barbuda\", \"tr\": \"Antigua Ve Barbuda\", \"pt-BR\": \"Antígua e Barbuda\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Antigua\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "028", "iso3": "ATG", "nationality": "Antiguan or Barbudan", "capital": "St. Johns", "tld": ".ag", "native": "Antigua and Barbuda", "region": "Americas", "currency": "XCD", "currency_name": "Eastern Caribbean dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "17.05", "lng": "-61.80", "emoji": "🇦🇬", "emojiU": "U+1F1E6 U+1F1EC", "flag": "1", "is_activated": "1"}, {"id": "10", "name": "Argentina", "code": "AR", "phone": "032", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Argentina\", \"cn\": \"阿根廷\", \"de\": \"Argentinien\", \"es\": \"Argentina\", \"fa\": \"آرژانتین\", \"fr\": \"Argentine\", \"hr\": \"Argentina\", \"it\": \"Argentina\", \"ja\": \"アルゼンチン\", \"kr\": \"아르헨티나\", \"nl\": \"Argentinië\", \"pt\": \"Argentina\", \"tr\": \"<PERSON><PERSON><PERSON><PERSON>\", \"pt-BR\": \"Argentina\"}", "timezones": "[{\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/Buenos_Aires\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/Catamarca\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/Cordoba\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/Jujuy\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/La_Rioja\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/Mendoza\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/Rio_Gallegos\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/Salta\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/San_Juan\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/San_Luis\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/Tucuman\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Argentina Time\", \"zoneName\": \"America/Argentina/Ushuaia\", \"gmtOffset\": -10800, \"abbreviation\": \"ART\", \"gmtOffsetName\": \"UTC-03:00\"}]", "numeric_code": "032", "iso3": "ARG", "nationality": "Argentine", "capital": "Buenos Aires", "tld": ".ar", "native": "Argentina", "region": "Americas", "currency": "ARS", "currency_name": "Argentine peso", "currency_symbol": "$", "wikiDataId": null, "lat": "-34.00", "lng": "-64.00", "emoji": "🇦🇷", "emojiU": "U+1F1E6 U+1F1F7", "flag": "1", "is_activated": "1"}, {"id": "11", "name": "Armenia", "code": "AM", "phone": "051", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Armenia\", \"cn\": \"亚美尼亚\", \"de\": \"Armenien\", \"es\": \"Armenia\", \"fa\": \"ارمنستان\", \"fr\": \"Arménie\", \"hr\": \"Armenija\", \"it\": \"Armenia\", \"ja\": \"アルメニア\", \"kr\": \"아르메니아\", \"nl\": \"Armenië\", \"pt\": \"Arménia\", \"tr\": \"Ermenistan\", \"pt-BR\": \"Armênia\"}", "timezones": "[{\"tzName\": \"Armenia Time\", \"zoneName\": \"Asia/Yerevan\", \"gmtOffset\": 14400, \"abbreviation\": \"AMT\", \"gmtOffsetName\": \"UTC+04:00\"}]", "numeric_code": "051", "iso3": "ARM", "nationality": "Armenian", "capital": "Yerevan", "tld": ".am", "native": "Հայաստան", "region": "Asia", "currency": "AMD", "currency_name": "Armenian dram", "currency_symbol": "֏", "wikiDataId": null, "lat": "40.00", "lng": "45.00", "emoji": "🇦🇲", "emojiU": "U+1F1E6 U+1F1F2", "flag": "1", "is_activated": "1"}, {"id": "12", "name": "Aruba", "code": "AW", "phone": "533", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Aruba\", \"cn\": \"阿鲁巴\", \"de\": \"Aruba\", \"es\": \"Aruba\", \"fa\": \"آروبا\", \"fr\": \"Aruba\", \"hr\": \"Aruba\", \"it\": \"Aruba\", \"ja\": \"アルバ\", \"kr\": \"아루바\", \"nl\": \"Aruba\", \"pt\": \"Aruba\", \"tr\": \"Aruba\", \"pt-BR\": \"Aruba\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Aruba\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "533", "iso3": "ABW", "nationality": "Aruban", "capital": "Oranjestad", "tld": ".aw", "native": "Aruba", "region": "Americas", "currency": "AWG", "currency_name": "Aruban florin", "currency_symbol": "ƒ", "wikiDataId": null, "lat": "12.50", "lng": "-69.97", "emoji": "🇦🇼", "emojiU": "U+1F1E6 U+1F1FC", "flag": "1", "is_activated": "1"}, {"id": "13", "name": "Australia", "code": "AU", "phone": "036", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Australia\", \"cn\": \"澳大利亚\", \"de\": \"Australien\", \"es\": \"Australia\", \"fa\": \"استرالیا\", \"fr\": \"Australie\", \"hr\": \"Australija\", \"it\": \"Australia\", \"ja\": \"オーストラリア\", \"kr\": \"호주\", \"nl\": \"Australië\", \"pt\": \"Austrália\", \"tr\": \"Avustralya\", \"pt-BR\": \"Austrália\"}", "timezones": "[{\"tzName\": \"Macquarie Island Station Time\", \"zoneName\": \"Antarctica/Macquarie\", \"gmtOffset\": 39600, \"abbreviation\": \"MIST\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Australian Central Daylight Saving Time\", \"zoneName\": \"Australia/Adelaide\", \"gmtOffset\": 37800, \"abbreviation\": \"ACDT\", \"gmtOffsetName\": \"UTC+10:30\"}, {\"tzName\": \"Australian Eastern Standard Time\", \"zoneName\": \"Australia/Brisbane\", \"gmtOffset\": 36000, \"abbreviation\": \"AEST\", \"gmtOffsetName\": \"UTC+10:00\"}, {\"tzName\": \"Australian Central Daylight Saving Time\", \"zoneName\": \"Australia/Broken_Hill\", \"gmtOffset\": 37800, \"abbreviation\": \"ACDT\", \"gmtOffsetName\": \"UTC+10:30\"}, {\"tzName\": \"Australian Eastern Daylight Saving Time\", \"zoneName\": \"Australia/Currie\", \"gmtOffset\": 39600, \"abbreviation\": \"AEDT\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Australian Central Standard Time\", \"zoneName\": \"Australia/Darwin\", \"gmtOffset\": 34200, \"abbreviation\": \"ACST\", \"gmtOffsetName\": \"UTC+09:30\"}, {\"tzName\": \"Australian Central Western Standard Time (Unofficial)\", \"zoneName\": \"Australia/Eucla\", \"gmtOffset\": 31500, \"abbreviation\": \"ACWST\", \"gmtOffsetName\": \"UTC+08:45\"}, {\"tzName\": \"Australian Eastern Daylight Saving Time\", \"zoneName\": \"Australia/Hobart\", \"gmtOffset\": 39600, \"abbreviation\": \"AEDT\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Australian Eastern Standard Time\", \"zoneName\": \"Australia/Lindeman\", \"gmtOffset\": 36000, \"abbreviation\": \"AEST\", \"gmtOffsetName\": \"UTC+10:00\"}, {\"tzName\": \"Lord Howe Summer Time\", \"zoneName\": \"Australia/Lord_Howe\", \"gmtOffset\": 39600, \"abbreviation\": \"LHST\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Australian Eastern Daylight Saving Time\", \"zoneName\": \"Australia/Melbourne\", \"gmtOffset\": 39600, \"abbreviation\": \"AEDT\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Australian Western Standard Time\", \"zoneName\": \"Australia/Perth\", \"gmtOffset\": 28800, \"abbreviation\": \"AWST\", \"gmtOffsetName\": \"UTC+08:00\"}, {\"tzName\": \"Australian Eastern Daylight Saving Time\", \"zoneName\": \"Australia/Sydney\", \"gmtOffset\": 39600, \"abbreviation\": \"AEDT\", \"gmtOffsetName\": \"UTC+11:00\"}]", "numeric_code": "036", "iso3": "AUS", "nationality": "Australian", "capital": "Canberra", "tld": ".au", "native": "Australia", "region": "Oceania", "currency": "AUD", "currency_name": "Australian dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "-27.00", "lng": "133.00", "emoji": "🇦🇺", "emojiU": "U+1F1E6 U+1F1FA", "flag": "1", "is_activated": "1"}, {"id": "14", "name": "Austria", "code": "AT", "phone": "040", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Austria\", \"cn\": \"奥地利\", \"de\": \"Österreich\", \"es\": \"Austria\", \"fa\": \"اتریش\", \"fr\": \"<PERSON>tric<PERSON>\", \"hr\": \"Aust<PERSON>ja\", \"it\": \"Austria\", \"ja\": \"オーストリア\", \"kr\": \"오스트리아\", \"nl\": \"Oostenrijk\", \"pt\": \"áustria\", \"tr\": \"Avusturya\", \"pt-BR\": \"áustria\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Vienna\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "040", "iso3": "AUT", "nationality": "Austrian", "capital": "Vienna", "tld": ".at", "native": "Österreich", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "47.33", "lng": "13.33", "emoji": "🇦🇹", "emojiU": "U+1F1E6 U+1F1F9", "flag": "1", "is_activated": "1"}, {"id": "15", "name": "Azerbaijan", "code": "AZ", "phone": "031", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Azerbaijan\", \"cn\": \"阿塞拜疆\", \"de\": \"Aserbaidschan\", \"es\": \"Azerbaiyán\", \"fa\": \"آذربایجان\", \"fr\": \"Azerbaïdjan\", \"hr\": \"Azerbajdžan\", \"it\": \"Azerbaijan\", \"ja\": \"アゼルバイジャン\", \"kr\": \"아제르바이잔\", \"nl\": \"Azerbeidzjan\", \"pt\": \"Azerbaijão\", \"tr\": \"Azerbaycan\", \"pt-BR\": \"Azerbaijão\"}", "timezones": "[{\"tzName\": \"Azerbaijan Time\", \"zoneName\": \"Asia/Baku\", \"gmtOffset\": 14400, \"abbreviation\": \"AZT\", \"gmtOffsetName\": \"UTC+04:00\"}]", "numeric_code": "031", "iso3": "AZE", "nationality": "Azerbaijani, Azeri", "capital": "Baku", "tld": ".az", "native": "Azərbaycan", "region": "Asia", "currency": "AZN", "currency_name": "Azerbaijani manat", "currency_symbol": "m", "wikiDataId": null, "lat": "40.50", "lng": "47.50", "emoji": "🇦🇿", "emojiU": "U+1F1E6 U+1F1FF", "flag": "1", "is_activated": "1"}, {"id": "16", "name": "The Bahamas", "code": "BS", "phone": "044", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"The Bahamas\", \"cn\": \"巴哈马\", \"de\": \"Bahamas\", \"es\": \"Bahamas\", \"fa\": \"باهاما\", \"fr\": \"Bahamas\", \"hr\": \"Bahami\", \"it\": \"Bahamas\", \"ja\": \"バハマ\", \"kr\": \"바하마\", \"nl\": \"<PERSON>ham<PERSON>’s\", \"pt\": \"Baamas\", \"tr\": \"Bahamalar\", \"pt-BR\": \"Bahamas\"}", "timezones": "[{\"tzName\": \"Eastern Standard Time (North America)\", \"zoneName\": \"America/Nassau\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}]", "numeric_code": "044", "iso3": "BHS", "nationality": "<PERSON><PERSON><PERSON>", "capital": "Nassau", "tld": ".bs", "native": "Bahamas", "region": "Americas", "currency": "BSD", "currency_name": "Bahamian dollar", "currency_symbol": "B$", "wikiDataId": null, "lat": "24.25", "lng": "-76.00", "emoji": "🇧🇸", "emojiU": "U+1F1E7 U+1F1F8", "flag": "1", "is_activated": "1"}, {"id": "17", "name": "Bahrain", "code": "BH", "phone": "048", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Bahrain\", \"cn\": \"巴林\", \"de\": \"Bahrain\", \"es\": \"<PERSON>hr<PERSON>\", \"fa\": \"بحرین\", \"fr\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"hr\": \"<PERSON><PERSON><PERSON>\", \"it\": \"<PERSON><PERSON><PERSON>\", \"ja\": \"バーレーン\", \"kr\": \"바레인\", \"nl\": \"Bahrein\", \"pt\": \"Barém\", \"tr\": \"<PERSON>hr<PERSON><PERSON>\", \"pt-BR\": \"Bahrein\"}", "timezones": "[{\"tzName\": \"Arabia Standard Time\", \"zoneName\": \"Asia/Bahrain\", \"gmtOffset\": 10800, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC+03:00\"}]", "numeric_code": "048", "iso3": "BHR", "nationality": "Bahraini", "capital": "Manama", "tld": ".bh", "native": "‏البحرين", "region": "Asia", "currency": "BHD", "currency_name": "<PERSON><PERSON> dinar", "currency_symbol": ".د.ب", "wikiDataId": null, "lat": "26.00", "lng": "50.55", "emoji": "🇧🇭", "emojiU": "U+1F1E7 U+1F1ED", "flag": "1", "is_activated": "1"}, {"id": "18", "name": "Bangladesh", "code": "BD", "phone": "050", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Bangladesh\", \"cn\": \"孟加拉\", \"de\": \"Bangladesch\", \"es\": \"Bangladesh\", \"fa\": \"بنگلادش\", \"fr\": \"Bangladesh\", \"hr\": \"<PERSON><PERSON><PERSON>\", \"it\": \"Bangladesh\", \"ja\": \"バングラデシュ\", \"kr\": \"방글라데시\", \"nl\": \"Bangladesh\", \"pt\": \"<PERSON>ladeche\", \"tr\": \"<PERSON><PERSON>ş\", \"pt-BR\": \"Bangladesh\"}", "timezones": "[{\"tzName\": \"Bangladesh Standard Time\", \"zoneName\": \"Asia/Dhaka\", \"gmtOffset\": 21600, \"abbreviation\": \"BDT\", \"gmtOffsetName\": \"UTC+06:00\"}]", "numeric_code": "050", "iso3": "BGD", "nationality": "Bangladeshi", "capital": "Dhaka", "tld": ".bd", "native": "Bangladesh", "region": "Asia", "currency": "BDT", "currency_name": "Bangladeshi taka", "currency_symbol": "৳", "wikiDataId": null, "lat": "24.00", "lng": "90.00", "emoji": "🇧🇩", "emojiU": "U+1F1E7 U+1F1E9", "flag": "1", "is_activated": "1"}, {"id": "19", "name": "Barbados", "code": "BB", "phone": "052", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Barbados\", \"cn\": \"巴巴多斯\", \"de\": \"Barbados\", \"es\": \"Barbados\", \"fa\": \"باربادوس\", \"fr\": \"Barbade\", \"hr\": \"Barbados\", \"it\": \"Barbados\", \"ja\": \"バルバドス\", \"kr\": \"바베이도스\", \"nl\": \"Barbados\", \"pt\": \"Barbados\", \"tr\": \"Barbados\", \"pt-BR\": \"Barbados\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Barbados\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "052", "iso3": "BRB", "nationality": "Barbadian", "capital": "Bridgetown", "tld": ".bb", "native": "Barbados", "region": "Americas", "currency": "BBD", "currency_name": "Barbadian dollar", "currency_symbol": "Bds$", "wikiDataId": null, "lat": "13.17", "lng": "-59.53", "emoji": "🇧🇧", "emojiU": "U+1F1E7 U+1F1E7", "flag": "1", "is_activated": "1"}, {"id": "20", "name": "Belarus", "code": "BY", "phone": "112", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Belarus\", \"cn\": \"白俄罗斯\", \"de\": \"Weißrussland\", \"es\": \"Bielorrusia\", \"fa\": \"بلاروس\", \"fr\": \"Biélorussie\", \"hr\": \"Bjelorusija\", \"it\": \"Bielorussia\", \"ja\": \"ベラルーシ\", \"kr\": \"벨라루스\", \"nl\": \"Wit-Rusland\", \"pt\": \"Bielorrússia\", \"tr\": \"Belarus\", \"pt-BR\": \"Bielorrússia\"}", "timezones": "[{\"tzName\": \"Moscow Time\", \"zoneName\": \"Europe/Minsk\", \"gmtOffset\": 10800, \"abbreviation\": \"MSK\", \"gmtOffsetName\": \"UTC+03:00\"}]", "numeric_code": "112", "iso3": "BLR", "nationality": "Belarusian", "capital": "Minsk", "tld": ".by", "native": "Белару́сь", "region": "Europe", "currency": "BYN", "currency_name": "Belarusian ruble", "currency_symbol": "Br", "wikiDataId": null, "lat": "53.00", "lng": "28.00", "emoji": "🇧🇾", "emojiU": "U+1F1E7 U+1F1FE", "flag": "1", "is_activated": "1"}, {"id": "21", "name": "Belgium", "code": "BE", "phone": "056", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Belgium\", \"cn\": \"比利时\", \"de\": \"Belgien\", \"es\": \"Bélgica\", \"fa\": \"بلژیک\", \"fr\": \"Belgique\", \"hr\": \"Belgija\", \"it\": \"Belgio\", \"ja\": \"ベルギー\", \"kr\": \"벨기에\", \"nl\": \"België\", \"pt\": \"Bélgica\", \"tr\": \"Belçika\", \"pt-BR\": \"Bélgica\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Brussels\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "056", "iso3": "BEL", "nationality": "Belgian", "capital": "Brussels", "tld": ".be", "native": "België", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "50.83", "lng": "4.00", "emoji": "🇧🇪", "emojiU": "U+1F1E7 U+1F1EA", "flag": "1", "is_activated": "1"}, {"id": "22", "name": "Belize", "code": "BZ", "phone": "084", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Belize\", \"cn\": \"伯利兹\", \"de\": \"Belize\", \"es\": \"Belice\", \"fa\": \"بلیز\", \"fr\": \"Belize\", \"hr\": \"Belize\", \"it\": \"Belize\", \"ja\": \"ベリーズ\", \"kr\": \"벨리즈\", \"nl\": \"Belize\", \"pt\": \"Belize\", \"tr\": \"Belize\", \"pt-BR\": \"Belize\"}", "timezones": "[{\"tzName\": \"Central Standard Time (North America)\", \"zoneName\": \"America/Belize\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}]", "numeric_code": "084", "iso3": "BLZ", "nationality": "Belizean", "capital": "Belmopan", "tld": ".bz", "native": "Belize", "region": "Americas", "currency": "BZD", "currency_name": "Belize dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "17.25", "lng": "-88.75", "emoji": "🇧🇿", "emojiU": "U+1F1E7 U+1F1FF", "flag": "1", "is_activated": "1"}, {"id": "23", "name": "Benin", "code": "BJ", "phone": "204", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Benin\", \"cn\": \"贝宁\", \"de\": \"Benin\", \"es\": \"Benín\", \"fa\": \"بنین\", \"fr\": \"B<PERSON><PERSON>\", \"hr\": \"Benin\", \"it\": \"Benin\", \"ja\": \"ベナン\", \"kr\": \"베냉\", \"nl\": \"Benin\", \"pt\": \"Benim\", \"tr\": \"Benin\", \"pt-BR\": \"Benin\"}", "timezones": "[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Porto-Novo\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "204", "iso3": "BEN", "nationality": "<PERSON><PERSON>, Beninois", "capital": "Porto-Novo", "tld": ".bj", "native": "<PERSON><PERSON><PERSON>", "region": "Africa", "currency": "XOF", "currency_name": "West African CFA franc", "currency_symbol": "CFA", "wikiDataId": null, "lat": "9.50", "lng": "2.25", "emoji": "🇧🇯", "emojiU": "U+1F1E7 U+1F1EF", "flag": "1", "is_activated": "1"}, {"id": "24", "name": "Bermuda", "code": "BM", "phone": "060", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Bermuda\", \"cn\": \"百慕大\", \"de\": \"Bermuda\", \"es\": \"Bermudas\", \"fa\": \"برمودا\", \"fr\": \"Bermudes\", \"hr\": \"<PERSON><PERSON><PERSON>\", \"it\": \"Bermuda\", \"ja\": \"バミューダ\", \"kr\": \"버뮤다\", \"nl\": \"Bermuda\", \"pt\": \"Bermudas\", \"tr\": \"Bermuda\", \"pt-BR\": \"Bermudas\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"Atlantic/Bermuda\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "060", "iso3": "BMU", "nationality": "<PERSON><PERSON><PERSON>an, Bermudan", "capital": "<PERSON>", "tld": ".bm", "native": "Bermuda", "region": "Americas", "currency": "BMD", "currency_name": "Bermudian dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "32.33", "lng": "-64.75", "emoji": "🇧🇲", "emojiU": "U+1F1E7 U+1F1F2", "flag": "1", "is_activated": "1"}, {"id": "25", "name": "Bhutan", "code": "BT", "phone": "064", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Bhutan\", \"cn\": \"不丹\", \"de\": \"Bhutan\", \"es\": \"Bután\", \"fa\": \"بوتان\", \"fr\": \"Bhoutan\", \"hr\": \"Butan\", \"it\": \"Bhutan\", \"ja\": \"ブータン\", \"kr\": \"부탄\", \"nl\": \"Bhutan\", \"pt\": \"But<PERSON>\", \"tr\": \"Butan\", \"pt-BR\": \"<PERSON><PERSON>\"}", "timezones": "[{\"tzName\": \"Bhutan Time\", \"zoneName\": \"Asia/Thimphu\", \"gmtOffset\": 21600, \"abbreviation\": \"BTT\", \"gmtOffsetName\": \"UTC+06:00\"}]", "numeric_code": "064", "iso3": "BTN", "nationality": "Bhutanese", "capital": "<PERSON><PERSON><PERSON><PERSON>", "tld": ".bt", "native": "ʼbrug-yul", "region": "Asia", "currency": "BTN", "currency_name": "Bhutanese ngultrum", "currency_symbol": "Nu.", "wikiDataId": null, "lat": "27.50", "lng": "90.50", "emoji": "🇧🇹", "emojiU": "U+1F1E7 U+1F1F9", "flag": "1", "is_activated": "1"}, {"id": "26", "name": "Bolivia", "code": "BO", "phone": "068", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Bolivia\", \"cn\": \"玻利维亚\", \"de\": \"Bolivien\", \"es\": \"Bolivia\", \"fa\": \"بولیوی\", \"fr\": \"Bolivie\", \"hr\": \"Bolivija\", \"it\": \"Bolivia\", \"ja\": \"ボリビア多民族国\", \"kr\": \"볼리비아\", \"nl\": \"Bolivia\", \"pt\": \"Bolívia\", \"tr\": \"Bolivya\", \"pt-BR\": \"Bolívia\"}", "timezones": "[{\"tzName\": \"Bolivia Time\", \"zoneName\": \"America/La_Paz\", \"gmtOffset\": -14400, \"abbreviation\": \"BOT\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "068", "iso3": "BOL", "nationality": "Bolivian", "capital": "<PERSON><PERSON>", "tld": ".bo", "native": "Bolivia", "region": "Americas", "currency": "BOB", "currency_name": "Bolivian boliviano", "currency_symbol": "Bs.", "wikiDataId": null, "lat": "-17.00", "lng": "-65.00", "emoji": "🇧🇴", "emojiU": "U+1F1E7 U+1F1F4", "flag": "1", "is_activated": "1"}, {"id": "27", "name": "Bonaire, Sint Eustatius and Saba", "code": "BQ", "phone": "535", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Bonaire, Sint Eustatius and Saba\", \"cn\": \"博内尔岛、圣尤斯特歇斯和萨巴岛\", \"de\": \"Bonaire, Sint Eustatius und Saba\", \"fa\": \"بونیر\", \"fr\": \"<PERSON><PERSON>, Saint-Eust<PERSON> et Saba\", \"it\": \"<PERSON><PERSON>, Saint-Eustache e Saba\", \"kr\": \"보네르 섬\", \"pt\": \"Bonaire\", \"tr\": \"Karayip Hollandasi\", \"pt-BR\": \"Bonaire\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Anguilla\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "535", "iso3": "BES", "nationality": "Bonaire", "capital": "Kralendijk", "tld": ".an", "native": "Caribisch Nederland", "region": "Americas", "currency": "USD", "currency_name": "United States dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "12.15", "lng": "-68.27", "emoji": "🇧🇶", "emojiU": "U+1F1E7 U+1F1F6", "flag": "1", "is_activated": "1"}, {"id": "28", "name": "Bosnia and Herzegovina", "code": "BA", "phone": "070", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Bosnia and Herzegovina\", \"cn\": \"波斯尼亚和黑塞哥维那\", \"de\": \"Bosnien und Herzegowina\", \"es\": \"Bosnia y Herzegovina\", \"fa\": \"بوسنی و هرزگوین\", \"fr\": \"Bosnie-Herzégovine\", \"hr\": \"Bosna i Hercegovina\", \"it\": \"Bosnia ed Erzegovina\", \"ja\": \"ボスニア・ヘルツェゴビナ\", \"kr\": \"보스니아 헤르체고비나\", \"nl\": \"Bosnië en Herzegovina\", \"pt\": \"Bósnia e Herzegovina\", \"tr\": \"Bosna Hersek\", \"pt-BR\": \"Bósnia e Herzegovina\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Sarajevo\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "070", "iso3": "BIH", "nationality": "Bosnian or Herzegovinian", "capital": "Sarajevo", "tld": ".ba", "native": "Bosna i Hercegovina", "region": "Europe", "currency": "BAM", "currency_name": "Bosnia and Herzegovina convertible mark", "currency_symbol": "KM", "wikiDataId": null, "lat": "44.00", "lng": "18.00", "emoji": "🇧🇦", "emojiU": "U+1F1E7 U+1F1E6", "flag": "1", "is_activated": "1"}, {"id": "29", "name": "Botswana", "code": "BW", "phone": "072", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Botswana\", \"cn\": \"博茨瓦纳\", \"de\": \"Botswana\", \"es\": \"Botswana\", \"fa\": \"بوتسوانا\", \"fr\": \"Botswana\", \"hr\": \"Bocvana\", \"it\": \"Botswana\", \"ja\": \"ボツワナ\", \"kr\": \"보츠와나\", \"nl\": \"Botswana\", \"pt\": \"Botsuana\", \"tr\": \"Botsvana\", \"pt-BR\": \"Botsuana\"}", "timezones": "[{\"tzName\": \"Central Africa Time\", \"zoneName\": \"Africa/Gaborone\", \"gmtOffset\": 7200, \"abbreviation\": \"CAT\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "072", "iso3": "BWA", "nationality": "Motswana, Botswanan", "capital": "Gaborone", "tld": ".bw", "native": "Botswana", "region": "Africa", "currency": "BWP", "currency_name": "Botswana pula", "currency_symbol": "P", "wikiDataId": null, "lat": "-22.00", "lng": "24.00", "emoji": "🇧🇼", "emojiU": "U+1F1E7 U+1F1FC", "flag": "1", "is_activated": "1"}, {"id": "30", "name": "Bouvet Island", "code": "BV", "phone": "074", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Bouvet Island\", \"cn\": \"布维岛\", \"de\": \"<PERSON><PERSON>tin<PERSON>\", \"es\": \"<PERSON>la Bouvet\", \"fa\": \"جزیره بووه\", \"fr\": \"Île Bouvet\", \"hr\": \"Otok Bouvet\", \"it\": \"Isola Bouvet\", \"ja\": \"ブーベ島\", \"kr\": \"부벳 섬\", \"nl\": \"Bouveteiland\", \"pt\": \"Il<PERSON> Bouvet\", \"tr\": \"Bouvet Adasi\", \"pt-BR\": \"Ilha Bouvet\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Oslo\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "074", "iso3": "BVT", "nationality": "Bouvet Island", "capital": "", "tld": ".bv", "native": "Bouvetøya", "region": "", "currency": "NOK", "currency_name": "Norwegian Krone", "currency_symbol": "kr", "wikiDataId": null, "lat": "-54.43", "lng": "3.40", "emoji": "🇧🇻", "emojiU": "U+1F1E7 U+1F1FB", "flag": "1", "is_activated": "1"}, {"id": "31", "name": "Brazil", "code": "BR", "phone": "076", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Brazil\", \"cn\": \"巴西\", \"de\": \"Brasilien\", \"es\": \"Brasil\", \"fa\": \"برزیل\", \"fr\": \"<PERSON><PERSON>sil\", \"hr\": \"Brazil\", \"it\": \"Brasile\", \"ja\": \"ブラジル\", \"kr\": \"브라질\", \"nl\": \"Brazilië\", \"pt\": \"Brasil\", \"tr\": \"<PERSON><PERSON><PERSON><PERSON>\", \"pt-BR\": \"Brasil\"}", "timezones": "[{\"tzName\": \"Brasília Time\", \"zoneName\": \"America/Araguaina\", \"gmtOffset\": -10800, \"abbreviation\": \"BRT\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Brasília Time\", \"zoneName\": \"America/Bahia\", \"gmtOffset\": -10800, \"abbreviation\": \"BRT\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Brasília Time\", \"zoneName\": \"America/Belem\", \"gmtOffset\": -10800, \"abbreviation\": \"BRT\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Amazon Time (Brazil)[3\", \"zoneName\": \"America/Boa_Vista\", \"gmtOffset\": -14400, \"abbreviation\": \"AMT\", \"gmtOffsetName\": \"UTC-04:00\"}, {\"tzName\": \"Amazon Time (Brazil)[3\", \"zoneName\": \"America/Campo_Grande\", \"gmtOffset\": -14400, \"abbreviation\": \"AMT\", \"gmtOffsetName\": \"UTC-04:00\"}, {\"tzName\": \"Brasilia Time\", \"zoneName\": \"America/Cuiaba\", \"gmtOffset\": -14400, \"abbreviation\": \"BRT\", \"gmtOffsetName\": \"UTC-04:00\"}, {\"tzName\": \"Acre Time\", \"zoneName\": \"America/Eirunepe\", \"gmtOffset\": -18000, \"abbreviation\": \"ACT\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Brasília Time\", \"zoneName\": \"America/Fortaleza\", \"gmtOffset\": -10800, \"abbreviation\": \"BRT\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Brasília Time\", \"zoneName\": \"America/Maceio\", \"gmtOffset\": -10800, \"abbreviation\": \"BRT\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Amazon Time (Brazil)\", \"zoneName\": \"America/Manaus\", \"gmtOffset\": -14400, \"abbreviation\": \"AMT\", \"gmtOffsetName\": \"UTC-04:00\"}, {\"tzName\": \"Fernando de Noronha Time\", \"zoneName\": \"America/Noronha\", \"gmtOffset\": -7200, \"abbreviation\": \"FNT\", \"gmtOffsetName\": \"UTC-02:00\"}, {\"tzName\": \"Amazon Time (Brazil)[3\", \"zoneName\": \"America/Porto_Velho\", \"gmtOffset\": -14400, \"abbreviation\": \"AMT\", \"gmtOffsetName\": \"UTC-04:00\"}, {\"tzName\": \"Brasília Time\", \"zoneName\": \"America/Recife\", \"gmtOffset\": -10800, \"abbreviation\": \"BRT\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Acre Time\", \"zoneName\": \"America/Rio_Branco\", \"gmtOffset\": -18000, \"abbreviation\": \"ACT\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Brasília Time\", \"zoneName\": \"America/Santarem\", \"gmtOffset\": -10800, \"abbreviation\": \"BRT\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Brasília Time\", \"zoneName\": \"America/Sao_Paulo\", \"gmtOffset\": -10800, \"abbreviation\": \"BRT\", \"gmtOffsetName\": \"UTC-03:00\"}]", "numeric_code": "076", "iso3": "BRA", "nationality": "Brazilian", "capital": "Brasilia", "tld": ".br", "native": "Brasil", "region": "Americas", "currency": "BRL", "currency_name": "Brazilian real", "currency_symbol": "R$", "wikiDataId": null, "lat": "-10.00", "lng": "-55.00", "emoji": "🇧🇷", "emojiU": "U+1F1E7 U+1F1F7", "flag": "1", "is_activated": "1"}, {"id": "32", "name": "British Indian Ocean Territory", "code": "IO", "phone": "086", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"British Indian Ocean Territory\", \"cn\": \"英属印度洋领地\", \"de\": \"Britisches Territorium im Indischen Ozean\", \"es\": \"Territorio Británico del Océano Índico\", \"fa\": \"قلمرو بریتانیا در اقیانوس هند\", \"fr\": \"Territoire britannique de locéan Indien\", \"hr\": \"Britanski Indijskooceanski teritorij\", \"it\": \"Territorio britannico dell oceano indiano\", \"ja\": \"イギリス領インド洋地域\", \"kr\": \"영국령 인도양 지역\", \"nl\": \"Brits<PERSON> Gebieden in de Indische Oceaan\", \"pt\": \"Território Britânico do Oceano Índico\", \"tr\": \"Britanya Hint Okyanusu Topraklari\", \"pt-BR\": \"Território Britânico do Oceano íÍdico\"}", "timezones": "[{\"tzName\": \"Indian Ocean Time\", \"zoneName\": \"Indian/Chagos\", \"gmtOffset\": 21600, \"abbreviation\": \"IOT\", \"gmtOffsetName\": \"UTC+06:00\"}]", "numeric_code": "086", "iso3": "IOT", "nationality": "BIOT", "capital": "<PERSON>", "tld": ".io", "native": "British Indian Ocean Territory", "region": "Africa", "currency": "USD", "currency_name": "United States dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "-6.00", "lng": "71.50", "emoji": "🇮🇴", "emojiU": "U+1F1EE U+1F1F4", "flag": "1", "is_activated": "1"}, {"id": "33", "name": "Brunei", "code": "BN", "phone": "096", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Brunei\", \"cn\": \"文莱\", \"de\": \"Brunei\", \"es\": \"Brunei\", \"fa\": \"برونئی\", \"fr\": \"Brunei\", \"hr\": \"<PERSON>run<PERSON>\", \"it\": \"Brunei\", \"ja\": \"ブルネイ・ダルサラーム\", \"kr\": \"브루나이\", \"nl\": \"Brunei\", \"pt\": \"Brunei\", \"tr\": \"Brunei\", \"pt-BR\": \"Brunei\"}", "timezones": "[{\"tzName\": \"Brunei Darussalam Time\", \"zoneName\": \"Asia/Brunei\", \"gmtOffset\": 28800, \"abbreviation\": \"BNT\", \"gmtOffsetName\": \"UTC+08:00\"}]", "numeric_code": "096", "iso3": "BRN", "nationality": "Bruneian", "capital": "Bandar Seri Begawan", "tld": ".bn", "native": "Negara Brunei Darussalam", "region": "Asia", "currency": "BND", "currency_name": "Brunei dollar", "currency_symbol": "B$", "wikiDataId": null, "lat": "4.50", "lng": "114.67", "emoji": "🇧🇳", "emojiU": "U+1F1E7 U+1F1F3", "flag": "1", "is_activated": "1"}, {"id": "34", "name": "Bulgaria", "code": "BG", "phone": "100", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Bulgaria\", \"cn\": \"保加利亚\", \"de\": \"Bulgarien\", \"es\": \"Bulgaria\", \"fa\": \"بلغارستان\", \"fr\": \"Bulgarie\", \"hr\": \"Bugarska\", \"it\": \"Bulgaria\", \"ja\": \"ブルガリア\", \"kr\": \"불가리아\", \"nl\": \"Bulgarije\", \"pt\": \"Bulgária\", \"tr\": \"Bulgaristan\", \"pt-BR\": \"Bulgária\"}", "timezones": "[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Sofia\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "100", "iso3": "BGR", "nationality": "Bulgarian", "capital": "Sofia", "tld": ".bg", "native": "България", "region": "Europe", "currency": "BGN", "currency_name": "Bulgarian lev", "currency_symbol": "Л<PERSON>.", "wikiDataId": null, "lat": "43.00", "lng": "25.00", "emoji": "🇧🇬", "emojiU": "U+1F1E7 U+1F1EC", "flag": "1", "is_activated": "1"}, {"id": "35", "name": "Burkina Faso", "code": "BF", "phone": "854", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Burkina Faso\", \"cn\": \"布基纳法索\", \"de\": \"Burkina Faso\", \"es\": \"Burkina Faso\", \"fa\": \"بورکینافاسو\", \"fr\": \"Burkina Faso\", \"hr\": \"Burkina Faso\", \"it\": \"Burkina Faso\", \"ja\": \"ブルキナファソ\", \"kr\": \"부르키나 파소\", \"nl\": \"Burkina Faso\", \"pt\": \"Burquina Faso\", \"tr\": \"Burkina Faso\", \"pt-BR\": \"Burkina Faso\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Ouagadougou\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "854", "iso3": "BFA", "nationality": "Burkinabe", "capital": "Ouagadougou", "tld": ".bf", "native": "Burkina Faso", "region": "Africa", "currency": "XOF", "currency_name": "West African CFA franc", "currency_symbol": "CFA", "wikiDataId": null, "lat": "13.00", "lng": "-2.00", "emoji": "🇧🇫", "emojiU": "U+1F1E7 U+1F1EB", "flag": "1", "is_activated": "1"}, {"id": "36", "name": "Burundi", "code": "BI", "phone": "108", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Burundi\", \"cn\": \"布隆迪\", \"de\": \"Burundi\", \"es\": \"Burundi\", \"fa\": \"بوروندی\", \"fr\": \"Burundi\", \"hr\": \"Burundi\", \"it\": \"Burundi\", \"ja\": \"ブルンジ\", \"kr\": \"부룬디\", \"nl\": \"Burundi\", \"pt\": \"<PERSON><PERSON><PERSON><PERSON>\", \"tr\": \"Burundi\", \"pt-BR\": \"Burundi\"}", "timezones": "[{\"tzName\": \"Central Africa Time\", \"zoneName\": \"Africa/Bujumbura\", \"gmtOffset\": 7200, \"abbreviation\": \"CAT\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "108", "iso3": "BDI", "nationality": "Burundian", "capital": "Bujumbura", "tld": ".bi", "native": "Burundi", "region": "Africa", "currency": "BIF", "currency_name": "Burundian franc", "currency_symbol": "FBu", "wikiDataId": null, "lat": "-3.50", "lng": "30.00", "emoji": "🇧🇮", "emojiU": "U+1F1E7 U+1F1EE", "flag": "1", "is_activated": "1"}, {"id": "37", "name": "Cambodia", "code": "KH", "phone": "116", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Cambodia\", \"cn\": \"柬埔寨\", \"de\": \"Kambodscha\", \"es\": \"Camboya\", \"fa\": \"کامبوج\", \"fr\": \"Cambodge\", \"hr\": \"Kambodž<PERSON>\", \"it\": \"Cambogia\", \"ja\": \"カンボジア\", \"kr\": \"캄보디아\", \"nl\": \"Cambodja\", \"pt\": \"Camboja\", \"tr\": \"Kamboçya\", \"pt-BR\": \"Camboja\"}", "timezones": "[{\"tzName\": \"Indochina Time\", \"zoneName\": \"Asia/Phnom_Penh\", \"gmtOffset\": 25200, \"abbreviation\": \"ICT\", \"gmtOffsetName\": \"UTC+07:00\"}]", "numeric_code": "116", "iso3": "KHM", "nationality": "Cambodian", "capital": "Phnom Penh", "tld": ".kh", "native": "Kâmpŭchéa", "region": "Asia", "currency": "KHR", "currency_name": "Cambodian riel", "currency_symbol": "KHR", "wikiDataId": null, "lat": "13.00", "lng": "105.00", "emoji": "🇰🇭", "emojiU": "U+1F1F0 U+1F1ED", "flag": "1", "is_activated": "1"}, {"id": "38", "name": "Cameroon", "code": "CM", "phone": "120", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Cameroon\", \"cn\": \"喀麦隆\", \"de\": \"Kamerun\", \"es\": \"Camer<PERSON>\", \"fa\": \"کامرون\", \"fr\": \"Cameroun\", \"hr\": \"Kamerun\", \"it\": \"Camerun\", \"ja\": \"カメルーン\", \"kr\": \"카메룬\", \"nl\": \"Kameroen\", \"pt\": \"Camarões\", \"tr\": \"Kamerun\", \"pt-BR\": \"Camarões\"}", "timezones": "[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Douala\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "120", "iso3": "CMR", "nationality": "Cameroonian", "capital": "Yaounde", "tld": ".cm", "native": "Cameroon", "region": "Africa", "currency": "XAF", "currency_name": "Central African CFA franc", "currency_symbol": "FCFA", "wikiDataId": null, "lat": "6.00", "lng": "12.00", "emoji": "🇨🇲", "emojiU": "U+1F1E8 U+1F1F2", "flag": "1", "is_activated": "1"}, {"id": "39", "name": "Canada", "code": "CA", "phone": "124", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Canada\", \"cn\": \"加拿大\", \"de\": \"Kana<PERSON>\", \"es\": \"Canad<PERSON>\", \"fa\": \"کانادا\", \"fr\": \"Canada\", \"hr\": \"Kanada\", \"it\": \"Canada\", \"ja\": \"カナダ\", \"kr\": \"캐나다\", \"nl\": \"Canada\", \"pt\": \"Canadá\", \"tr\": \"Kanada\", \"pt-BR\": \"Canad<PERSON>\"}", "timezones": "[{\"tzName\": \"Eastern Standard Time (North America)\", \"zoneName\": \"America/Atikokan\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Blanc-Sablon\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}, {\"tzName\": \"Mountain Standard Time (North America)\", \"zoneName\": \"America/Cambridge_Bay\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Mountain Standard Time (North America)\", \"zoneName\": \"America/Creston\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Mountain Standard Time (North America)\", \"zoneName\": \"America/Dawson\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Mountain Standard Time (North America)\", \"zoneName\": \"America/Dawson_Creek\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Mountain Standard Time (North America)\", \"zoneName\": \"America/Edmonton\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Mountain Standard Time (North America)\", \"zoneName\": \"America/Fort_Nelson\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Glace_Bay\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}, {\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Goose_Bay\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}, {\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Halifax\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}, {\"tzName\": \"Mountain Standard Time (North America\", \"zoneName\": \"America/Inuvik\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Iqaluit\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Moncton\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Nipigon\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Pangnirtung\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Rainy_River\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Rankin_Inlet\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Regina\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Resolute\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Newfoundland Standard Time\", \"zoneName\": \"America/St_Johns\", \"gmtOffset\": -12600, \"abbreviation\": \"NST\", \"gmtOffsetName\": \"UTC-03:30\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Swift_Current\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Thunder_Bay\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Toronto\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Pacific Standard Time (North America\", \"zoneName\": \"America/Vancouver\", \"gmtOffset\": -28800, \"abbreviation\": \"PST\", \"gmtOffsetName\": \"UTC-08:00\"}, {\"tzName\": \"Mountain Standard Time (North America\", \"zoneName\": \"America/Whitehorse\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Winnipeg\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Mountain Standard Time (North America\", \"zoneName\": \"America/Yellowknife\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}]", "numeric_code": "124", "iso3": "CAN", "nationality": "Canadian", "capital": "Ottawa", "tld": ".ca", "native": "Canada", "region": "Americas", "currency": "CAD", "currency_name": "Canadian dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "60.00", "lng": "-95.00", "emoji": "🇨🇦", "emojiU": "U+1F1E8 U+1F1E6", "flag": "1", "is_activated": "1"}, {"id": "40", "name": "Cape Verde", "code": "CV", "phone": "132", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Cape Verde\", \"cn\": \"佛得角\", \"de\": \"Kap Verde\", \"es\": \"Cabo Verde\", \"fa\": \"کیپ ورد\", \"fr\": \"Cap Vert\", \"hr\": \"Zelenortska Republika\", \"it\": \"Capo Verde\", \"ja\": \"カーボベルデ\", \"kr\": \"카보베르데\", \"nl\": \"Kaapverdië\", \"pt\": \"Cabo Verde\", \"tr\": \"Cabo Verde\", \"pt-BR\": \"Cabo Verde\"}", "timezones": "[{\"tzName\": \"Cape Verde Time\", \"zoneName\": \"Atlantic/Cape_Verde\", \"gmtOffset\": -3600, \"abbreviation\": \"CVT\", \"gmtOffsetName\": \"UTC-01:00\"}]", "numeric_code": "132", "iso3": "CPV", "nationality": "Verdean", "capital": "Praia", "tld": ".cv", "native": "Cabo Verde", "region": "Africa", "currency": "CVE", "currency_name": "Cape Verdean escudo", "currency_symbol": "$", "wikiDataId": null, "lat": "16.00", "lng": "-24.00", "emoji": "🇨🇻", "emojiU": "U+1F1E8 U+1F1FB", "flag": "1", "is_activated": "1"}, {"id": "41", "name": "Cayman Islands", "code": "KY", "phone": "136", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Cayman Islands\", \"cn\": \"开曼群岛\", \"de\": \"Kaimaninseln\", \"es\": \"Islas Caimán\", \"fa\": \"جزایر کیمن\", \"fr\": \"Îles Caïmans\", \"hr\": \"<PERSON><PERSON><PERSON><PERSON>\", \"it\": \"Isole Cayman\", \"ja\": \"ケイマン諸島\", \"kr\": \"케이먼 제도\", \"nl\": \"Caymaneilanden\", \"pt\": \"Ilhas Caimão\", \"tr\": \"Cayman Adalari\", \"pt-BR\": \"Ilhas Cayman\"}", "timezones": "[{\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Cayman\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}]", "numeric_code": "136", "iso3": "CYM", "nationality": "Caymanian", "capital": "George Town", "tld": ".ky", "native": "Cayman Islands", "region": "Americas", "currency": "KYD", "currency_name": "Cayman Islands dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "19.50", "lng": "-80.50", "emoji": "🇰🇾", "emojiU": "U+1F1F0 U+1F1FE", "flag": "1", "is_activated": "1"}, {"id": "42", "name": "Central African Republic", "code": "CF", "phone": "140", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Central African Republic\", \"cn\": \"中非\", \"de\": \"Zentralafrikanische Republik\", \"es\": \"República Centroafricana\", \"fa\": \"جمهوری آفریقای مرکزی\", \"fr\": \"République centrafricaine\", \"hr\": \"Srednjoafrička Republika\", \"it\": \"Repubblica Centrafricana\", \"ja\": \"中央アフリカ共和国\", \"kr\": \"중앙아프리카 공화국\", \"nl\": \"Centraal-Afrikaanse Republiek\", \"pt\": \"República Centro-Africana\", \"tr\": \"Orta Afrika Cumhuriyeti\", \"pt-BR\": \"República Centro-Africana\"}", "timezones": "[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Bangui\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "140", "iso3": "CAF", "nationality": "Central African", "capital": "<PERSON><PERSON>", "tld": ".cf", "native": "Ködör<PERSON><PERSON><PERSON><PERSON> tî <PERSON>af<PERSON>î<PERSON>", "region": "Africa", "currency": "XAF", "currency_name": "Central African CFA franc", "currency_symbol": "FCFA", "wikiDataId": null, "lat": "7.00", "lng": "21.00", "emoji": "🇨🇫", "emojiU": "U+1F1E8 U+1F1EB", "flag": "1", "is_activated": "1"}, {"id": "43", "name": "Chad", "code": "TD", "phone": "148", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"<PERSON>\", \"cn\": \"乍得\", \"de\": \"Tschad\", \"es\": \"Chad\", \"fa\": \"چاد\", \"fr\": \"Tchad\", \"hr\": \"Čad\", \"it\": \"Ciad\", \"ja\": \"チャド\", \"kr\": \"차드\", \"nl\": \"Tsjaad\", \"pt\": \"<PERSON><PERSON>\", \"tr\": \"Çad\", \"pt-BR\": \"<PERSON><PERSON>\"}", "timezones": "[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Ndjamena\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "148", "iso3": "TCD", "nationality": "Chadian", "capital": "NDjamena", "tld": ".td", "native": "Tchad", "region": "Africa", "currency": "XAF", "currency_name": "Central African CFA franc", "currency_symbol": "FCFA", "wikiDataId": null, "lat": "15.00", "lng": "19.00", "emoji": "🇹🇩", "emojiU": "U+1F1F9 U+1F1E9", "flag": "1", "is_activated": "1"}, {"id": "44", "name": "Chile", "code": "CL", "phone": "152", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Chile\", \"cn\": \"智利\", \"de\": \"Chile\", \"es\": \"Chile\", \"fa\": \"شیلی\", \"fr\": \"Chili\", \"hr\": \"Čile\", \"it\": \"Cile\", \"ja\": \"チリ\", \"kr\": \"칠리\", \"nl\": \"Chili\", \"pt\": \"Chile\", \"tr\": \"<PERSON>ili\", \"pt-BR\": \"Chile\"}", "timezones": "[{\"tzName\": \"Chile Summer Time\", \"zoneName\": \"America/Punta_Arenas\", \"gmtOffset\": -10800, \"abbreviation\": \"CLST\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Chile Summer Time\", \"zoneName\": \"America/Santiago\", \"gmtOffset\": -10800, \"abbreviation\": \"CLST\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Easter Island Summer Time\", \"zoneName\": \"Pacific/Easter\", \"gmtOffset\": -18000, \"abbreviation\": \"EASST\", \"gmtOffsetName\": \"UTC-05:00\"}]", "numeric_code": "152", "iso3": "CHL", "nationality": "Chilean", "capital": "Santiago", "tld": ".cl", "native": "Chile", "region": "Americas", "currency": "CLP", "currency_name": "Chilean peso", "currency_symbol": "$", "wikiDataId": null, "lat": "-30.00", "lng": "-71.00", "emoji": "🇨🇱", "emojiU": "U+1F1E8 U+1F1F1", "flag": "1", "is_activated": "1"}, {"id": "45", "name": "China", "code": "CN", "phone": "156", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"China\", \"cn\": \"中国\", \"de\": \"China\", \"es\": \"China\", \"fa\": \"چین\", \"fr\": \"Chine\", \"hr\": \"Kina\", \"it\": \"Cina\", \"ja\": \"中国\", \"kr\": \"중국\", \"nl\": \"China\", \"pt\": \"China\", \"tr\": \"Çin\", \"pt-BR\": \"China\"}", "timezones": "[{\"tzName\": \"China Standard Time\", \"zoneName\": \"Asia/Shanghai\", \"gmtOffset\": 28800, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC+08:00\"}, {\"tzName\": \"China Standard Time\", \"zoneName\": \"Asia/Urumqi\", \"gmtOffset\": 21600, \"abbreviation\": \"XJT\", \"gmtOffsetName\": \"UTC+06:00\"}]", "numeric_code": "156", "iso3": "CHN", "nationality": "Chinese", "capital": "Beijing", "tld": ".cn", "native": "中国", "region": "Asia", "currency": "CNY", "currency_name": "Chinese yuan", "currency_symbol": "¥", "wikiDataId": null, "lat": "35.00", "lng": "105.00", "emoji": "🇨🇳", "emojiU": "U+1F1E8 U+1F1F3", "flag": "1", "is_activated": "1"}, {"id": "46", "name": "Christmas Island", "code": "CX", "phone": "162", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Christmas Island\", \"cn\": \"圣诞岛\", \"de\": \"Weihnachtsinsel\", \"es\": \"<PERSON>la de Navidad\", \"fa\": \"جزیره کریسمس\", \"fr\": \"Île Christmas\", \"hr\": \"<PERSON>ž<PERSON><PERSON> otok\", \"it\": \"<PERSON><PERSON> di Natale\", \"ja\": \"クリスマス島\", \"kr\": \"크리스마스 섬\", \"nl\": \"Christmaseiland\", \"pt\": \"Ilha do Natal\", \"tr\": \"Christmas Adasi\", \"pt-BR\": \"Ilha Christmas\"}", "timezones": "[{\"tzName\": \"Christmas Island Time\", \"zoneName\": \"Indian/Christmas\", \"gmtOffset\": 25200, \"abbreviation\": \"CXT\", \"gmtOffsetName\": \"UTC+07:00\"}]", "numeric_code": "162", "iso3": "CXR", "nationality": "Christmas Island", "capital": "Flying Fish Cove", "tld": ".cx", "native": "Christmas Island", "region": "Oceania", "currency": "AUD", "currency_name": "Australian dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "-10.50", "lng": "105.67", "emoji": "🇨🇽", "emojiU": "U+1F1E8 U+1F1FD", "flag": "1", "is_activated": "1"}, {"id": "47", "name": "Cocos (Keeling) Islands", "code": "CC", "phone": "166", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Cocos (Keeling) Islands\", \"cn\": \"科科斯（基林）群岛\", \"de\": \"Kokosinseln\", \"es\": \"<PERSON>las Cocos o Islas Keeling\", \"fa\": \"جزایر کوکوس\", \"fr\": \"Îles Cocos\", \"hr\": \"Kokosovi Otoci\", \"it\": \"Isole Cocos e Keeling\", \"ja\": \"ココス（キーリング）諸島\", \"kr\": \"코코스 제도\", \"nl\": \"Cocoseilanden\", \"pt\": \"Ilhas dos Cocos\", \"tr\": \"Cocos Adalari\", \"pt-BR\": \"Ilhas Cocos\"}", "timezones": "[{\"tzName\": \"Cocos Islands Time\", \"zoneName\": \"Indian/Cocos\", \"gmtOffset\": 23400, \"abbreviation\": \"CCT\", \"gmtOffsetName\": \"UTC+06:30\"}]", "numeric_code": "166", "iso3": "CCK", "nationality": "Cocos Island", "capital": "West Island", "tld": ".cc", "native": "Cocos (Keeling) Islands", "region": "Oceania", "currency": "AUD", "currency_name": "Australian dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "-12.50", "lng": "96.83", "emoji": "🇨🇨", "emojiU": "U+1F1E8 U+1F1E8", "flag": "1", "is_activated": "1"}, {"id": "48", "name": "Colombia", "code": "CO", "phone": "170", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Colombia\", \"cn\": \"哥伦比亚\", \"de\": \"Kolumbien\", \"es\": \"Colombia\", \"fa\": \"کلمبیا\", \"fr\": \"<PERSON><PERSON>ie\", \"hr\": \"Kolumbija\", \"it\": \"Colombia\", \"ja\": \"コロンビア\", \"kr\": \"콜롬비아\", \"nl\": \"Colombia\", \"pt\": \"Colômbia\", \"tr\": \"Kolombiya\", \"pt-BR\": \"Colômbia\"}", "timezones": "[{\"tzName\": \"Colombia Time\", \"zoneName\": \"America/Bogota\", \"gmtOffset\": -18000, \"abbreviation\": \"COT\", \"gmtOffsetName\": \"UTC-05:00\"}]", "numeric_code": "170", "iso3": "COL", "nationality": "Colombian", "capital": "Bogotá", "tld": ".co", "native": "Colombia", "region": "Americas", "currency": "COP", "currency_name": "Colombian peso", "currency_symbol": "$", "wikiDataId": null, "lat": "4.00", "lng": "-72.00", "emoji": "🇨🇴", "emojiU": "U+1F1E8 U+1F1F4", "flag": "1", "is_activated": "1"}, {"id": "49", "name": "Comoros", "code": "KM", "phone": "174", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Comoros\", \"cn\": \"科摩罗\", \"de\": \"Union der Komoren\", \"es\": \"Comoras\", \"fa\": \"کومور\", \"fr\": \"Comores\", \"hr\": \"<PERSON>mor<PERSON>\", \"it\": \"<PERSON><PERSON>\", \"ja\": \"コモロ\", \"kr\": \"코모로\", \"nl\": \"Comoren\", \"pt\": \"Comores\", \"tr\": \"Komor<PERSON>\", \"pt-BR\": \"Comores\"}", "timezones": "[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Indian/Comoro\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]", "numeric_code": "174", "iso3": "COM", "nationality": "<PERSON><PERSON>, Comorian", "capital": "<PERSON><PERSON><PERSON>", "tld": ".km", "native": "<PERSON><PERSON><PERSON>", "region": "Africa", "currency": "KMF", "currency_name": "Comorian franc", "currency_symbol": "CF", "wikiDataId": null, "lat": "-12.17", "lng": "44.25", "emoji": "🇰🇲", "emojiU": "U+1F1F0 U+1F1F2", "flag": "1", "is_activated": "1"}, {"id": "50", "name": "Congo", "code": "CG", "phone": "178", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Congo\", \"cn\": \"刚果\", \"de\": \"Kongo\", \"es\": \"Congo\", \"fa\": \"کنگو\", \"fr\": \"Congo\", \"hr\": \"Kongo\", \"it\": \"Congo\", \"ja\": \"コンゴ共和国\", \"kr\": \"콩고\", \"nl\": \"Congo [Republiek]\", \"pt\": \"Congo\", \"tr\": \"Kongo\", \"pt-BR\": \"Congo\"}", "timezones": "[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Brazzaville\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "178", "iso3": "COG", "nationality": "Congolese", "capital": "Brazzaville", "tld": ".cg", "native": "République du Congo", "region": "Africa", "currency": "XAF", "currency_name": "Central African CFA franc", "currency_symbol": "FC", "wikiDataId": null, "lat": "-1.00", "lng": "15.00", "emoji": "🇨🇬", "emojiU": "U+1F1E8 U+1F1EC", "flag": "1", "is_activated": "1"}, {"id": "51", "name": "Democratic Republic of the Congo", "code": "CD", "phone": "180", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Democratic Republic of the Congo\", \"cn\": \"刚果（金）\", \"de\": \"<PERSON><PERSON> (Dem. Rep.)\", \"es\": \"Congo (Rep. Dem.)\", \"fa\": \"جمهوری کنگو\", \"fr\": \"Congo (Rép. dém.)\", \"hr\": \"Kongo, Demokratska Republika\", \"it\": \"Congo (Rep. Dem.)\", \"ja\": \"コンゴ民主共和国\", \"kr\": \"콩고 민주 공화국\", \"nl\": \"Congo [DRC]\", \"pt\": \"RD Congo\", \"tr\": \"Kongo Demokratik Cumhuriyeti\", \"pt-BR\": \"RD Congo\"}", "timezones": "[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Kinshasa\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}, {\"tzName\": \"Central Africa Time\", \"zoneName\": \"Africa/Lubumbashi\", \"gmtOffset\": 7200, \"abbreviation\": \"CAT\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "180", "iso3": "COD", "nationality": "Congolese", "capital": "Kinshasa", "tld": ".cd", "native": "République démocratique du Congo", "region": "Africa", "currency": "CDF", "currency_name": "Congolese Franc", "currency_symbol": "FC", "wikiDataId": null, "lat": "0.00", "lng": "25.00", "emoji": "🇨🇩", "emojiU": "U+1F1E8 U+1F1E9", "flag": "1", "is_activated": "1"}, {"id": "52", "name": "Cook Islands", "code": "CK", "phone": "184", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Cook Islands\", \"cn\": \"库克群岛\", \"de\": \"Cookinseln\", \"es\": \"<PERSON><PERSON>\", \"fa\": \"جزایر کوک\", \"fr\": \"Îles Cook\", \"hr\": \"<PERSON>ovo Otočje\", \"it\": \"<PERSON><PERSON> Cook\", \"ja\": \"クック諸島\", \"kr\": \"쿡 제도\", \"nl\": \"Cookeilanden\", \"pt\": \"<PERSON><PERSON>\", \"tr\": \"Cook Adalari\", \"pt-BR\": \"<PERSON><PERSON>\"}", "timezones": "[{\"tzName\": \"Cook Island Time\", \"zoneName\": \"Pacific/Rarotonga\", \"gmtOffset\": -36000, \"abbreviation\": \"CKT\", \"gmtOffsetName\": \"UTC-10:00\"}]", "numeric_code": "184", "iso3": "COK", "nationality": "Cook Island", "capital": "<PERSON><PERSON><PERSON>", "tld": ".ck", "native": "Cook Islands", "region": "Oceania", "currency": "NZD", "currency_name": "Cook Islands dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "-21.23", "lng": "-159.77", "emoji": "🇨🇰", "emojiU": "U+1F1E8 U+1F1F0", "flag": "1", "is_activated": "1"}, {"id": "53", "name": "Costa Rica", "code": "CR", "phone": "188", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Costa Rica\", \"cn\": \"哥斯达黎加\", \"de\": \"Costa Rica\", \"es\": \"Costa Rica\", \"fa\": \"کاستاریکا\", \"fr\": \"Costa Rica\", \"hr\": \"Kostarika\", \"it\": \"Costa Rica\", \"ja\": \"コスタリカ\", \"kr\": \"코스타리카\", \"nl\": \"Costa Rica\", \"pt\": \"Costa Rica\", \"tr\": \"Kosta Rika\", \"pt-BR\": \"Costa Rica\"}", "timezones": "[{\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Costa_Rica\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}]", "numeric_code": "188", "iso3": "CRI", "nationality": "Costa Rican", "capital": "San Jose", "tld": ".cr", "native": "Costa Rica", "region": "Americas", "currency": "CRC", "currency_name": "Costa Rican colón", "currency_symbol": "₡", "wikiDataId": null, "lat": "10.00", "lng": "-84.00", "emoji": "🇨🇷", "emojiU": "U+1F1E8 U+1F1F7", "flag": "1", "is_activated": "1"}, {"id": "54", "name": "Cote DIvoire (Ivory Coast)", "code": "CI", "phone": "384", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Cote DIvoire (Ivory Coast)\", \"cn\": \"科特迪瓦\", \"de\": \"Elfenbeinküste\", \"es\": \"Costa de Marfil\", \"fa\": \"ساحل عاج\", \"fr\": \"Côte dIvoire\", \"hr\": \"<PERSON>bala Bjelokosti\", \"it\": \"Costa DAvorio\", \"ja\": \"コートジボワール\", \"kr\": \"코트디부아르\", \"nl\": \"Ivoorkust\", \"pt\": \"Costa do Marfim\", \"tr\": \"Kotdivuar\", \"pt-BR\": \"Costa do Marfim\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Abidjan\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "384", "iso3": "CIV", "nationality": "Ivorian", "capital": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tld": ".ci", "native": null, "region": "Africa", "currency": "XOF", "currency_name": "West African CFA franc", "currency_symbol": "CFA", "wikiDataId": null, "lat": "8.00", "lng": "-5.00", "emoji": "🇨🇮", "emojiU": "U+1F1E8 U+1F1EE", "flag": "1", "is_activated": "1"}, {"id": "55", "name": "Croatia", "code": "HR", "phone": "191", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Croatia\", \"cn\": \"克罗地亚\", \"de\": \"Kroatien\", \"es\": \"Croacia\", \"fa\": \"کرواسی\", \"fr\": \"Croatie\", \"hr\": \"Hrvatska\", \"it\": \"Croazia\", \"ja\": \"クロアチア\", \"kr\": \"크로아티아\", \"nl\": \"Kroatië\", \"pt\": \"Croácia\", \"tr\": \"Hirvatistan\", \"pt-BR\": \"Croácia\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Zagreb\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "191", "iso3": "HRV", "nationality": "Croatian", "capital": "Zagreb", "tld": ".hr", "native": "Hrvatska", "region": "Europe", "currency": "HRK", "currency_name": "Croatian kuna", "currency_symbol": "kn", "wikiDataId": null, "lat": "45.17", "lng": "15.50", "emoji": "🇭🇷", "emojiU": "U+1F1ED U+1F1F7", "flag": "1", "is_activated": "1"}, {"id": "56", "name": "Cuba", "code": "CU", "phone": "192", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Cuba\", \"cn\": \"古巴\", \"de\": \"<PERSON><PERSON>\", \"es\": \"Cuba\", \"fa\": \"کوبا\", \"fr\": \"Cuba\", \"hr\": \"<PERSON><PERSON>\", \"it\": \"Cuba\", \"ja\": \"キューバ\", \"kr\": \"쿠바\", \"nl\": \"Cuba\", \"pt\": \"Cuba\", \"tr\": \"Küba\", \"pt-BR\": \"Cuba\"}", "timezones": "[{\"tzName\": \"Cuba Standard Time\", \"zoneName\": \"America/Havana\", \"gmtOffset\": -18000, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-05:00\"}]", "numeric_code": "192", "iso3": "CUB", "nationality": "Cuban", "capital": "Havana", "tld": ".cu", "native": "Cuba", "region": "Americas", "currency": "CUP", "currency_name": "Cuban peso", "currency_symbol": "$", "wikiDataId": null, "lat": "21.50", "lng": "-80.00", "emoji": "🇨🇺", "emojiU": "U+1F1E8 U+1F1FA", "flag": "1", "is_activated": "1"}, {"id": "57", "name": "Curaçao", "code": "CW", "phone": "531", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Curaçao\", \"cn\": \"库拉索\", \"de\": \"Curaçao\", \"fa\": \"کوراسائو\", \"fr\": \"Curaçao\", \"it\": \"Curaçao\", \"kr\": \"퀴라소\", \"nl\": \"Curaçao\", \"pt\": \"Curaçao\", \"tr\": \"Curaçao\", \"pt-BR\": \"Curaçao\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Curacao\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "531", "iso3": "CUW", "nationality": "Curacaoan", "capital": "Willem<PERSON>", "tld": ".cw", "native": "Curaçao", "region": "Americas", "currency": "ANG", "currency_name": "Netherlands Antillean guilder", "currency_symbol": "ƒ", "wikiDataId": null, "lat": "12.12", "lng": "-68.93", "emoji": "🇨🇼", "emojiU": "U+1F1E8 U+1F1FC", "flag": "1", "is_activated": "1"}, {"id": "58", "name": "Cyprus", "code": "CY", "phone": "196", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Cyprus\", \"cn\": \"塞浦路斯\", \"de\": \"<PERSON><PERSON><PERSON>\", \"es\": \"<PERSON>re\", \"fa\": \"قبرس\", \"fr\": \"<PERSON>y<PERSON><PERSON>\", \"hr\": \"<PERSON>ipar\", \"it\": \"<PERSON><PERSON><PERSON>\", \"ja\": \"キプロス\", \"kr\": \"키프로스\", \"nl\": \"Cyprus\", \"pt\": \"Chipre\", \"tr\": \"Kuzey Kıbrıs Türk Cumhuriyeti\", \"pt-BR\": \"Chipre\"}", "timezones": "[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Asia/Famagusta\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}, {\"tzName\": \"Eastern European Time\", \"zoneName\": \"Asia/Nicosia\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "196", "iso3": "CYP", "nationality": "Cypriot", "capital": "Nicosia", "tld": ".cy", "native": "Κύ<PERSON>ρ<PERSON>", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "35.00", "lng": "33.00", "emoji": "🇨🇾", "emojiU": "U+1F1E8 U+1F1FE", "flag": "1", "is_activated": "1"}, {"id": "59", "name": "Czech Republic", "code": "CZ", "phone": "203", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Czech Republic\", \"cn\": \"捷克\", \"de\": \"Tschechische Republik\", \"es\": \"República Checa\", \"fa\": \"جمهوری چک\", \"fr\": \"République tchèque\", \"hr\": \"<PERSON><PERSON><PERSON><PERSON>\", \"it\": \"Repubblica Ceca\", \"ja\": \"チェコ\", \"kr\": \"체코\", \"nl\": \"<PERSON>sjechië\", \"pt\": \"República Checa\", \"tr\": \"<PERSON>ek<PERSON>\", \"pt-BR\": \"República Tcheca\"}", "timezones": null, "numeric_code": "203", "iso3": "CZE", "nationality": "Czech", "capital": "Prague", "tld": ".cz", "native": "Česká republika", "region": "Europe", "currency": "CZK", "currency_name": "Czech koruna", "currency_symbol": "Kč", "wikiDataId": null, "lat": "49.75", "lng": "15.50", "emoji": "🇨🇿", "emojiU": "U+1F1E8 U+1F1FF", "flag": "1", "is_activated": "1"}, {"id": "60", "name": "Denmark", "code": "DK", "phone": "208", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Denmark\", \"cn\": \"丹麦\", \"de\": \"Dänemark\", \"es\": \"Dinamarca\", \"fa\": \"دانمارک\", \"fr\": \"Danemark\", \"hr\": \"<PERSON><PERSON>\", \"it\": \"<PERSON><PERSON><PERSON>\", \"ja\": \"デンマーク\", \"kr\": \"덴마크\", \"nl\": \"Denemarken\", \"pt\": \"Dinamarca\", \"tr\": \"Danimarka\", \"pt-BR\": \"Dinamarca\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Copenhagen\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "208", "iso3": "DNK", "nationality": "Danish", "capital": "Copenhagen", "tld": ".dk", "native": "Danmark", "region": "Europe", "currency": "DKK", "currency_name": "Danish krone", "currency_symbol": "<PERSON>r.", "wikiDataId": null, "lat": "56.00", "lng": "10.00", "emoji": "🇩🇰", "emojiU": "U+1F1E9 U+1F1F0", "flag": "1", "is_activated": "1"}, {"id": "61", "name": "Djibouti", "code": "DJ", "phone": "262", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Djibouti\", \"cn\": \"吉布提\", \"de\": \"Dschibuti\", \"es\": \"<PERSON><PERSON><PERSON>\", \"fa\": \"جیبوتی\", \"fr\": \"Djibouti\", \"hr\": \"Džibuti\", \"it\": \"Gib<PERSON>\", \"ja\": \"ジブチ\", \"kr\": \"지부티\", \"nl\": \"Djibouti\", \"pt\": \"Djibuti\", \"tr\": \"Cibuti\", \"pt-BR\": \"Djibuti\"}", "timezones": "[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Africa/Djibouti\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]", "numeric_code": "262", "iso3": "DJI", "nationality": "Djiboutian", "capital": "Djibouti", "tld": ".dj", "native": "Djibouti", "region": "Africa", "currency": "DJF", "currency_name": "Djiboutian franc", "currency_symbol": "Fdj", "wikiDataId": null, "lat": "11.50", "lng": "43.00", "emoji": "🇩🇯", "emojiU": "U+1F1E9 U+1F1EF", "flag": "1", "is_activated": "1"}, {"id": "62", "name": "Dominica", "code": "DM", "phone": "212", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"<PERSON><PERSON>\", \"cn\": \"多米尼加\", \"de\": \"<PERSON><PERSON>\", \"es\": \"<PERSON><PERSON>\", \"fa\": \"دومینیکا\", \"fr\": \"<PERSON>\", \"hr\": \"<PERSON>ini<PERSON>\", \"it\": \"<PERSON><PERSON>\", \"ja\": \"ドミニカ国\", \"kr\": \"도미니카 연방\", \"nl\": \"Dominic<PERSON>\", \"pt\": \"Dominic<PERSON>\", \"tr\": \"<PERSON><PERSON><PERSON>\", \"pt-BR\": \"Dominic<PERSON>\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Dominica\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "212", "iso3": "DMA", "nationality": "Dominican", "capital": "<PERSON><PERSON>", "tld": ".dm", "native": "Dominica", "region": "Americas", "currency": "XCD", "currency_name": "Eastern Caribbean dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "15.42", "lng": "-61.33", "emoji": "🇩🇲", "emojiU": "U+1F1E9 U+1F1F2", "flag": "1", "is_activated": "1"}, {"id": "63", "name": "Dominican Republic", "code": "DO", "phone": "214", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Dominican Republic\", \"cn\": \"多明尼加共和国\", \"de\": \"Dominikanische Republik\", \"es\": \"República Dominicana\", \"fa\": \"جمهوری دومینیکن\", \"fr\": \"République dominicaine\", \"hr\": \"Dominikanska Republika\", \"it\": \"Repubblica Dominicana\", \"ja\": \"ドミニカ共和国\", \"kr\": \"도미니카 공화국\", \"nl\": \"Dominicaanse Republiek\", \"pt\": \"República Dominicana\", \"tr\": \"Dominik Cumhuriyeti\", \"pt-BR\": \"República Dominicana\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Santo_Domingo\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "214", "iso3": "DOM", "nationality": "Dominican", "capital": "Santo Domingo", "tld": ".do", "native": "República Dominicana", "region": "Americas", "currency": "DOP", "currency_name": "Dominican peso", "currency_symbol": "$", "wikiDataId": null, "lat": "19.00", "lng": "-70.67", "emoji": "🇩🇴", "emojiU": "U+1F1E9 U+1F1F4", "flag": "1", "is_activated": "1"}, {"id": "64", "name": "Ecuador", "code": "EC", "phone": "218", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Ecuador\", \"cn\": \"厄瓜多尔\", \"de\": \"Ecuador\", \"es\": \"Ecuador\", \"fa\": \"اکوادور\", \"fr\": \"Équateur\", \"hr\": \"Ekvador\", \"it\": \"Ecuador\", \"ja\": \"エクアドル\", \"kr\": \"에콰도르\", \"nl\": \"Ecuador\", \"pt\": \"Equador\", \"tr\": \"Ekvator\", \"pt-BR\": \"Equador\"}", "timezones": "[{\"tzName\": \"Ecuador Time\", \"zoneName\": \"America/Guayaquil\", \"gmtOffset\": -18000, \"abbreviation\": \"ECT\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Galápagos Time\", \"zoneName\": \"Pacific/Galapagos\", \"gmtOffset\": -21600, \"abbreviation\": \"GALT\", \"gmtOffsetName\": \"UTC-06:00\"}]", "numeric_code": "218", "iso3": "ECU", "nationality": "Ecuadorian", "capital": "Quito", "tld": ".ec", "native": "Ecuador", "region": "Americas", "currency": "USD", "currency_name": "United States dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "-2.00", "lng": "-77.50", "emoji": "🇪🇨", "emojiU": "U+1F1EA U+1F1E8", "flag": "1", "is_activated": "1"}, {"id": "65", "name": "Egypt", "code": "EG", "phone": "02", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Egypt\", \"cn\": \"埃及\", \"de\": \"Ägypten\", \"es\": \"Egipto\", \"fa\": \"مصر\", \"fr\": \"Égypte\", \"hr\": \"Egipat\", \"it\": \"Egitto\", \"ja\": \"エジプト\", \"kr\": \"이집트\", \"nl\": \"Egypte\", \"pt\": \"Egipto\", \"tr\": \"<PERSON><PERSON>sır\", \"pt-BR\": \"Egito\"}", "timezones": "[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Africa/Cairo\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "818", "iso3": "EGY", "nationality": "Egyptian", "capital": "Cairo", "tld": ".eg", "native": "مصر‎", "region": "Africa", "currency": "EGP", "currency_name": "Egyptian pound", "currency_symbol": "ج.م", "wikiDataId": null, "lat": "27.00", "lng": "30.00", "emoji": "🇪🇬", "emojiU": "U+1F1EA U+1F1EC", "flag": "1", "is_activated": "1"}, {"id": "66", "name": "El Salvador", "code": "SV", "phone": "222", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"El Salvador\", \"cn\": \"萨尔瓦多\", \"de\": \"El Salvador\", \"es\": \"El Salvador\", \"fa\": \"السالوادور\", \"fr\": \"Salvador\", \"hr\": \"Salvador\", \"it\": \"El Salvador\", \"ja\": \"エルサルバドル\", \"kr\": \"엘살바도르\", \"nl\": \"El Salvador\", \"pt\": \"El Salvador\", \"tr\": \"El Salvador\", \"pt-BR\": \"El Salvador\"}", "timezones": "[{\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/El_Salvador\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}]", "numeric_code": "222", "iso3": "SLV", "nationality": "Salvadoran", "capital": "San Salvador", "tld": ".sv", "native": "El Salvador", "region": "Americas", "currency": "USD", "currency_name": "United States dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "13.83", "lng": "-88.92", "emoji": "🇸🇻", "emojiU": "U+1F1F8 U+1F1FB", "flag": "1", "is_activated": "1"}, {"id": "67", "name": "Equatorial Guinea", "code": "GQ", "phone": "226", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Equatorial Guinea\", \"cn\": \"赤道几内亚\", \"de\": \"Äquatorial-Guinea\", \"es\": \"Guinea Ecuatorial\", \"fa\": \"گینه استوایی\", \"fr\": \"Guinée-Équatoriale\", \"hr\": \"Ekvatorijalna Gvineja\", \"it\": \"Guinea Equatoriale\", \"ja\": \"赤道ギニア\", \"kr\": \"적도 기니\", \"nl\": \"Equatoriaal-Guinea\", \"pt\": \"Guiné Equatorial\", \"tr\": \"<PERSON>kvat<PERSON>\", \"pt-BR\": \"Guiné Equatorial\"}", "timezones": "[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Malabo\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "226", "iso3": "GNQ", "nationality": "Equatorial Guinean, Equatoguinean", "capital": "Malabo", "tld": ".gq", "native": "Guinea Ecuatorial", "region": "Africa", "currency": "XAF", "currency_name": "Central African CFA franc", "currency_symbol": "FCFA", "wikiDataId": null, "lat": "2.00", "lng": "10.00", "emoji": "🇬🇶", "emojiU": "U+1F1EC U+1F1F6", "flag": "1", "is_activated": "1"}, {"id": "68", "name": "Eritrea", "code": "ER", "phone": "232", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Eritrea\", \"cn\": \"厄立特里亚\", \"de\": \"Erit<PERSON>\", \"es\": \"<PERSON><PERSON><PERSON>\", \"fa\": \"اریتره\", \"fr\": \"Éryth<PERSON>e\", \"hr\": \"<PERSON><PERSON><PERSON><PERSON>\", \"it\": \"<PERSON><PERSON><PERSON>\", \"ja\": \"エリトリア\", \"kr\": \"에리트레아\", \"nl\": \"Eritrea\", \"pt\": \"E<PERSON>re<PERSON>\", \"tr\": \"<PERSON>ritre\", \"pt-BR\": \"Eritreia\"}", "timezones": "[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Africa/Asmara\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]", "numeric_code": "232", "iso3": "ERI", "nationality": "Eritrean", "capital": "Asmara", "tld": ".er", "native": "ኤርትራ", "region": "Africa", "currency": "ERN", "currency_name": "Eritrean nakfa", "currency_symbol": "Nfk", "wikiDataId": null, "lat": "15.00", "lng": "39.00", "emoji": "🇪🇷", "emojiU": "U+1F1EA U+1F1F7", "flag": "1", "is_activated": "1"}, {"id": "69", "name": "Estonia", "code": "EE", "phone": "233", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Estonia\", \"cn\": \"爱沙尼亚\", \"de\": \"Estland\", \"es\": \"Estonia\", \"fa\": \"استونی\", \"fr\": \"Estonie\", \"hr\": \"Estonija\", \"it\": \"Estonia\", \"ja\": \"エストニア\", \"kr\": \"에스토니아\", \"nl\": \"Estland\", \"pt\": \"Estónia\", \"tr\": \"Estonya\", \"pt-BR\": \"Estônia\"}", "timezones": "[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Tallinn\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "233", "iso3": "EST", "nationality": "Estonian", "capital": "Tallinn", "tld": ".ee", "native": "<PERSON><PERSON><PERSON>", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "59.00", "lng": "26.00", "emoji": "🇪🇪", "emojiU": "U+1F1EA U+1F1EA", "flag": "1", "is_activated": "1"}, {"id": "70", "name": "Ethiopia", "code": "ET", "phone": "231", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Ethiopia\", \"cn\": \"埃塞俄比亚\", \"de\": \"Äthiopien\", \"es\": \"Etiopía\", \"fa\": \"اتیوپی\", \"fr\": \"Éthiopie\", \"hr\": \"Etiopija\", \"it\": \"Etiop<PERSON>\", \"ja\": \"エチオピア\", \"kr\": \"에티오피아\", \"nl\": \"Ethiopië\", \"pt\": \"Etiópia\", \"tr\": \"<PERSON><PERSON><PERSON>py<PERSON>\", \"pt-BR\": \"Etiópia\"}", "timezones": "[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Africa/Addis_Ababa\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]", "numeric_code": "231", "iso3": "ETH", "nationality": "Ethiopian", "capital": "Addis A<PERSON>ba", "tld": ".et", "native": "ኢትዮጵያ", "region": "Africa", "currency": "ETB", "currency_name": "Ethiopian birr", "currency_symbol": "Nkf", "wikiDataId": null, "lat": "8.00", "lng": "38.00", "emoji": "🇪🇹", "emojiU": "U+1F1EA U+1F1F9", "flag": "1", "is_activated": "1"}, {"id": "71", "name": "Falkland Islands", "code": "FK", "phone": "238", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Falkland Islands\", \"cn\": \"福克兰群岛\", \"de\": \"Falklandinseln\", \"es\": \"Islas Malvinas\", \"fa\": \"جزایر فالکلند\", \"fr\": \"Îles Malouines\", \"hr\": \"Falklandski Otoci\", \"it\": \"Isole Falkland o Isole Malvine\", \"ja\": \"フォークランド（マルビナス）諸島\", \"kr\": \"포클랜드 제도\", \"nl\": \"Falklandeilanden [Islas Malvinas]\", \"pt\": \"Ilhas Falkland\", \"tr\": \"Falkland Adalari\", \"pt-BR\": \"<PERSON>has Malvina<PERSON>\"}", "timezones": "[{\"tzName\": \"Falkland Islands Summer Time\", \"zoneName\": \"Atlantic/Stanley\", \"gmtOffset\": -10800, \"abbreviation\": \"FKST\", \"gmtOffsetName\": \"UTC-03:00\"}]", "numeric_code": "238", "iso3": "FLK", "nationality": "Falkland Island", "capital": "<PERSON>", "tld": ".fk", "native": "Falkland Islands", "region": "Americas", "currency": "FKP", "currency_name": "Falkland Islands pound", "currency_symbol": "£", "wikiDataId": null, "lat": "-51.75", "lng": "-59.00", "emoji": "🇫🇰", "emojiU": "U+1F1EB U+1F1F0", "flag": "1", "is_activated": "1"}, {"id": "72", "name": "Faroe Islands", "code": "FO", "phone": "234", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Faroe Islands\", \"cn\": \"法罗群岛\", \"de\": \"Färöer-Inseln\", \"es\": \"Islas Faroe\", \"fa\": \"جزایر فارو\", \"fr\": \"Îles Féroé\", \"hr\": \"<PERSON><PERSON> Otoci\", \"it\": \"Isole Far Oer\", \"ja\": \"フェロー諸島\", \"kr\": \"페로 제도\", \"nl\": \"<PERSON>aeröer\", \"pt\": \"Ilhas Faroé\", \"tr\": \"Faroe Adalari\", \"pt-BR\": \"Ilhas Faroé\"}", "timezones": "[{\"tzName\": \"Western European Time\", \"zoneName\": \"Atlantic/Faroe\", \"gmtOffset\": 0, \"abbreviation\": \"WET\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "234", "iso3": "FRO", "nationality": "Faroese", "capital": "Torshavn", "tld": ".fo", "native": "<PERSON><PERSON><PERSON><PERSON>", "region": "Europe", "currency": "DKK", "currency_name": "Danish krone", "currency_symbol": "<PERSON>r.", "wikiDataId": null, "lat": "62.00", "lng": "-7.00", "emoji": "🇫🇴", "emojiU": "U+1F1EB U+1F1F4", "flag": "1", "is_activated": "1"}, {"id": "73", "name": "Fiji Islands", "code": "FJ", "phone": "242", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Fiji Islands\", \"cn\": \"斐济\", \"de\": \"<PERSON><PERSON><PERSON>\", \"es\": \"<PERSON>yi\", \"fa\": \"فیجی\", \"fr\": \"<PERSON>dji\", \"hr\": \"<PERSON><PERSON><PERSON>\", \"it\": \"<PERSON><PERSON>\", \"ja\": \"フィジー\", \"kr\": \"피지\", \"nl\": \"Fiji\", \"pt\": \"Fiji\", \"tr\": \"Fiji\", \"pt-BR\": \"Fiji\"}", "timezones": "[{\"tzName\": \"Fiji Time\", \"zoneName\": \"Pacific/Fiji\", \"gmtOffset\": 43200, \"abbreviation\": \"FJT\", \"gmtOffsetName\": \"UTC+12:00\"}]", "numeric_code": "242", "iso3": "FJI", "nationality": "Fijian", "capital": "<PERSON><PERSON>", "tld": ".fj", "native": "Fiji", "region": "Oceania", "currency": "FJD", "currency_name": "Fijian dollar", "currency_symbol": "FJ$", "wikiDataId": null, "lat": "-18.00", "lng": "175.00", "emoji": "🇫🇯", "emojiU": "U+1F1EB U+1F1EF", "flag": "1", "is_activated": "1"}, {"id": "74", "name": "Finland", "code": "FI", "phone": "246", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Finland\", \"cn\": \"芬兰\", \"de\": \"Finnland\", \"es\": \"Finlandia\", \"fa\": \"فنلاند\", \"fr\": \"Finland<PERSON>\", \"hr\": \"<PERSON><PERSON>\", \"it\": \"Finlandia\", \"ja\": \"フィンランド\", \"kr\": \"핀란드\", \"nl\": \"Finland\", \"pt\": \"Finlândia\", \"tr\": \"Finlandiya\", \"pt-BR\": \"Finlândia\"}", "timezones": "[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Helsinki\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "246", "iso3": "FIN", "nationality": "Finnish", "capital": "Helsinki", "tld": ".fi", "native": "<PERSON><PERSON>", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "64.00", "lng": "26.00", "emoji": "🇫🇮", "emojiU": "U+1F1EB U+1F1EE", "flag": "1", "is_activated": "1"}, {"id": "75", "name": "France", "code": "FR", "phone": "250", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"France\", \"cn\": \"法国\", \"de\": \"Frankreich\", \"es\": \"Francia\", \"fa\": \"فرانسه\", \"fr\": \"France\", \"hr\": \"Francuska\", \"it\": \"Francia\", \"ja\": \"フランス\", \"kr\": \"프랑스\", \"nl\": \"<PERSON>rijk\", \"pt\": \"<PERSON>an<PERSON>\", \"tr\": \"<PERSON>ans<PERSON>\", \"pt-BR\": \"<PERSON>an<PERSON>\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Paris\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "250", "iso3": "FRA", "nationality": "French", "capital": "Paris", "tld": ".fr", "native": "France", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "46.00", "lng": "2.00", "emoji": "🇫🇷", "emojiU": "U+1F1EB U+1F1F7", "flag": "1", "is_activated": "1"}, {"id": "76", "name": "French Guiana", "code": "GF", "phone": "254", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"French Guiana\", \"cn\": \"法属圭亚那\", \"de\": \"Französisch Guyana\", \"es\": \"Guayana Francesa\", \"fa\": \"گویان فرانسه\", \"fr\": \"Guayane\", \"hr\": \"Francuska Gvajana\", \"it\": \"Guyana francese\", \"ja\": \"フランス領ギアナ\", \"kr\": \"프랑스령 기아나\", \"nl\": \"Frans-Guyana\", \"pt\": \"Guiana Francesa\", \"tr\": \"Fransiz Guyanasi\", \"pt-BR\": \"Guiana Francesa\"}", "timezones": "[{\"tzName\": \"French Guiana Time\", \"zoneName\": \"America/Cayenne\", \"gmtOffset\": -10800, \"abbreviation\": \"GFT\", \"gmtOffsetName\": \"UTC-03:00\"}]", "numeric_code": "254", "iso3": "GUF", "nationality": "French Guianese", "capital": "Cayenne", "tld": ".gf", "native": "<PERSON>ane française", "region": "Americas", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "4.00", "lng": "-53.00", "emoji": "🇬🇫", "emojiU": "U+1F1EC U+1F1EB", "flag": "1", "is_activated": "1"}, {"id": "77", "name": "French Polynesia", "code": "PF", "phone": "258", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"French Polynesia\", \"cn\": \"法属波利尼西亚\", \"de\": \"Französisch-Polynesien\", \"es\": \"Polinesia Francesa\", \"fa\": \"پلی‌نزی فرانسه\", \"fr\": \"Polynésie française\", \"hr\": \"Francuska Polinezija\", \"it\": \"Polinesia Francese\", \"ja\": \"フランス領ポリネシア\", \"kr\": \"프랑스령 폴리네시아\", \"nl\": \"Frans-Polynesië\", \"pt\": \"Polinésia Francesa\", \"tr\": \"Fransiz Poline<PERSON>\", \"pt-BR\": \"Polinésia Francesa\"}", "timezones": "[{\"tzName\": \"Gambier Islands Time\", \"zoneName\": \"Pacific/Gambier\", \"gmtOffset\": -32400, \"abbreviation\": \"GAMT\", \"gmtOffsetName\": \"UTC-09:00\"}, {\"tzName\": \"Marquesas Islands Time\", \"zoneName\": \"Pacific/Marquesas\", \"gmtOffset\": -34200, \"abbreviation\": \"MART\", \"gmtOffsetName\": \"UTC-09:30\"}, {\"tzName\": \"Tahiti Time\", \"zoneName\": \"Pacific/Tahiti\", \"gmtOffset\": -36000, \"abbreviation\": \"TAHT\", \"gmtOffsetName\": \"UTC-10:00\"}]", "numeric_code": "258", "iso3": "PYF", "nationality": "French Polynesia", "capital": "<PERSON><PERSON><PERSON>", "tld": ".pf", "native": "Polynésie française", "region": "Oceania", "currency": "XPF", "currency_name": "CFP franc", "currency_symbol": "₣", "wikiDataId": null, "lat": "-15.00", "lng": "-140.00", "emoji": "🇵🇫", "emojiU": "U+1F1F5 U+1F1EB", "flag": "1", "is_activated": "1"}, {"id": "78", "name": "French Southern Territories", "code": "TF", "phone": "260", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"French Southern Territories\", \"cn\": \"法属南部领地\", \"de\": \"Französische Süd- und Antarktisgebiete\", \"es\": \"Tierras Australes y Antárticas Francesas\", \"fa\": \"سرزمین‌های جنوبی و جنوبگانی فرانسه\", \"fr\": \"Terres australes et antarctiques françaises\", \"hr\": \"Francuski južni i antarktički teritoriji\", \"it\": \"Territori Francesi del Sud\", \"ja\": \"フランス領南方・南極地域\", \"kr\": \"프랑스령 남방 및 남극\", \"nl\": \"Franse Gebieden in de zuidelijke Indische Oceaan\", \"pt\": \"Terras Austrais e Antárticas Francesas\", \"tr\": \"Fransiz Güney Topraklari\", \"pt-BR\": \"Terras Austrais e Antárticas Francesas\"}", "timezones": "[{\"tzName\": \"French Southern and Antarctic Time\", \"zoneName\": \"Indian/Kerguelen\", \"gmtOffset\": 18000, \"abbreviation\": \"TFT\", \"gmtOffsetName\": \"UTC+05:00\"}]", "numeric_code": "260", "iso3": "ATF", "nationality": "French Southern Territories", "capital": "Port-aux-Francais", "tld": ".tf", "native": "Territoire des Terres australes et antarctiques fr", "region": "Africa", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "-49.25", "lng": "69.17", "emoji": "🇹🇫", "emojiU": "U+1F1F9 U+1F1EB", "flag": "1", "is_activated": "1"}, {"id": "79", "name": "Gabon", "code": "GA", "phone": "266", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Gabon\", \"cn\": \"加蓬\", \"de\": \"<PERSON>abun\", \"es\": \"Gabón\", \"fa\": \"گابن\", \"fr\": \"Gabon\", \"hr\": \"Gabon\", \"it\": \"Gabon\", \"ja\": \"ガボン\", \"kr\": \"가봉\", \"nl\": \"Gabon\", \"pt\": \"Gab<PERSON>\", \"tr\": \"Gabon\", \"pt-BR\": \"Gab<PERSON>\"}", "timezones": "[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Libreville\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "266", "iso3": "GAB", "nationality": "Gabonese", "capital": "Libreville", "tld": ".ga", "native": "Gabon", "region": "Africa", "currency": "XAF", "currency_name": "Central African CFA franc", "currency_symbol": "FCFA", "wikiDataId": null, "lat": "-1.00", "lng": "11.75", "emoji": "🇬🇦", "emojiU": "U+1F1EC U+1F1E6", "flag": "1", "is_activated": "1"}, {"id": "80", "name": "Gambia The", "code": "GM", "phone": "270", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Gambia The\", \"cn\": \"冈比亚\", \"de\": \"Gambia\", \"es\": \"Gambia\", \"fa\": \"گامبیا\", \"fr\": \"Gambie\", \"hr\": \"Gambija\", \"it\": \"Gambia\", \"ja\": \"ガンビア\", \"kr\": \"감비아\", \"nl\": \"Gambia\", \"pt\": \"Gâmbia\", \"tr\": \"Gambiya\", \"pt-BR\": \"Gâmb<PERSON>\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Banjul\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "270", "iso3": "GMB", "nationality": "Gambian", "capital": "Banjul", "tld": ".gm", "native": "Gambia", "region": "Africa", "currency": "GMD", "currency_name": "Gambian dalasi", "currency_symbol": "D", "wikiDataId": null, "lat": "13.47", "lng": "-16.57", "emoji": "🇬🇲", "emojiU": "U+1F1EC U+1F1F2", "flag": "1", "is_activated": "1"}, {"id": "81", "name": "Georgia", "code": "GE", "phone": "268", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Georgia\", \"cn\": \"格鲁吉亚\", \"de\": \"Georgien\", \"es\": \"Georgia\", \"fa\": \"گرجستان\", \"fr\": \"Géorgie\", \"hr\": \"Gruzija\", \"it\": \"Georgia\", \"ja\": \"グルジア\", \"kr\": \"조지아\", \"nl\": \"Georg<PERSON><PERSON>\", \"pt\": \"Geórgia\", \"tr\": \"Gürcistan\", \"pt-BR\": \"Geórgia\"}", "timezones": "[{\"tzName\": \"Georgia Standard Time\", \"zoneName\": \"Asia/Tbilisi\", \"gmtOffset\": 14400, \"abbreviation\": \"GET\", \"gmtOffsetName\": \"UTC+04:00\"}]", "numeric_code": "268", "iso3": "GEO", "nationality": "Georgian", "capital": "Tbilisi", "tld": ".ge", "native": "საქართველო", "region": "Asia", "currency": "GEL", "currency_name": "Georgian lari", "currency_symbol": "ლ", "wikiDataId": null, "lat": "42.00", "lng": "43.50", "emoji": "🇬🇪", "emojiU": "U+1F1EC U+1F1EA", "flag": "1", "is_activated": "1"}, {"id": "82", "name": "Germany", "code": "DE", "phone": "276", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Germany\", \"cn\": \"德国\", \"de\": \"Deutschland\", \"es\": \"Alemania\", \"fa\": \"آلمان\", \"fr\": \"Allemagne\", \"hr\": \"Njemačka\", \"it\": \"Germania\", \"ja\": \"ドイツ\", \"kr\": \"독일\", \"nl\": \"Duitsland\", \"pt\": \"Alemanha\", \"tr\": \"Almanya\", \"pt-BR\": \"Alemanha\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Berlin\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}, {\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Busingen\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "276", "iso3": "DEU", "nationality": "German", "capital": "Berlin", "tld": ".de", "native": "Deutschland", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "51.00", "lng": "9.00", "emoji": "🇩🇪", "emojiU": "U+1F1E9 U+1F1EA", "flag": "1", "is_activated": "1"}, {"id": "83", "name": "Ghana", "code": "GH", "phone": "288", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Ghana\", \"cn\": \"加纳\", \"de\": \"Ghana\", \"es\": \"Ghana\", \"fa\": \"غنا\", \"fr\": \"Ghana\", \"hr\": \"<PERSON>ana\", \"it\": \"Ghana\", \"ja\": \"ガーナ\", \"kr\": \"가나\", \"nl\": \"Ghana\", \"pt\": \"<PERSON>ana\", \"tr\": \"<PERSON>ana\", \"pt-BR\": \"<PERSON><PERSON>\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Accra\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "288", "iso3": "GHA", "nationality": "Ghanaian", "capital": "Accra", "tld": ".gh", "native": "Ghana", "region": "Africa", "currency": "GHS", "currency_name": "Ghanaian cedi", "currency_symbol": "GH₵", "wikiDataId": null, "lat": "8.00", "lng": "-2.00", "emoji": "🇬🇭", "emojiU": "U+1F1EC U+1F1ED", "flag": "1", "is_activated": "1"}, {"id": "84", "name": "Gibraltar", "code": "GI", "phone": "292", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Gibraltar\", \"cn\": \"直布罗陀\", \"de\": \"Gibraltar\", \"es\": \"Gibraltar\", \"fa\": \"جبل‌طارق\", \"fr\": \"Gibraltar\", \"hr\": \"Gibraltar\", \"it\": \"Gibilterra\", \"ja\": \"ジブラルタル\", \"kr\": \"지브롤터\", \"nl\": \"Gibraltar\", \"pt\": \"Gibraltar\", \"tr\": \"Cebelitarik\", \"pt-BR\": \"Gibraltar\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Gibraltar\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "292", "iso3": "GIB", "nationality": "Gibraltar", "capital": "Gibraltar", "tld": ".gi", "native": "Gibraltar", "region": "Europe", "currency": "GIP", "currency_name": "Gibraltar pound", "currency_symbol": "£", "wikiDataId": null, "lat": "36.13", "lng": "-5.35", "emoji": "🇬🇮", "emojiU": "U+1F1EC U+1F1EE", "flag": "1", "is_activated": "1"}, {"id": "85", "name": "Greece", "code": "GR", "phone": "300", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Greece\", \"cn\": \"希腊\", \"de\": \"Griechenland\", \"es\": \"Grecia\", \"fa\": \"یونان\", \"fr\": \"<PERSON><PERSON><PERSON><PERSON>\", \"hr\": \"<PERSON>r<PERSON><PERSON>\", \"it\": \"<PERSON>recia\", \"ja\": \"ギリシャ\", \"kr\": \"그리스\", \"nl\": \"Griekenland\", \"pt\": \"Grécia\", \"tr\": \"Yunanistan\", \"pt-BR\": \"Grécia\"}", "timezones": "[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Athens\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "300", "iso3": "GRC", "nationality": "Greek, Hellenic", "capital": "Athens", "tld": ".gr", "native": "Ελλάδα", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "39.00", "lng": "22.00", "emoji": "🇬🇷", "emojiU": "U+1F1EC U+1F1F7", "flag": "1", "is_activated": "1"}, {"id": "86", "name": "Greenland", "code": "GL", "phone": "304", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Greenland\", \"cn\": \"格陵兰岛\", \"de\": \"Grönland\", \"es\": \"Groenlandia\", \"fa\": \"گرینلند\", \"fr\": \"Groenland\", \"hr\": \"Grenland\", \"it\": \"Groenlandia\", \"ja\": \"グリーンランド\", \"kr\": \"그린란드\", \"nl\": \"Groenland\", \"pt\": \"Gronelândia\", \"tr\": \"Grönland\", \"pt-BR\": \"Groelândia\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"America/Danmarkshavn\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}, {\"tzName\": \"West Greenland Time\", \"zoneName\": \"America/Nuuk\", \"gmtOffset\": -10800, \"abbreviation\": \"WGT\", \"gmtOffsetName\": \"UTC-03:00\"}, {\"tzName\": \"Eastern Greenland Time\", \"zoneName\": \"America/Scoresbysund\", \"gmtOffset\": -3600, \"abbreviation\": \"EGT\", \"gmtOffsetName\": \"UTC-01:00\"}, {\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Thule\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "304", "iso3": "GRL", "nationality": "Greenlandic", "capital": "<PERSON><PERSON><PERSON>", "tld": ".gl", "native": "Kalaallit Nunaat", "region": "Americas", "currency": "DKK", "currency_name": "Danish krone", "currency_symbol": "<PERSON>r.", "wikiDataId": null, "lat": "72.00", "lng": "-40.00", "emoji": "🇬🇱", "emojiU": "U+1F1EC U+1F1F1", "flag": "1", "is_activated": "1"}, {"id": "87", "name": "Grenada", "code": "GD", "phone": "308", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Grenada\", \"cn\": \"格林纳达\", \"de\": \"Grenada\", \"es\": \"Grenada\", \"fa\": \"گرنادا\", \"fr\": \"Grenade\", \"hr\": \"Grenada\", \"it\": \"Grenada\", \"ja\": \"グレナダ\", \"kr\": \"그레나다\", \"nl\": \"Grenada\", \"pt\": \"Granada\", \"tr\": \"Grenada\", \"pt-BR\": \"Granada\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Grenada\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "308", "iso3": "GRD", "nationality": "Grenadian", "capital": "St. Georges", "tld": ".gd", "native": "Grenada", "region": "Americas", "currency": "XCD", "currency_name": "Eastern Caribbean dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "12.12", "lng": "-61.67", "emoji": "🇬🇩", "emojiU": "U+1F1EC U+1F1E9", "flag": "1", "is_activated": "1"}, {"id": "88", "name": "Guadeloupe", "code": "GP", "phone": "312", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Guadeloupe\", \"cn\": \"瓜德罗普岛\", \"de\": \"Guadeloupe\", \"es\": \"<PERSON>\", \"fa\": \"جزیره گوادلوپ\", \"fr\": \"Guadeloupe\", \"hr\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"it\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"ja\": \"グアドループ\", \"kr\": \"과들루프\", \"nl\": \"Guadeloupe\", \"pt\": \"Guadalupe\", \"tr\": \"Guadeloupe\", \"pt-BR\": \"Guadalupe\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Guadeloupe\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "312", "iso3": "GLP", "nationality": "Guadeloupe", "capital": "Basse-Terre", "tld": ".gp", "native": "Guadeloupe", "region": "Americas", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "16.25", "lng": "-61.58", "emoji": "🇬🇵", "emojiU": "U+1F1EC U+1F1F5", "flag": "1", "is_activated": "1"}, {"id": "89", "name": "Guam", "code": "GU", "phone": "316", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Guam\", \"cn\": \"关岛\", \"de\": \"Guam\", \"es\": \"Guam\", \"fa\": \"گوام\", \"fr\": \"Guam\", \"hr\": \"Guam\", \"it\": \"Guam\", \"ja\": \"グアム\", \"kr\": \"괌\", \"nl\": \"Guam\", \"pt\": \"Guame\", \"tr\": \"Guam\", \"pt-BR\": \"Guam\"}", "timezones": "[{\"tzName\": \"Chamorro Standard Time\", \"zoneName\": \"Pacific/Guam\", \"gmtOffset\": 36000, \"abbreviation\": \"CHST\", \"gmtOffsetName\": \"UTC+10:00\"}]", "numeric_code": "316", "iso3": "GUM", "nationality": "Guamanian, Guambat", "capital": "<PERSON><PERSON><PERSON>", "tld": ".gu", "native": "Guam", "region": "Oceania", "currency": "USD", "currency_name": "US Dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "13.47", "lng": "144.78", "emoji": "🇬🇺", "emojiU": "U+1F1EC U+1F1FA", "flag": "1", "is_activated": "1"}, {"id": "90", "name": "Guatemala", "code": "GT", "phone": "320", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Guatemala\", \"cn\": \"危地马拉\", \"de\": \"Guatemala\", \"es\": \"Guatemala\", \"fa\": \"گواتمالا\", \"fr\": \"Guatemala\", \"hr\": \"Gvatemala\", \"it\": \"Guatemala\", \"ja\": \"グアテマラ\", \"kr\": \"과테말라\", \"nl\": \"Guatemala\", \"pt\": \"Guatemala\", \"tr\": \"Guatemala\", \"pt-BR\": \"Guatemala\"}", "timezones": "[{\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Guatemala\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}]", "numeric_code": "320", "iso3": "GTM", "nationality": "Guatemalan", "capital": "Guatemala City", "tld": ".gt", "native": "Guatemala", "region": "Americas", "currency": "GTQ", "currency_name": "Guatemalan quetzal", "currency_symbol": "Q", "wikiDataId": null, "lat": "15.50", "lng": "-90.25", "emoji": "🇬🇹", "emojiU": "U+1F1EC U+1F1F9", "flag": "1", "is_activated": "1"}, {"id": "91", "name": "Guernsey and Alderney", "code": "GG", "phone": "831", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Guernsey and Alderney\", \"cn\": \"根西岛\", \"de\": \"Guernsey\", \"es\": \"Guernsey\", \"fa\": \"گرنزی\", \"fr\": \"<PERSON><PERSON><PERSON>y\", \"hr\": \"Guernsey\", \"it\": \"Guernsey\", \"ja\": \"ガーンジー\", \"kr\": \"건지, 올더니\", \"nl\": \"Guernsey\", \"pt\": \"Guernsey\", \"tr\": \"Alderney\", \"pt-BR\": \"Guernsey\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Europe/Guernsey\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "831", "iso3": "GGY", "nationality": "Channel Island", "capital": "St Peter Port", "tld": ".gg", "native": "Guernsey", "region": "Europe", "currency": "GBP", "currency_name": "British pound", "currency_symbol": "£", "wikiDataId": null, "lat": "49.47", "lng": "-2.58", "emoji": "🇬🇬", "emojiU": "U+1F1EC U+1F1EC", "flag": "1", "is_activated": "1"}, {"id": "92", "name": "Guinea", "code": "GN", "phone": "324", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Guinea\", \"cn\": \"几内亚\", \"de\": \"Guinea\", \"es\": \"Guinea\", \"fa\": \"گینه\", \"fr\": \"Guin<PERSON>\", \"hr\": \"<PERSON><PERSON><PERSON><PERSON>\", \"it\": \"Guinea\", \"ja\": \"ギニア\", \"kr\": \"기니\", \"nl\": \"Guinee\", \"pt\": \"Guin<PERSON>\", \"tr\": \"G<PERSON>\", \"pt-BR\": \"Guin<PERSON>\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Conakry\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "324", "iso3": "GIN", "nationality": "Guinean", "capital": "Conakry", "tld": ".gn", "native": "Guinée", "region": "Africa", "currency": "GNF", "currency_name": "Guinean franc", "currency_symbol": "FG", "wikiDataId": null, "lat": "11.00", "lng": "-10.00", "emoji": "🇬🇳", "emojiU": "U+1F1EC U+1F1F3", "flag": "1", "is_activated": "1"}, {"id": "93", "name": "Guinea-Bissau", "code": "GW", "phone": "624", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Guinea-Bissau\", \"cn\": \"几内亚比绍\", \"de\": \"Guinea-Bissau\", \"es\": \"Guinea-Bisáu\", \"fa\": \"گینه بیسائو\", \"fr\": \"Guinée-Bissau\", \"hr\": \"<PERSON><PERSON>eja Bisau\", \"it\": \"Guinea-Bissau\", \"ja\": \"ギニアビサウ\", \"kr\": \"기니비사우\", \"nl\": \"Guinee-Bissau\", \"pt\": \"Guiné-Bissau\", \"tr\": \"Gine-bissau\", \"pt-BR\": \"Guiné-Bissau\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Bissau\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "624", "iso3": "GNB", "nationality": "Bissau-Guinean", "capital": "Bissau", "tld": ".gw", "native": "Guiné-Bissau", "region": "Africa", "currency": "XOF", "currency_name": "West African CFA franc", "currency_symbol": "CFA", "wikiDataId": null, "lat": "12.00", "lng": "-15.00", "emoji": "🇬🇼", "emojiU": "U+1F1EC U+1F1FC", "flag": "1", "is_activated": "1"}, {"id": "94", "name": "Guyana", "code": "GY", "phone": "328", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Guyana\", \"cn\": \"圭亚那\", \"de\": \"Guyana\", \"es\": \"Guyana\", \"fa\": \"گویان\", \"fr\": \"<PERSON>ane\", \"hr\": \"Gvajana\", \"it\": \"Guyana\", \"ja\": \"ガイアナ\", \"kr\": \"가이아나\", \"nl\": \"Guyana\", \"pt\": \"Guiana\", \"tr\": \"Guyana\", \"pt-BR\": \"Guiana\"}", "timezones": "[{\"tzName\": \"Guyana Time\", \"zoneName\": \"America/Guyana\", \"gmtOffset\": -14400, \"abbreviation\": \"GYT\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "328", "iso3": "GUY", "nationality": "Guyanese", "capital": "Georgetown", "tld": ".gy", "native": "Guyana", "region": "Americas", "currency": "GYD", "currency_name": "Guyanese dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "5.00", "lng": "-59.00", "emoji": "🇬🇾", "emojiU": "U+1F1EC U+1F1FE", "flag": "1", "is_activated": "1"}, {"id": "95", "name": "Haiti", "code": "HT", "phone": "332", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Haiti\", \"cn\": \"海地\", \"de\": \"Haiti\", \"es\": \"Haiti\", \"fa\": \"هائیتی\", \"fr\": \"<PERSON><PERSON><PERSON>\", \"hr\": \"Haiti\", \"it\": \"Haiti\", \"ja\": \"ハイチ\", \"kr\": \"아이티\", \"nl\": \"<PERSON><PERSON><PERSON>\", \"pt\": \"Haiti\", \"tr\": \"Haiti\", \"pt-BR\": \"Haiti\"}", "timezones": "[{\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Port-au-Prince\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}]", "numeric_code": "332", "iso3": "HTI", "nationality": "Haitian", "capital": "Port-au-Prince", "tld": ".ht", "native": "<PERSON><PERSON><PERSON>", "region": "Americas", "currency": "HTG", "currency_name": "Haitian gourde", "currency_symbol": "G", "wikiDataId": null, "lat": "19.00", "lng": "-72.42", "emoji": "🇭🇹", "emojiU": "U+1F1ED U+1F1F9", "flag": "1", "is_activated": "1"}, {"id": "96", "name": "Heard Island and McDonald Islands", "code": "HM", "phone": "334", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Heard Island and McDonald Islands\", \"cn\": \"赫德·唐纳岛及麦唐纳岛\", \"de\": \"Heard und die McDonaldinseln\", \"es\": \"<PERSON><PERSON> y <PERSON>\", \"fa\": \"جزیره هرد و جزایر مک‌دونالد\", \"fr\": \"Îles Heard-et-MacDonald\", \"hr\": \"Otok Heard i o<PERSON>\", \"it\": \"Isole Heard e McDonald\", \"ja\": \"ハード島とマクドナルド諸島\", \"kr\": \"허드 맥도날드 제도\", \"nl\": \"Heard- en McDonaldeilanden\", \"pt\": \"<PERSON><PERSON> e <PERSON>\", \"tr\": \"<PERSON> Adasi <PERSON> Mcdonald <PERSON>lari\", \"pt-BR\": \"<PERSON><PERSON> e <PERSON>\"}", "timezones": "[{\"tzName\": \"French Southern and Antarctic Time\", \"zoneName\": \"Indian/Kerguelen\", \"gmtOffset\": 18000, \"abbreviation\": \"TFT\", \"gmtOffsetName\": \"UTC+05:00\"}]", "numeric_code": "334", "iso3": "HMD", "nationality": "Heard Island or McDonald Islands", "capital": "", "tld": ".hm", "native": "Heard Island and McDonald Islands", "region": "", "currency": "AUD", "currency_name": "Australian dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "-53.10", "lng": "72.52", "emoji": "🇭🇲", "emojiU": "U+1F1ED U+1F1F2", "flag": "1", "is_activated": "1"}, {"id": "97", "name": "Vatican City State (Holy See)", "code": "VA", "phone": "336", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Vatican City State (Holy See)\", \"cn\": \"梵蒂冈\", \"de\": \"<PERSON><PERSON><PERSON> Stuhl\", \"es\": \"Santa Sede\", \"fa\": \"سریر مقدس\", \"fr\": \"voir Saint\", \"hr\": \"Sveta Stolica\", \"it\": \"Santa Sede\", \"ja\": \"聖座\", \"kr\": \"바티칸 시국\", \"nl\": \"<PERSON><PERSON><PERSON> Stoel\", \"pt\": \"Vaticano\", \"tr\": \"Vatikan\", \"pt-BR\": \"Vaticano\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Vatican\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "336", "iso3": "VAT", "nationality": "Vatican", "capital": "Vatican City", "tld": ".va", "native": "Vaticano", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "41.90", "lng": "12.45", "emoji": "🇻🇦", "emojiU": "U+1F1FB U+1F1E6", "flag": "1", "is_activated": "1"}, {"id": "98", "name": "Honduras", "code": "HN", "phone": "340", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Honduras\", \"cn\": \"洪都拉斯\", \"de\": \"Honduras\", \"es\": \"Honduras\", \"fa\": \"هندوراس\", \"fr\": \"Honduras\", \"hr\": \"Honduras\", \"it\": \"Honduras\", \"ja\": \"ホンジュラス\", \"kr\": \"온두라스\", \"nl\": \"Honduras\", \"pt\": \"Honduras\", \"tr\": \"Honduras\", \"pt-BR\": \"Honduras\"}", "timezones": "[{\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Tegucigalpa\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}]", "numeric_code": "340", "iso3": "HND", "nationality": "<PERSON><PERSON><PERSON>", "capital": "Tegucigalpa", "tld": ".hn", "native": "Honduras", "region": "Americas", "currency": "HNL", "currency_name": "<PERSON><PERSON><PERSON>", "currency_symbol": "L", "wikiDataId": null, "lat": "15.00", "lng": "-86.50", "emoji": "🇭🇳", "emojiU": "U+1F1ED U+1F1F3", "flag": "1", "is_activated": "1"}, {"id": "99", "name": "Hong Kong S.A.R.", "code": "HK", "phone": "344", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Hong Kong S.A.R.\", \"cn\": \"中国香港\", \"de\": \"Hong Kong\", \"es\": \"Hong Kong\", \"fa\": \"هنگ‌کنگ\", \"fr\": \"Hong Kong\", \"hr\": \"Hong Kong\", \"it\": \"Hong Kong\", \"ja\": \"香港\", \"kr\": \"홍콩\", \"nl\": \"Hongkong\", \"pt\": \"Hong Kong\", \"tr\": \"Hong Kong\", \"pt-BR\": \"Hong Kong\"}", "timezones": "[{\"tzName\": \"Hong Kong Time\", \"zoneName\": \"Asia/Hong_Kong\", \"gmtOffset\": 28800, \"abbreviation\": \"HKT\", \"gmtOffsetName\": \"UTC+08:00\"}]", "numeric_code": "344", "iso3": "HKG", "nationality": "Hong Kong, Hong Kongese", "capital": "Hong Kong", "tld": ".hk", "native": "香港", "region": "Asia", "currency": "HKD", "currency_name": "Hong Kong dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "22.25", "lng": "114.17", "emoji": "🇭🇰", "emojiU": "U+1F1ED U+1F1F0", "flag": "1", "is_activated": "1"}, {"id": "100", "name": "Hungary", "code": "HU", "phone": "348", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Hungary\", \"cn\": \"匈牙利\", \"de\": \"Ungarn\", \"es\": \"Hungr<PERSON>\", \"fa\": \"مجارستان\", \"fr\": \"<PERSON><PERSON>\", \"hr\": \"<PERSON><PERSON><PERSON><PERSON>\", \"it\": \"<PERSON><PERSON><PERSON>\", \"ja\": \"ハンガリー\", \"kr\": \"헝가리\", \"nl\": \"Hongarije\", \"pt\": \"Hungria\", \"tr\": \"Macaristan\", \"pt-BR\": \"Hungria\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Budapest\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "348", "iso3": "HUN", "nationality": "Hungarian, Ma<PERSON>ar", "capital": "Budapest", "tld": ".hu", "native": "Magyarország", "region": "Europe", "currency": "HUF", "currency_name": "Hungarian forint", "currency_symbol": "Ft", "wikiDataId": null, "lat": "47.00", "lng": "20.00", "emoji": "🇭🇺", "emojiU": "U+1F1ED U+1F1FA", "flag": "1", "is_activated": "1"}, {"id": "101", "name": "Iceland", "code": "IS", "phone": "352", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Iceland\", \"cn\": \"冰岛\", \"de\": \"Island\", \"es\": \"Islandia\", \"fa\": \"ایسلند\", \"fr\": \"Islande\", \"hr\": \"Island\", \"it\": \"Islanda\", \"ja\": \"アイスランド\", \"kr\": \"아이슬란드\", \"nl\": \"IJsland\", \"pt\": \"Islândia\", \"tr\": \"İzlanda\", \"pt-BR\": \"Islândia\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Atlantic/Reykjavik\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "352", "iso3": "ISL", "nationality": "Icelandic", "capital": "Reykjavik", "tld": ".is", "native": "Ísland", "region": "Europe", "currency": "ISK", "currency_name": "Icelandic króna", "currency_symbol": "kr", "wikiDataId": null, "lat": "65.00", "lng": "-18.00", "emoji": "🇮🇸", "emojiU": "U+1F1EE U+1F1F8", "flag": "1", "is_activated": "1"}, {"id": "102", "name": "India", "code": "IN", "phone": "356", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"India\", \"cn\": \"印度\", \"de\": \"Indien\", \"es\": \"India\", \"fa\": \"هند\", \"fr\": \"Inde\", \"hr\": \"Indija\", \"it\": \"India\", \"ja\": \"インド\", \"kr\": \"인도\", \"nl\": \"India\", \"pt\": \"Índia\", \"tr\": \"Hindistan\", \"pt-BR\": \"Índia\"}", "timezones": "[{\"tzName\": \"Indian Standard Time\", \"zoneName\": \"Asia/Kolkata\", \"gmtOffset\": 19800, \"abbreviation\": \"IST\", \"gmtOffsetName\": \"UTC+05:30\"}]", "numeric_code": "356", "iso3": "IND", "nationality": "Indian", "capital": "New Delhi", "tld": ".in", "native": "भारत", "region": "Asia", "currency": "INR", "currency_name": "Indian rupee", "currency_symbol": "₹", "wikiDataId": null, "lat": "20.00", "lng": "77.00", "emoji": "🇮🇳", "emojiU": "U+1F1EE U+1F1F3", "flag": "1", "is_activated": "1"}, {"id": "103", "name": "Indonesia", "code": "ID", "phone": "360", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Indonesia\", \"cn\": \"印度尼西亚\", \"de\": \"Indonesien\", \"es\": \"Indonesia\", \"fa\": \"اندونزی\", \"fr\": \"Indonésie\", \"hr\": \"Indonezija\", \"it\": \"Indonesia\", \"ja\": \"インドネシア\", \"kr\": \"인도네시아\", \"nl\": \"Indonesië\", \"pt\": \"Indonésia\", \"tr\": \"Endonezya\", \"pt-BR\": \"Indonésia\"}", "timezones": "[{\"tzName\": \"Western Indonesian Time\", \"zoneName\": \"Asia/Jakarta\", \"gmtOffset\": 25200, \"abbreviation\": \"WIB\", \"gmtOffsetName\": \"UTC+07:00\"}, {\"tzName\": \"Eastern Indonesian Time\", \"zoneName\": \"Asia/Jayapura\", \"gmtOffset\": 32400, \"abbreviation\": \"WIT\", \"gmtOffsetName\": \"UTC+09:00\"}, {\"tzName\": \"Central Indonesia Time\", \"zoneName\": \"Asia/Makassar\", \"gmtOffset\": 28800, \"abbreviation\": \"WITA\", \"gmtOffsetName\": \"UTC+08:00\"}, {\"tzName\": \"Western Indonesian Time\", \"zoneName\": \"Asia/Pontianak\", \"gmtOffset\": 25200, \"abbreviation\": \"WIB\", \"gmtOffsetName\": \"UTC+07:00\"}]", "numeric_code": "360", "iso3": "IDN", "nationality": "Indonesian", "capital": "Jakarta", "tld": ".id", "native": "Indonesia", "region": "Asia", "currency": "IDR", "currency_name": "Indonesian rupiah", "currency_symbol": "Rp", "wikiDataId": null, "lat": "-5.00", "lng": "120.00", "emoji": "🇮🇩", "emojiU": "U+1F1EE U+1F1E9", "flag": "1", "is_activated": "1"}, {"id": "104", "name": "Iran", "code": "IR", "phone": "364", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Iran\", \"cn\": \"伊朗\", \"de\": \"Iran\", \"es\": \"Iran\", \"fa\": \"ایران\", \"fr\": \"Iran\", \"hr\": \"Iran\", \"ja\": \"イラン・イスラム共和国\", \"kr\": \"이란\", \"nl\": \"Iran\", \"pt\": \"Ir<PERSON>\", \"tr\": \"İran\", \"pt-BR\": \"Ir<PERSON>\"}", "timezones": "[{\"tzName\": \"Iran Daylight Time\", \"zoneName\": \"Asia/Tehran\", \"gmtOffset\": 12600, \"abbreviation\": \"IRDT\", \"gmtOffsetName\": \"UTC+03:30\"}]", "numeric_code": "364", "iso3": "IRN", "nationality": "Iranian, Persian", "capital": "Tehran", "tld": ".ir", "native": "ایران", "region": "Asia", "currency": "IRR", "currency_name": "Iranian rial", "currency_symbol": "﷼", "wikiDataId": null, "lat": "32.00", "lng": "53.00", "emoji": "🇮🇷", "emojiU": "U+1F1EE U+1F1F7", "flag": "1", "is_activated": "1"}, {"id": "105", "name": "Iraq", "code": "IQ", "phone": "368", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Iraq\", \"cn\": \"伊拉克\", \"de\": \"<PERSON><PERSON>\", \"es\": \"Irak\", \"fa\": \"عراق\", \"fr\": \"Ira<PERSON>\", \"hr\": \"Irak\", \"it\": \"Iraq\", \"ja\": \"イラク\", \"kr\": \"이라크\", \"nl\": \"Irak\", \"pt\": \"Iraque\", \"tr\": \"Irak\", \"pt-BR\": \"Iraque\"}", "timezones": "[{\"tzName\": \"Arabia Standard Time\", \"zoneName\": \"Asia/Baghdad\", \"gmtOffset\": 10800, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC+03:00\"}]", "numeric_code": "368", "iso3": "IRQ", "nationality": "Iraqi", "capital": "Baghdad", "tld": ".iq", "native": "العراق", "region": "Asia", "currency": "IQD", "currency_name": "Iraqi dinar", "currency_symbol": "د.ع", "wikiDataId": null, "lat": "33.00", "lng": "44.00", "emoji": "🇮🇶", "emojiU": "U+1F1EE U+1F1F6", "flag": "1", "is_activated": "1"}, {"id": "106", "name": "Ireland", "code": "IE", "phone": "372", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Ireland\", \"cn\": \"爱尔兰\", \"de\": \"Irland\", \"es\": \"Irlanda\", \"fa\": \"ایرلند\", \"fr\": \"<PERSON>rland<PERSON>\", \"hr\": \"<PERSON><PERSON><PERSON>\", \"it\": \"<PERSON>rland<PERSON>\", \"ja\": \"アイルランド\", \"kr\": \"아일랜드\", \"nl\": \"Ierland\", \"pt\": \"Irlanda\", \"tr\": \"İrlanda\", \"pt-BR\": \"Irlanda\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Europe/Dublin\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "372", "iso3": "IRL", "nationality": "Irish", "capital": "Dublin", "tld": ".ie", "native": "Éire", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "53.00", "lng": "-8.00", "emoji": "🇮🇪", "emojiU": "U+1F1EE U+1F1EA", "flag": "1", "is_activated": "1"}, {"id": "107", "name": "Man (Isle of)", "code": "IM", "phone": "833", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Man (Isle of)\", \"cn\": \"马恩岛\", \"de\": \"Insel Man\", \"es\": \"Isla de Man\", \"fa\": \"جزیره من\", \"fr\": \"Île de Man\", \"hr\": \"Otok Man\", \"it\": \"Isola di Man\", \"ja\": \"マン島\", \"kr\": \"맨 섬\", \"nl\": \"Isle of Man\", \"pt\": \"Ilha de Man\", \"tr\": \"Man Adasi\", \"pt-BR\": \"Ilha de Man\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Europe/Isle_of_Man\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "833", "iso3": "IMN", "nationality": "Manx", "capital": "Douglas, Isle of Man", "tld": ".im", "native": "Isle of Man", "region": "Europe", "currency": "GBP", "currency_name": "British pound", "currency_symbol": "£", "wikiDataId": null, "lat": "54.25", "lng": "-4.50", "emoji": "🇮🇲", "emojiU": "U+1F1EE U+1F1F2", "flag": "1", "is_activated": "1"}, {"id": "108", "name": "Israel", "code": "IL", "phone": "376", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Israel\", \"cn\": \"以色列\", \"de\": \"Israel\", \"es\": \"Israel\", \"fa\": \"اسرائیل\", \"fr\": \"<PERSON><PERSON><PERSON>\", \"hr\": \"<PERSON><PERSON><PERSON><PERSON>\", \"it\": \"Israele\", \"ja\": \"イスラエル\", \"kr\": \"이스라엘\", \"nl\": \"<PERSON>ra<PERSON>\", \"pt\": \"Israel\", \"tr\": \"İsrail\", \"pt-BR\": \"Israel\"}", "timezones": "[{\"tzName\": \"Israel Standard Time\", \"zoneName\": \"Asia/Jerusalem\", \"gmtOffset\": 7200, \"abbreviation\": \"IST\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "376", "iso3": "ISR", "nationality": "Israeli", "capital": "Jerusalem", "tld": ".il", "native": "יִשְׂרָאֵל", "region": "Asia", "currency": "ILS", "currency_name": "Israeli new shekel", "currency_symbol": "₪", "wikiDataId": null, "lat": "31.50", "lng": "34.75", "emoji": "🇮🇱", "emojiU": "U+1F1EE U+1F1F1", "flag": "1", "is_activated": "1"}, {"id": "109", "name": "Italy", "code": "IT", "phone": "380", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Italy\", \"cn\": \"意大利\", \"de\": \"Italien\", \"es\": \"Italia\", \"fa\": \"ایتالیا\", \"fr\": \"Italie\", \"hr\": \"Italija\", \"it\": \"Italia\", \"ja\": \"イタリア\", \"kr\": \"이탈리아\", \"nl\": \"Italië\", \"pt\": \"It<PERSON>lia\", \"tr\": \"<PERSON>talya\", \"pt-BR\": \"Itália\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Rome\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "380", "iso3": "ITA", "nationality": "Italian", "capital": "Rome", "tld": ".it", "native": "Italia", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "42.83", "lng": "12.83", "emoji": "🇮🇹", "emojiU": "U+1F1EE U+1F1F9", "flag": "1", "is_activated": "1"}, {"id": "110", "name": "Jamaica", "code": "JM", "phone": "388", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Jamaica\", \"cn\": \"牙买加\", \"de\": \"<PERSON>ai<PERSON>\", \"es\": \"Jamaica\", \"fa\": \"جامائیکا\", \"fr\": \"Jamaï<PERSON>\", \"hr\": \"<PERSON><PERSON><PERSON>\", \"it\": \"<PERSON><PERSON><PERSON><PERSON>\", \"ja\": \"ジャマイカ\", \"kr\": \"자메이카\", \"nl\": \"Jamaica\", \"pt\": \"Jamaica\", \"tr\": \"Jamaika\", \"pt-BR\": \"Jamaica\"}", "timezones": "[{\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Jamaica\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}]", "numeric_code": "388", "iso3": "JAM", "nationality": "Jamaican", "capital": "Kingston", "tld": ".jm", "native": "Jamaica", "region": "Americas", "currency": "JMD", "currency_name": "Jamaican dollar", "currency_symbol": "J$", "wikiDataId": null, "lat": "18.25", "lng": "-77.50", "emoji": "🇯🇲", "emojiU": "U+1F1EF U+1F1F2", "flag": "1", "is_activated": "1"}, {"id": "111", "name": "Japan", "code": "JP", "phone": "392", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Japan\", \"cn\": \"日本\", \"de\": \"Japan\", \"es\": \"Japón\", \"fa\": \"ژاپن\", \"fr\": \"Japon\", \"hr\": \"Japan\", \"it\": \"Giappone\", \"ja\": \"日本\", \"kr\": \"일본\", \"nl\": \"Japan\", \"pt\": \"<PERSON><PERSON><PERSON>\", \"tr\": \"<PERSON>apon<PERSON>\", \"pt-BR\": \"<PERSON><PERSON><PERSON>\"}", "timezones": "[{\"tzName\": \"Japan Standard Time\", \"zoneName\": \"Asia/Tokyo\", \"gmtOffset\": 32400, \"abbreviation\": \"JST\", \"gmtOffsetName\": \"UTC+09:00\"}]", "numeric_code": "392", "iso3": "JPN", "nationality": "Japanese", "capital": "Tokyo", "tld": ".jp", "native": "日本", "region": "Asia", "currency": "JPY", "currency_name": "Japanese yen", "currency_symbol": "¥", "wikiDataId": null, "lat": "36.00", "lng": "138.00", "emoji": "🇯🇵", "emojiU": "U+1F1EF U+1F1F5", "flag": "1", "is_activated": "1"}, {"id": "112", "name": "Jersey", "code": "JE", "phone": "832", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Jersey\", \"cn\": \"泽西岛\", \"de\": \"Jersey\", \"es\": \"Jersey\", \"fa\": \"جرزی\", \"fr\": \"Jersey\", \"hr\": \"Jersey\", \"it\": \"Isola di Jersey\", \"ja\": \"ジャージー\", \"kr\": \"저지 섬\", \"nl\": \"Jersey\", \"pt\": \"Jersey\", \"tr\": \"Jersey\", \"pt-BR\": \"Jersey\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Europe/Jersey\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "832", "iso3": "JEY", "nationality": "Channel Island", "capital": "Saint Helier", "tld": ".je", "native": "Jersey", "region": "Europe", "currency": "GBP", "currency_name": "British pound", "currency_symbol": "£", "wikiDataId": null, "lat": "49.25", "lng": "-2.17", "emoji": "🇯🇪", "emojiU": "U+1F1EF U+1F1EA", "flag": "1", "is_activated": "1"}, {"id": "113", "name": "Jordan", "code": "JO", "phone": "400", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"<PERSON>\", \"cn\": \"约旦\", \"de\": \"Jordanien\", \"es\": \"Jordan<PERSON>\", \"fa\": \"اردن\", \"fr\": \"<PERSON><PERSON>\", \"hr\": \"Jordan\", \"it\": \"Giordania\", \"ja\": \"ヨルダン\", \"kr\": \"요르단\", \"nl\": \"<PERSON><PERSON><PERSON>\", \"pt\": \"Jordânia\", \"tr\": \"Ürdün\", \"pt-BR\": \"Jordânia\"}", "timezones": "[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Asia/Amman\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "400", "iso3": "JOR", "nationality": "<PERSON><PERSON>", "capital": "Amman", "tld": ".jo", "native": "الأردن", "region": "Asia", "currency": "JOD", "currency_name": "Jordanian dinar", "currency_symbol": "ا.د", "wikiDataId": null, "lat": "31.00", "lng": "36.00", "emoji": "🇯🇴", "emojiU": "U+1F1EF U+1F1F4", "flag": "1", "is_activated": "1"}, {"id": "114", "name": "Kazakhstan", "code": "KZ", "phone": "398", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Kazakhstan\", \"cn\": \"哈萨克斯坦\", \"de\": \"Kasachstan\", \"es\": \"Kazajistán\", \"fa\": \"قزاقستان\", \"fr\": \"Kazakhstan\", \"hr\": \"Kazahstan\", \"it\": \"Kazakistan\", \"ja\": \"カザフスタン\", \"kr\": \"카자흐스탄\", \"nl\": \"Kazachstan\", \"pt\": \"Cazaquistão\", \"tr\": \"Kazakistan\", \"pt-BR\": \"Cazaquistão\"}", "timezones": "[{\"tzName\": \"Alma-Ata Time[1\", \"zoneName\": \"Asia/Almaty\", \"gmtOffset\": 21600, \"abbreviation\": \"ALMT\", \"gmtOffsetName\": \"UTC+06:00\"}, {\"tzName\": \"Aqtobe Time\", \"zoneName\": \"Asia/Aqtau\", \"gmtOffset\": 18000, \"abbreviation\": \"AQTT\", \"gmtOffsetName\": \"UTC+05:00\"}, {\"tzName\": \"Aqtobe Time\", \"zoneName\": \"Asia/Aqtobe\", \"gmtOffset\": 18000, \"abbreviation\": \"AQTT\", \"gmtOffsetName\": \"UTC+05:00\"}, {\"tzName\": \"Moscow Daylight Time+1\", \"zoneName\": \"Asia/Atyrau\", \"gmtOffset\": 18000, \"abbreviation\": \"MSD+1\", \"gmtOffsetName\": \"UTC+05:00\"}, {\"tzName\": \"Oral Time\", \"zoneName\": \"Asia/Oral\", \"gmtOffset\": 18000, \"abbreviation\": \"ORAT\", \"gmtOffsetName\": \"UTC+05:00\"}, {\"tzName\": \"Qyzylorda Summer Time\", \"zoneName\": \"Asia/Qostanay\", \"gmtOffset\": 21600, \"abbreviation\": \"QYZST\", \"gmtOffsetName\": \"UTC+06:00\"}, {\"tzName\": \"Qyzylorda Summer Time\", \"zoneName\": \"Asia/Qyzylorda\", \"gmtOffset\": 18000, \"abbreviation\": \"QYZT\", \"gmtOffsetName\": \"UTC+05:00\"}]", "numeric_code": "398", "iso3": "KAZ", "nationality": "Kazakhstani, Kazakh", "capital": "Astana", "tld": ".kz", "native": "Қазақстан", "region": "Asia", "currency": "KZT", "currency_name": "Kazakhstani tenge", "currency_symbol": "лв", "wikiDataId": null, "lat": "48.00", "lng": "68.00", "emoji": "🇰🇿", "emojiU": "U+1F1F0 U+1F1FF", "flag": "1", "is_activated": "1"}, {"id": "115", "name": "Kenya", "code": "KE", "phone": "404", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Kenya\", \"cn\": \"肯尼亚\", \"de\": \"<PERSON><PERSON>\", \"es\": \"<PERSON><PERSON>\", \"fa\": \"کنیا\", \"fr\": \"Kenya\", \"hr\": \"<PERSON><PERSON>\", \"it\": \"Kenya\", \"ja\": \"ケニア\", \"kr\": \"케냐\", \"nl\": \"<PERSON><PERSON>\", \"pt\": \"Quénia\", \"tr\": \"Kenya\", \"pt-BR\": \"Quênia\"}", "timezones": "[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Africa/Nairobi\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]", "numeric_code": "404", "iso3": "KEN", "nationality": "Kenyan", "capital": "Nairobi", "tld": ".ke", "native": "Kenya", "region": "Africa", "currency": "KES", "currency_name": "Kenyan shilling", "currency_symbol": "KSh", "wikiDataId": null, "lat": "1.00", "lng": "38.00", "emoji": "🇰🇪", "emojiU": "U+1F1F0 U+1F1EA", "flag": "1", "is_activated": "1"}, {"id": "116", "name": "Kiribati", "code": "KI", "phone": "296", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"<PERSON><PERSON><PERSON>i\", \"cn\": \"基里巴斯\", \"de\": \"<PERSON><PERSON><PERSON><PERSON>\", \"es\": \"<PERSON><PERSON><PERSON><PERSON>\", \"fa\": \"کیریباتی\", \"fr\": \"<PERSON><PERSON><PERSON>i\", \"hr\": \"<PERSON><PERSON><PERSON><PERSON>\", \"it\": \"<PERSON><PERSON><PERSON><PERSON>\", \"ja\": \"キリバス\", \"kr\": \"키리바시\", \"nl\": \"Kiribati\", \"pt\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\", \"tr\": \"Ki<PERSON><PERSON>i\", \"pt-BR\": \"Kiribati\"}", "timezones": "[{\"tzName\": \"Phoenix Island Time\", \"zoneName\": \"Pacific/Enderbury\", \"gmtOffset\": 46800, \"abbreviation\": \"PHOT\", \"gmtOffsetName\": \"UTC+13:00\"}, {\"tzName\": \"Line Islands Time\", \"zoneName\": \"Pacific/Kiritimati\", \"gmtOffset\": 50400, \"abbreviation\": \"LINT\", \"gmtOffsetName\": \"UTC+14:00\"}, {\"tzName\": \"Gilbert Island Time\", \"zoneName\": \"Pacific/Tarawa\", \"gmtOffset\": 43200, \"abbreviation\": \"GILT\", \"gmtOffsetName\": \"UTC+12:00\"}]", "numeric_code": "296", "iso3": "KIR", "nationality": "I-Kiribati", "capital": "<PERSON><PERSON>", "tld": ".ki", "native": "Kiribati", "region": "Oceania", "currency": "AUD", "currency_name": "Australian dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "1.42", "lng": "173.00", "emoji": "🇰🇮", "emojiU": "U+1F1F0 U+1F1EE", "flag": "1", "is_activated": "1"}, {"id": "117", "name": "North Korea", "code": "KP", "phone": "408", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"North Korea\", \"cn\": \"朝鲜\", \"de\": \"Nordkorea\", \"es\": \"Corea del Norte\", \"fa\": \"کره جنوبی\", \"fr\": \"Corée du Nord\", \"hr\": \"Sjeverna Koreja\", \"it\": \"Corea del Nord\", \"ja\": \"朝鮮民主主義人民共和国\", \"kr\": \"조선민주주의인민공화국\", \"nl\": \"Noord-Korea\", \"pt\": \"Coreia do Norte\", \"tr\": \"Kuzey Kore\", \"pt-BR\": \"Coreia do Norte\"}", "timezones": "[{\"tzName\": \"Korea Standard Time\", \"zoneName\": \"Asia/Pyongyang\", \"gmtOffset\": 32400, \"abbreviation\": \"KST\", \"gmtOffsetName\": \"UTC+09:00\"}]", "numeric_code": "408", "iso3": "PRK", "nationality": "North Korean", "capital": "Pyongyang", "tld": ".kp", "native": "북한", "region": "Asia", "currency": "KPW", "currency_name": "North Korean Won", "currency_symbol": "₩", "wikiDataId": null, "lat": "40.00", "lng": "127.00", "emoji": "🇰🇵", "emojiU": "U+1F1F0 U+1F1F5", "flag": "1", "is_activated": "1"}, {"id": "118", "name": "South Korea", "code": "KR", "phone": "410", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"South Korea\", \"cn\": \"韩国\", \"de\": \"<PERSON>üdkor<PERSON>\", \"es\": \"Corea del Sur\", \"fa\": \"کره شمالی\", \"fr\": \"Corée du Sud\", \"hr\": \"Južna Koreja\", \"it\": \"Corea del Sud\", \"ja\": \"大韓民国\", \"kr\": \"대한민국\", \"nl\": \"Zuid-Korea\", \"pt\": \"Coreia do Sul\", \"tr\": \"<PERSON><PERSON>ney Kore\", \"pt-BR\": \"Coreia do Sul\"}", "timezones": "[{\"tzName\": \"Korea Standard Time\", \"zoneName\": \"Asia/Seoul\", \"gmtOffset\": 32400, \"abbreviation\": \"KST\", \"gmtOffsetName\": \"UTC+09:00\"}]", "numeric_code": "410", "iso3": "KOR", "nationality": "South Korean", "capital": "Seoul", "tld": ".kr", "native": "대한민국", "region": "Asia", "currency": "KRW", "currency_name": "Won", "currency_symbol": "₩", "wikiDataId": null, "lat": "37.00", "lng": "127.50", "emoji": "🇰🇷", "emojiU": "U+1F1F0 U+1F1F7", "flag": "1", "is_activated": "1"}, {"id": "119", "name": "Kuwait", "code": "KW", "phone": "414", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Kuwait\", \"cn\": \"科威特\", \"de\": \"Kuwait\", \"es\": \"Kuwait\", \"fa\": \"کویت\", \"fr\": \"<PERSON>weït\", \"hr\": \"<PERSON>vajt\", \"it\": \"Kuwait\", \"ja\": \"クウェート\", \"kr\": \"쿠웨이트\", \"nl\": \"<PERSON>eweit\", \"pt\": \"Kuwait\", \"tr\": \"Kuveyt\", \"pt-BR\": \"Kuwait\"}", "timezones": "[{\"tzName\": \"Arabia Standard Time\", \"zoneName\": \"Asia/Kuwait\", \"gmtOffset\": 10800, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC+03:00\"}]", "numeric_code": "414", "iso3": "KWT", "nationality": "Kuwaiti", "capital": "Kuwait City", "tld": ".kw", "native": "الكويت", "region": "Asia", "currency": "KWD", "currency_name": "Kuwaiti dinar", "currency_symbol": "ك.د", "wikiDataId": null, "lat": "29.50", "lng": "45.75", "emoji": "🇰🇼", "emojiU": "U+1F1F0 U+1F1FC", "flag": "1", "is_activated": "1"}, {"id": "120", "name": "Kyrgyzstan", "code": "KG", "phone": "417", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Kyrgyzstan\", \"cn\": \"吉尔吉斯斯坦\", \"de\": \"Kirgisistan\", \"es\": \"Kirguizistán\", \"fa\": \"قرقیزستان\", \"fr\": \"Kirghizistan\", \"hr\": \"Kirgistan\", \"it\": \"Kirghizistan\", \"ja\": \"キルギス\", \"kr\": \"키르기스스탄\", \"nl\": \"Kirgizië\", \"pt\": \"Quirguizistão\", \"tr\": \"Kirgizistan\", \"pt-BR\": \"Quirguistão\"}", "timezones": "[{\"tzName\": \"Kyrgyzstan Time\", \"zoneName\": \"Asia/Bishkek\", \"gmtOffset\": 21600, \"abbreviation\": \"KGT\", \"gmtOffsetName\": \"UTC+06:00\"}]", "numeric_code": "417", "iso3": "KGZ", "nationality": "Kyrgyzstani, Kyrgyz, Kirgiz, Kirghiz", "capital": "Bishkek", "tld": ".kg", "native": "Кыргызстан", "region": "Asia", "currency": "KGS", "currency_name": "Kyrgyzstani som", "currency_symbol": "лв", "wikiDataId": null, "lat": "41.00", "lng": "75.00", "emoji": "🇰🇬", "emojiU": "U+1F1F0 U+1F1EC", "flag": "1", "is_activated": "1"}, {"id": "121", "name": "Laos", "code": "LA", "phone": "418", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Laos\", \"cn\": \"寮人民民主共和国\", \"de\": \"Laos\", \"es\": \"Laos\", \"fa\": \"لائوس\", \"fr\": \"Laos\", \"hr\": \"Laos\", \"it\": \"Laos\", \"ja\": \"ラオス人民民主共和国\", \"kr\": \"라오스\", \"nl\": \"Laos\", \"pt\": \"Laos\", \"tr\": \"Laos\", \"pt-BR\": \"Laos\"}", "timezones": "[{\"tzName\": \"Indochina Time\", \"zoneName\": \"Asia/Vientiane\", \"gmtOffset\": 25200, \"abbreviation\": \"ICT\", \"gmtOffsetName\": \"UTC+07:00\"}]", "numeric_code": "418", "iso3": "LAO", "nationality": "Lao, Laotian", "capital": "Vientiane", "tld": ".la", "native": "ສປປລາວ", "region": "Asia", "currency": "LAK", "currency_name": "Lao kip", "currency_symbol": "₭", "wikiDataId": null, "lat": "18.00", "lng": "105.00", "emoji": "🇱🇦", "emojiU": "U+1F1F1 U+1F1E6", "flag": "1", "is_activated": "1"}, {"id": "122", "name": "Latvia", "code": "LV", "phone": "428", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Latvia\", \"cn\": \"拉脱维亚\", \"de\": \"Lettland\", \"es\": \"Letonia\", \"fa\": \"لتونی\", \"fr\": \"Lettonie\", \"hr\": \"Latvija\", \"it\": \"<PERSON>ton<PERSON>\", \"ja\": \"ラトビア\", \"kr\": \"라트비아\", \"nl\": \"Letland\", \"pt\": \"<PERSON><PERSON><PERSON>\", \"tr\": \"Letonya\", \"pt-BR\": \"Letônia\"}", "timezones": "[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Riga\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "428", "iso3": "LVA", "nationality": "Latvian", "capital": "Riga", "tld": ".lv", "native": "Latvija", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "57.00", "lng": "25.00", "emoji": "🇱🇻", "emojiU": "U+1F1F1 U+1F1FB", "flag": "1", "is_activated": "1"}, {"id": "123", "name": "Lebanon", "code": "LB", "phone": "422", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Lebanon\", \"cn\": \"黎巴嫩\", \"de\": \"Libanon\", \"es\": \"Líbano\", \"fa\": \"لبنان\", \"fr\": \"Liban\", \"hr\": \"Libanon\", \"it\": \"Libano\", \"ja\": \"レバノン\", \"kr\": \"레바논\", \"nl\": \"Libanon\", \"pt\": \"Líbano\", \"tr\": \"<PERSON>ü<PERSON>nan\", \"pt-BR\": \"Líbano\"}", "timezones": "[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Asia/Beirut\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "422", "iso3": "LBN", "nationality": "Lebanese", "capital": "Beirut", "tld": ".lb", "native": "لبنان", "region": "Asia", "currency": "LBP", "currency_name": "Lebanese pound", "currency_symbol": "£", "wikiDataId": null, "lat": "33.83", "lng": "35.83", "emoji": "🇱🇧", "emojiU": "U+1F1F1 U+1F1E7", "flag": "1", "is_activated": "1"}, {"id": "124", "name": "Lesotho", "code": "LS", "phone": "426", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Lesotho\", \"cn\": \"莱索托\", \"de\": \"Lesotho\", \"es\": \"Lesotho\", \"fa\": \"لسوتو\", \"fr\": \"Lesotho\", \"hr\": \"<PERSON><PERSON>\", \"it\": \"Lesotho\", \"ja\": \"レソト\", \"kr\": \"레소토\", \"nl\": \"Lesotho\", \"pt\": \"Lesoto\", \"tr\": \"Lesotho\", \"pt-BR\": \"<PERSON><PERSON>\"}", "timezones": "[{\"tzName\": \"South African Standard Time\", \"zoneName\": \"Africa/Maseru\", \"gmtOffset\": 7200, \"abbreviation\": \"SAST\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "426", "iso3": "LSO", "nationality": "Basotho", "capital": "Maseru", "tld": ".ls", "native": "Lesotho", "region": "Africa", "currency": "LSL", "currency_name": "Lesotho loti", "currency_symbol": "L", "wikiDataId": null, "lat": "-29.50", "lng": "28.50", "emoji": "🇱🇸", "emojiU": "U+1F1F1 U+1F1F8", "flag": "1", "is_activated": "1"}, {"id": "125", "name": "Liberia", "code": "LR", "phone": "430", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Liberia\", \"cn\": \"利比里亚\", \"de\": \"Liberia\", \"es\": \"Liberia\", \"fa\": \"لیبریا\", \"fr\": \"Liberia\", \"hr\": \"Liberija\", \"it\": \"Liberia\", \"ja\": \"リベリア\", \"kr\": \"라이베리아\", \"nl\": \"Liberia\", \"pt\": \"Libéria\", \"tr\": \"Liberya\", \"pt-BR\": \"Libéria\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Monrovia\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "430", "iso3": "LBR", "nationality": "Liberian", "capital": "Monrovia", "tld": ".lr", "native": "Liberia", "region": "Africa", "currency": "LRD", "currency_name": "Liberian dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "6.50", "lng": "-9.50", "emoji": "🇱🇷", "emojiU": "U+1F1F1 U+1F1F7", "flag": "1", "is_activated": "1"}, {"id": "126", "name": "Libya", "code": "LY", "phone": "434", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Libya\", \"cn\": \"利比亚\", \"de\": \"Libyen\", \"es\": \"Libia\", \"fa\": \"لیبی\", \"fr\": \"<PERSON>bye\", \"hr\": \"Libija\", \"it\": \"Libia\", \"ja\": \"リビア\", \"kr\": \"리비아\", \"nl\": \"Libië\", \"pt\": \"<PERSON>íbia\", \"tr\": \"Libya\", \"pt-BR\": \"<PERSON>íbia\"}", "timezones": "[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Africa/Tripoli\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "434", "iso3": "LBY", "nationality": "Libyan", "capital": "Tripolis", "tld": ".ly", "native": "‏ليبيا", "region": "Africa", "currency": "LYD", "currency_name": "Libyan dinar", "currency_symbol": "د.ل", "wikiDataId": null, "lat": "25.00", "lng": "17.00", "emoji": "🇱🇾", "emojiU": "U+1F1F1 U+1F1FE", "flag": "1", "is_activated": "1"}, {"id": "127", "name": "Liechtenstein", "code": "LI", "phone": "438", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Liechtenstein\", \"cn\": \"列支敦士登\", \"de\": \"Liechtenstein\", \"es\": \"Liechtenstein\", \"fa\": \"لیختن‌اشتاین\", \"fr\": \"Liechtenstein\", \"hr\": \"Lihtenštajn\", \"it\": \"Liechtenstein\", \"ja\": \"リヒテンシュタイン\", \"kr\": \"리히텐슈타인\", \"nl\": \"Liechtenstein\", \"pt\": \"Listenstaine\", \"tr\": \"Lihtenştayn\", \"pt-BR\": \"Liechtenstein\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Vaduz\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "438", "iso3": "LIE", "nationality": "Liechtenstein", "capital": "Vaduz", "tld": ".li", "native": "Liechtenstein", "region": "Europe", "currency": "CHF", "currency_name": "Swiss franc", "currency_symbol": "CHf", "wikiDataId": null, "lat": "47.27", "lng": "9.53", "emoji": "🇱🇮", "emojiU": "U+1F1F1 U+1F1EE", "flag": "1", "is_activated": "1"}, {"id": "128", "name": "Lithuania", "code": "LT", "phone": "440", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Lithuania\", \"cn\": \"立陶宛\", \"de\": \"<PERSON><PERSON><PERSON>\", \"es\": \"Lituania\", \"fa\": \"لیتوانی\", \"fr\": \"Lituanie\", \"hr\": \"Litva\", \"it\": \"Litu<PERSON>\", \"ja\": \"リトアニア\", \"kr\": \"리투아니아\", \"nl\": \"Litouwen\", \"pt\": \"Lituânia\", \"tr\": \"Litvanya\", \"pt-BR\": \"Lituânia\"}", "timezones": "[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Vilnius\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "440", "iso3": "LTU", "nationality": "Lithuanian", "capital": "Vilnius", "tld": ".lt", "native": "Lietuva", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "56.00", "lng": "24.00", "emoji": "🇱🇹", "emojiU": "U+1F1F1 U+1F1F9", "flag": "1", "is_activated": "1"}, {"id": "129", "name": "Luxembourg", "code": "LU", "phone": "442", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Luxembourg\", \"cn\": \"卢森堡\", \"de\": \"Luxemburg\", \"es\": \"Luxemburgo\", \"fa\": \"لوکزامبورگ\", \"fr\": \"Luxembourg\", \"hr\": \"Luksemburg\", \"it\": \"Lussemburgo\", \"ja\": \"ルクセンブルク\", \"kr\": \"룩셈부르크\", \"nl\": \"Luxemburg\", \"pt\": \"Luxemburgo\", \"tr\": \"Lüksemburg\", \"pt-BR\": \"Luxemburgo\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Luxembourg\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "442", "iso3": "LUX", "nationality": "Luxembourg, Luxembourgish", "capital": "Luxembourg", "tld": ".lu", "native": "Luxembourg", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "49.75", "lng": "6.17", "emoji": "🇱🇺", "emojiU": "U+1F1F1 U+1F1FA", "flag": "1", "is_activated": "1"}, {"id": "130", "name": "Macau S.A.R.", "code": "MO", "phone": "446", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Macau S.A.R.\", \"cn\": \"中国澳门\", \"de\": \"<PERSON>ao\", \"es\": \"<PERSON><PERSON>\", \"fa\": \"مکائو\", \"fr\": \"<PERSON><PERSON>\", \"hr\": \"<PERSON><PERSON><PERSON>\", \"it\": \"<PERSON><PERSON>\", \"ja\": \"マカオ\", \"kr\": \"마카오\", \"nl\": \"<PERSON>ao\", \"pt\": \"Macau\", \"tr\": \"<PERSON><PERSON><PERSON>\", \"pt-BR\": \"Macau\"}", "timezones": "[{\"tzName\": \"China Standard Time\", \"zoneName\": \"Asia/Macau\", \"gmtOffset\": 28800, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC+08:00\"}]", "numeric_code": "446", "iso3": "MAC", "nationality": "<PERSON><PERSON><PERSON>, Chinese", "capital": "Macao", "tld": ".mo", "native": "澳門", "region": "Asia", "currency": "MOP", "currency_name": "Macanese pataca", "currency_symbol": "$", "wikiDataId": null, "lat": "22.17", "lng": "113.55", "emoji": "🇲🇴", "emojiU": "U+1F1F2 U+1F1F4", "flag": "1", "is_activated": "1"}, {"id": "131", "name": "North Macedonia", "code": "MK", "phone": "807", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"North Macedonia\", \"cn\": \"北馬其頓\", \"de\": \"Nordmazedonien\", \"es\": \"Macedonia del Norte\", \"fa\": \"ﻢﻗﺩﻮﻨﯿﻫ ﺶﻣﺎﻠﯾ\", \"fr\": \"Macédoine du Nord\", \"hr\": \"Sjeverna Makedonija\", \"it\": \"Macedonia del Nord\", \"ja\": \"北マケドニア\", \"kr\": \"북마케도니아\", \"nl\": \"Noord-Macedonië\", \"pt\": \"Macedónia do Norte\", \"tr\": \"Kuzey Makedonya\", \"pt-BR\": \"Macedônia do Norte\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Skopje\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "807", "iso3": "MKD", "nationality": "Macedonian", "capital": "Skopje", "tld": ".mk", "native": "Северна Македонија", "region": "Europe", "currency": "MKD", "currency_name": "<PERSON><PERSON>", "currency_symbol": "ден", "wikiDataId": null, "lat": "41.83", "lng": "22.00", "emoji": "🇲🇰", "emojiU": "U+1F1F2 U+1F1F0", "flag": "1", "is_activated": "1"}, {"id": "132", "name": "Madagascar", "code": "MG", "phone": "450", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Madagascar\", \"cn\": \"马达加斯加\", \"de\": \"Madagaskar\", \"es\": \"Madagascar\", \"fa\": \"ماداگاسکار\", \"fr\": \"Madagascar\", \"hr\": \"Madagaskar\", \"it\": \"Madagascar\", \"ja\": \"マダガスカル\", \"kr\": \"마다가스카르\", \"nl\": \"Madagaskar\", \"pt\": \"Madagáscar\", \"tr\": \"Madagaskar\", \"pt-BR\": \"Madagascar\"}", "timezones": "[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Indian/Antananarivo\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]", "numeric_code": "450", "iso3": "MDG", "nationality": "Malagasy", "capital": "Antananarivo", "tld": ".mg", "native": "Madagasikara", "region": "Africa", "currency": "MGA", "currency_name": "Malagasy ariary", "currency_symbol": "Ar", "wikiDataId": null, "lat": "-20.00", "lng": "47.00", "emoji": "🇲🇬", "emojiU": "U+1F1F2 U+1F1EC", "flag": "1", "is_activated": "1"}, {"id": "133", "name": "Malawi", "code": "MW", "phone": "454", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Malawi\", \"cn\": \"马拉维\", \"de\": \"Malawi\", \"es\": \"Malawi\", \"fa\": \"مالاوی\", \"fr\": \"Malawi\", \"hr\": \"Malavi\", \"it\": \"Malawi\", \"ja\": \"マラウイ\", \"kr\": \"말라위\", \"nl\": \"Malawi\", \"pt\": \"Malávi\", \"tr\": \"Malavi\", \"pt-BR\": \"Malawi\"}", "timezones": "[{\"tzName\": \"Central Africa Time\", \"zoneName\": \"Africa/Blantyre\", \"gmtOffset\": 7200, \"abbreviation\": \"CAT\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "454", "iso3": "MWI", "nationality": "Malawian", "capital": "Lilongwe", "tld": ".mw", "native": "Malawi", "region": "Africa", "currency": "MWK", "currency_name": "Malawian kwacha", "currency_symbol": "MK", "wikiDataId": null, "lat": "-13.50", "lng": "34.00", "emoji": "🇲🇼", "emojiU": "U+1F1F2 U+1F1FC", "flag": "1", "is_activated": "1"}, {"id": "134", "name": "Malaysia", "code": "MY", "phone": "458", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Malaysia\", \"cn\": \"马来西亚\", \"de\": \"Malaysia\", \"es\": \"Malasia\", \"fa\": \"مالزی\", \"fr\": \"<PERSON><PERSON><PERSON>\", \"hr\": \"<PERSON><PERSON><PERSON>\", \"it\": \"<PERSON><PERSON>\", \"ja\": \"マレーシア\", \"kr\": \"말레이시아\", \"nl\": \"<PERSON>isi<PERSON>\", \"pt\": \"Malásia\", \"tr\": \"<PERSON><PERSON><PERSON>\", \"pt-BR\": \"Malásia\"}", "timezones": "[{\"tzName\": \"Malaysia Time\", \"zoneName\": \"Asia/Kuala_Lumpur\", \"gmtOffset\": 28800, \"abbreviation\": \"MYT\", \"gmtOffsetName\": \"UTC+08:00\"}, {\"tzName\": \"Malaysia Time\", \"zoneName\": \"Asia/Kuching\", \"gmtOffset\": 28800, \"abbreviation\": \"MYT\", \"gmtOffsetName\": \"UTC+08:00\"}]", "numeric_code": "458", "iso3": "MYS", "nationality": "Malaysian", "capital": "Kuala Lumpur", "tld": ".my", "native": "Malaysia", "region": "Asia", "currency": "MYR", "currency_name": "Malaysian ringgit", "currency_symbol": "RM", "wikiDataId": null, "lat": "2.50", "lng": "112.50", "emoji": "🇲🇾", "emojiU": "U+1F1F2 U+1F1FE", "flag": "1", "is_activated": "1"}, {"id": "135", "name": "Maldives", "code": "MV", "phone": "462", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Maldives\", \"cn\": \"马尔代夫\", \"de\": \"Malediven\", \"es\": \"Maldivas\", \"fa\": \"مالدیو\", \"fr\": \"Maldives\", \"hr\": \"<PERSON>divi\", \"it\": \"Maldive\", \"ja\": \"モルディブ\", \"kr\": \"몰디브\", \"nl\": \"Maldiven\", \"pt\": \"Maldivas\", \"tr\": \"Maldivler\", \"pt-BR\": \"Maldivas\"}", "timezones": "[{\"tzName\": \"Maldives Time\", \"zoneName\": \"Indian/Maldives\", \"gmtOffset\": 18000, \"abbreviation\": \"MVT\", \"gmtOffsetName\": \"UTC+05:00\"}]", "numeric_code": "462", "iso3": "MDV", "nationality": "Maldivian", "capital": "Male", "tld": ".mv", "native": "Maldives", "region": "Asia", "currency": "MVR", "currency_name": "Maldivian rufiyaa", "currency_symbol": "Rf", "wikiDataId": null, "lat": "3.25", "lng": "73.00", "emoji": "🇲🇻", "emojiU": "U+1F1F2 U+1F1FB", "flag": "1", "is_activated": "1"}, {"id": "136", "name": "Mali", "code": "ML", "phone": "466", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Mali\", \"cn\": \"马里\", \"de\": \"Mali\", \"es\": \"Mali\", \"fa\": \"مالی\", \"fr\": \"Mali\", \"hr\": \"Mali\", \"it\": \"Mali\", \"ja\": \"マリ\", \"kr\": \"말리\", \"nl\": \"Mali\", \"pt\": \"Mali\", \"tr\": \"Mali\", \"pt-BR\": \"Mali\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Bamako\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "466", "iso3": "MLI", "nationality": "<PERSON><PERSON>, Malinese", "capital": "Ba<PERSON><PERSON>", "tld": ".ml", "native": "Mali", "region": "Africa", "currency": "XOF", "currency_name": "West African CFA franc", "currency_symbol": "CFA", "wikiDataId": null, "lat": "17.00", "lng": "-4.00", "emoji": "🇲🇱", "emojiU": "U+1F1F2 U+1F1F1", "flag": "1", "is_activated": "1"}, {"id": "137", "name": "Malta", "code": "MT", "phone": "470", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Malta\", \"cn\": \"马耳他\", \"de\": \"Malta\", \"es\": \"Malta\", \"fa\": \"مالت\", \"fr\": \"Malte\", \"hr\": \"Malta\", \"it\": \"Malta\", \"ja\": \"マルタ\", \"kr\": \"몰타\", \"nl\": \"Malta\", \"pt\": \"Malta\", \"tr\": \"Malta\", \"pt-BR\": \"Malta\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Malta\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "470", "iso3": "MLT", "nationality": "Maltese", "capital": "Valletta", "tld": ".mt", "native": "Malta", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "35.83", "lng": "14.58", "emoji": "🇲🇹", "emojiU": "U+1F1F2 U+1F1F9", "flag": "1", "is_activated": "1"}, {"id": "138", "name": "Marshall Islands", "code": "MH", "phone": "584", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Marshall Islands\", \"cn\": \"马绍尔群岛\", \"de\": \"Marshallinseln\", \"es\": \"<PERSON><PERSON>\", \"fa\": \"جزایر مارشال\", \"fr\": \"<PERSON><PERSON> Marshall\", \"hr\": \"<PERSON><PERSON><PERSON><PERSON>\", \"it\": \"<PERSON><PERSON>\", \"ja\": \"マーシャル諸島\", \"kr\": \"마셜 제도\", \"nl\": \"Marshalleiland<PERSON>\", \"pt\": \"<PERSON><PERSON>\", \"tr\": \"Mar<PERSON>al Adalari\", \"pt-BR\": \"<PERSON><PERSON>\"}", "timezones": "[{\"tzName\": \"Marshall Islands Time\", \"zoneName\": \"Pacific/Kwajalein\", \"gmtOffset\": 43200, \"abbreviation\": \"MHT\", \"gmtOffsetName\": \"UTC+12:00\"}, {\"tzName\": \"Marshall Islands Time\", \"zoneName\": \"Pacific/Majuro\", \"gmtOffset\": 43200, \"abbreviation\": \"MHT\", \"gmtOffsetName\": \"UTC+12:00\"}]", "numeric_code": "584", "iso3": "MHL", "nationality": "<PERSON><PERSON>", "capital": "<PERSON><PERSON>", "tld": ".mh", "native": "M̧ajeļ", "region": "Oceania", "currency": "USD", "currency_name": "United States dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "9.00", "lng": "168.00", "emoji": "🇲🇭", "emojiU": "U+1F1F2 U+1F1ED", "flag": "1", "is_activated": "1"}, {"id": "139", "name": "Martinique", "code": "MQ", "phone": "474", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"<PERSON><PERSON>\", \"cn\": \"马提尼克岛\", \"de\": \"<PERSON><PERSON>\", \"es\": \"<PERSON><PERSON>\", \"fa\": \"مونتسرات\", \"fr\": \"<PERSON><PERSON>\", \"hr\": \"<PERSON><PERSON>\", \"it\": \"<PERSON><PERSON>\", \"ja\": \"マルティニーク\", \"kr\": \"마르티니크\", \"nl\": \"<PERSON><PERSON>\", \"pt\": \"<PERSON><PERSON>\", \"tr\": \"<PERSON><PERSON>\", \"pt-BR\": \"<PERSON><PERSON>\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Martinique\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "474", "iso3": "MTQ", "nationality": "Martiniquais, Martinican", "capital": "Fort-de-France", "tld": ".mq", "native": "Martinique", "region": "Americas", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "14.67", "lng": "-61.00", "emoji": "🇲🇶", "emojiU": "U+1F1F2 U+1F1F6", "flag": "1", "is_activated": "1"}, {"id": "140", "name": "Mauritania", "code": "MR", "phone": "478", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Mauritania\", \"cn\": \"毛里塔尼亚\", \"de\": \"<PERSON>ure<PERSON><PERSON>\", \"es\": \"Mauritania\", \"fa\": \"موریتانی\", \"fr\": \"Mauritanie\", \"hr\": \"Mauritanija\", \"it\": \"Mauritania\", \"ja\": \"モーリタニア\", \"kr\": \"모리타니\", \"nl\": \"Mauritanië\", \"pt\": \"Mauritânia\", \"tr\": \"Moritanya\", \"pt-BR\": \"Mauritânia\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Nouakchott\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "478", "iso3": "MRT", "nationality": "Mauritanian", "capital": "Nouakchott", "tld": ".mr", "native": "موريتانيا", "region": "Africa", "currency": "MRO", "currency_name": "Mauritanian ouguiya", "currency_symbol": "MRU", "wikiDataId": null, "lat": "20.00", "lng": "-12.00", "emoji": "🇲🇷", "emojiU": "U+1F1F2 U+1F1F7", "flag": "1", "is_activated": "1"}, {"id": "141", "name": "Mauritius", "code": "MU", "phone": "480", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Mauritius\", \"cn\": \"毛里求斯\", \"de\": \"Mauritius\", \"es\": \"<PERSON><PERSON><PERSON>\", \"fa\": \"موریس\", \"fr\": \"Île Maurice\", \"hr\": \"<PERSON>uricijus\", \"it\": \"Mauritius\", \"ja\": \"モーリシャス\", \"kr\": \"모리셔스\", \"nl\": \"Mauritius\", \"pt\": \"Maur<PERSON>cia\", \"tr\": \"Morityus\", \"pt-BR\": \"<PERSON><PERSON><PERSON><PERSON>\"}", "timezones": "[{\"tzName\": \"Mauritius Time\", \"zoneName\": \"Indian/Mauritius\", \"gmtOffset\": 14400, \"abbreviation\": \"MUT\", \"gmtOffsetName\": \"UTC+04:00\"}]", "numeric_code": "480", "iso3": "MUS", "nationality": "<PERSON><PERSON><PERSON>", "capital": "Port Louis", "tld": ".mu", "native": "<PERSON>", "region": "Africa", "currency": "MUR", "currency_name": "Mauritian rupee", "currency_symbol": "₨", "wikiDataId": null, "lat": "-20.28", "lng": "57.55", "emoji": "🇲🇺", "emojiU": "U+1F1F2 U+1F1FA", "flag": "1", "is_activated": "1"}, {"id": "142", "name": "Mayotte", "code": "YT", "phone": "175", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Mayotte\", \"cn\": \"马约特\", \"de\": \"Mayotte\", \"es\": \"Mayotte\", \"fa\": \"مایوت\", \"fr\": \"Mayotte\", \"hr\": \"Mayotte\", \"it\": \"Mayotte\", \"ja\": \"マヨット\", \"kr\": \"마요트\", \"nl\": \"Mayotte\", \"pt\": \"Mayotte\", \"tr\": \"Mayotte\", \"pt-BR\": \"Mayotte\"}", "timezones": "[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Indian/Mayotte\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]", "numeric_code": "175", "iso3": "MYT", "nationality": "<PERSON><PERSON><PERSON>", "capital": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tld": ".yt", "native": "Mayotte", "region": "Africa", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "-12.83", "lng": "45.17", "emoji": "🇾🇹", "emojiU": "U+1F1FE U+1F1F9", "flag": "1", "is_activated": "1"}, {"id": "143", "name": "Mexico", "code": "MX", "phone": "484", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Mexico\", \"cn\": \"墨西哥\", \"de\": \"Mexiko\", \"es\": \"México\", \"fa\": \"مکزیک\", \"fr\": \"Mexique\", \"hr\": \"<PERSON><PERSON><PERSON>\", \"it\": \"Messico\", \"ja\": \"メキシコ\", \"kr\": \"멕시코\", \"nl\": \"Mexico\", \"pt\": \"México\", \"tr\": \"Meksika\", \"pt-BR\": \"México\"}", "timezones": "[{\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Bahia_Banderas\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Cancun\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Mountain Standard Time (North America\", \"zoneName\": \"America/Chihuahua\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Mountain Standard Time (North America\", \"zoneName\": \"America/Hermosillo\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Matamoros\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Mountain Standard Time (North America\", \"zoneName\": \"America/Mazatlan\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Merida\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Mexico_City\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Monterrey\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Mountain Standard Time (North America\", \"zoneName\": \"America/Ojinaga\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Pacific Standard Time (North America\", \"zoneName\": \"America/Tijuana\", \"gmtOffset\": -28800, \"abbreviation\": \"PST\", \"gmtOffsetName\": \"UTC-08:00\"}]", "numeric_code": "484", "iso3": "MEX", "nationality": "Mexican", "capital": "Ciudad de México", "tld": ".mx", "native": "México", "region": "Americas", "currency": "MXN", "currency_name": "Mexican peso", "currency_symbol": "$", "wikiDataId": null, "lat": "23.00", "lng": "-102.00", "emoji": "🇲🇽", "emojiU": "U+1F1F2 U+1F1FD", "flag": "1", "is_activated": "1"}, {"id": "144", "name": "Micronesia", "code": "FM", "phone": "583", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Micronesia\", \"cn\": \"密克罗尼西亚\", \"de\": \"<PERSON><PERSON><PERSON>sien\", \"es\": \"Micronesia\", \"fa\": \"ایالات فدرال میکرونزی\", \"fr\": \"Micronésie\", \"hr\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"it\": \"Micronesia\", \"ja\": \"ミクロネシア連邦\", \"kr\": \"미크로네시아 연방\", \"nl\": \"Micronesië\", \"pt\": \"Micronésia\", \"tr\": \"Mik<PERSON>zya\", \"pt-BR\": \"Micronésia\"}", "timezones": "[{\"tzName\": \"Chuuk Time\", \"zoneName\": \"Pacific/Chuuk\", \"gmtOffset\": 36000, \"abbreviation\": \"CHUT\", \"gmtOffsetName\": \"UTC+10:00\"}, {\"tzName\": \"Kosrae Time\", \"zoneName\": \"Pacific/Kosrae\", \"gmtOffset\": 39600, \"abbreviation\": \"KOST\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Pohnpei Standard Time\", \"zoneName\": \"Pacific/Pohnpei\", \"gmtOffset\": 39600, \"abbreviation\": \"PONT\", \"gmtOffsetName\": \"UTC+11:00\"}]", "numeric_code": "583", "iso3": "FSM", "nationality": "Micronesian", "capital": "Palikir", "tld": ".fm", "native": "Micronesia", "region": "Oceania", "currency": "USD", "currency_name": "United States dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "6.92", "lng": "158.25", "emoji": "🇫🇲", "emojiU": "U+1F1EB U+1F1F2", "flag": "1", "is_activated": "1"}, {"id": "145", "name": "Moldova", "code": "MD", "phone": "498", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Moldova\", \"cn\": \"摩尔多瓦\", \"de\": \"<PERSON><PERSON>wi<PERSON>\", \"es\": \"Moldavia\", \"fa\": \"مولداوی\", \"fr\": \"<PERSON>lda<PERSON>\", \"hr\": \"Moldova\", \"it\": \"Moldavia\", \"ja\": \"モルドバ共和国\", \"kr\": \"몰도바\", \"nl\": \"Moldavië\", \"pt\": \"Moldávia\", \"tr\": \"Moldova\", \"pt-BR\": \"Moldávia\"}", "timezones": "[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Chisinau\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "498", "iso3": "MDA", "nationality": "Moldovan", "capital": "<PERSON><PERSON><PERSON>", "tld": ".md", "native": "Moldova", "region": "Europe", "currency": "MDL", "currency_name": "Moldovan leu", "currency_symbol": "L", "wikiDataId": null, "lat": "47.00", "lng": "29.00", "emoji": "🇲🇩", "emojiU": "U+1F1F2 U+1F1E9", "flag": "1", "is_activated": "1"}, {"id": "146", "name": "Monaco", "code": "MC", "phone": "492", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Monaco\", \"cn\": \"摩纳哥\", \"de\": \"Monaco\", \"es\": \"Mónaco\", \"fa\": \"موناکو\", \"fr\": \"Monaco\", \"hr\": \"<PERSON><PERSON>\", \"it\": \"Principato di Monaco\", \"ja\": \"モナコ\", \"kr\": \"모나코\", \"nl\": \"Monaco\", \"pt\": \"Mónaco\", \"tr\": \"<PERSON><PERSON>\", \"pt-BR\": \"Mônaco\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Monaco\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "492", "iso3": "MCO", "nationality": "Monegasque, Monacan", "capital": "Monaco", "tld": ".mc", "native": "Monaco", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "43.73", "lng": "7.40", "emoji": "🇲🇨", "emojiU": "U+1F1F2 U+1F1E8", "flag": "1", "is_activated": "1"}, {"id": "147", "name": "Mongolia", "code": "MN", "phone": "496", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Mongolia\", \"cn\": \"蒙古\", \"de\": \"Mongolei\", \"es\": \"Mongolia\", \"fa\": \"مغولستان\", \"fr\": \"Mongolie\", \"hr\": \"Mongolija\", \"it\": \"Mongolia\", \"ja\": \"モンゴル\", \"kr\": \"몽골\", \"nl\": \"Mongolië\", \"pt\": \"Mongólia\", \"tr\": \"Moğolistan\", \"pt-BR\": \"Mongólia\"}", "timezones": "[{\"tzName\": \"Choibalsan Standard Time\", \"zoneName\": \"Asia/Choibalsan\", \"gmtOffset\": 28800, \"abbreviation\": \"CHOT\", \"gmtOffsetName\": \"UTC+08:00\"}, {\"tzName\": \"Hovd Time\", \"zoneName\": \"Asia/Hovd\", \"gmtOffset\": 25200, \"abbreviation\": \"HOVT\", \"gmtOffsetName\": \"UTC+07:00\"}, {\"tzName\": \"Ulaanbaatar Standard Time\", \"zoneName\": \"Asia/Ulaanbaatar\", \"gmtOffset\": 28800, \"abbreviation\": \"ULAT\", \"gmtOffsetName\": \"UTC+08:00\"}]", "numeric_code": "496", "iso3": "MNG", "nationality": "Mongolian", "capital": "<PERSON><PERSON>", "tld": ".mn", "native": "Монгол улс", "region": "Asia", "currency": "MNT", "currency_name": "Mongolian tögrög", "currency_symbol": "₮", "wikiDataId": null, "lat": "46.00", "lng": "105.00", "emoji": "🇲🇳", "emojiU": "U+1F1F2 U+1F1F3", "flag": "1", "is_activated": "1"}, {"id": "148", "name": "Montenegro", "code": "ME", "phone": "499", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Montenegro\", \"cn\": \"黑山\", \"de\": \"Montenegro\", \"es\": \"Montenegro\", \"fa\": \"مونته‌نگرو\", \"fr\": \"Monténégro\", \"hr\": \"Crna Gora\", \"it\": \"Montenegro\", \"ja\": \"モンテネグロ\", \"kr\": \"몬테네그로\", \"nl\": \"Montenegro\", \"pt\": \"Montenegro\", \"tr\": \"Karadağ\", \"pt-BR\": \"Montenegro\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Podgorica\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "499", "iso3": "MNE", "nationality": "Montenegrin", "capital": "Podgorica", "tld": ".me", "native": "Црна Гора", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "42.50", "lng": "19.30", "emoji": "🇲🇪", "emojiU": "U+1F1F2 U+1F1EA", "flag": "1", "is_activated": "1"}, {"id": "149", "name": "Montserrat", "code": "MS", "phone": "500", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Montserrat\", \"cn\": \"蒙特塞拉特\", \"de\": \"Montserrat\", \"es\": \"Montserrat\", \"fa\": \"مایوت\", \"fr\": \"Montserrat\", \"hr\": \"Montserrat\", \"it\": \"Montserrat\", \"ja\": \"モントセラト\", \"kr\": \"몬트세랫\", \"nl\": \"Montserrat\", \"pt\": \"Monserrate\", \"tr\": \"Montserrat\", \"pt-BR\": \"Montserrat\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Montserrat\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "500", "iso3": "MSR", "nationality": "<PERSON><PERSON><PERSON><PERSON>", "capital": "Plymouth", "tld": ".ms", "native": "Montserrat", "region": "Americas", "currency": "XCD", "currency_name": "Eastern Caribbean dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "16.75", "lng": "-62.20", "emoji": "🇲🇸", "emojiU": "U+1F1F2 U+1F1F8", "flag": "1", "is_activated": "1"}, {"id": "150", "name": "Morocco", "code": "MA", "phone": "504", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Morocco\", \"cn\": \"摩洛哥\", \"de\": \"<PERSON><PERSON><PERSON>\", \"es\": \"Marruecos\", \"fa\": \"مراکش\", \"fr\": \"<PERSON><PERSON>\", \"hr\": \"<PERSON><PERSON>\", \"it\": \"<PERSON><PERSON>\", \"ja\": \"モロッコ\", \"kr\": \"모로코\", \"nl\": \"<PERSON><PERSON><PERSON>\", \"pt\": \"Marrocos\", \"tr\": \"Fas\", \"pt-BR\": \"Marrocos\"}", "timezones": "[{\"tzName\": \"Western European Summer Time\", \"zoneName\": \"Africa/Casablanca\", \"gmtOffset\": 3600, \"abbreviation\": \"WEST\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "504", "iso3": "MAR", "nationality": "Moroccan", "capital": "Rabat", "tld": ".ma", "native": "المغرب", "region": "Africa", "currency": "MAD", "currency_name": "Moroccan dirham", "currency_symbol": "DH", "wikiDataId": null, "lat": "32.00", "lng": "-5.00", "emoji": "🇲🇦", "emojiU": "U+1F1F2 U+1F1E6", "flag": "1", "is_activated": "1"}, {"id": "151", "name": "Mozambique", "code": "MZ", "phone": "508", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Mozambique\", \"cn\": \"莫桑比克\", \"de\": \"Mosambik\", \"es\": \"Mozambique\", \"fa\": \"موزامبیک\", \"fr\": \"Mozambique\", \"hr\": \"Mozambik\", \"it\": \"Mozambico\", \"ja\": \"モザンビーク\", \"kr\": \"모잠비크\", \"nl\": \"Mozambique\", \"pt\": \"Moçambique\", \"tr\": \"Mozambik\", \"pt-BR\": \"Moçambique\"}", "timezones": "[{\"tzName\": \"Central Africa Time\", \"zoneName\": \"Africa/Maputo\", \"gmtOffset\": 7200, \"abbreviation\": \"CAT\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "508", "iso3": "MOZ", "nationality": "Mozambican", "capital": "Maputo", "tld": ".mz", "native": "Moçambique", "region": "Africa", "currency": "MZN", "currency_name": "Mozambican metical", "currency_symbol": "MT", "wikiDataId": null, "lat": "-18.25", "lng": "35.00", "emoji": "🇲🇿", "emojiU": "U+1F1F2 U+1F1FF", "flag": "1", "is_activated": "1"}, {"id": "152", "name": "Myanmar", "code": "MM", "phone": "104", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Myanmar\", \"cn\": \"缅甸\", \"de\": \"Myanmar\", \"es\": \"Myanmar\", \"fa\": \"میانمار\", \"fr\": \"Myanmar\", \"hr\": \"<PERSON>janmar\", \"it\": \"Birmania\", \"ja\": \"ミャンマー\", \"kr\": \"미얀마\", \"nl\": \"Myanmar\", \"pt\": \"Myanmar\", \"tr\": \"Myanmar\", \"pt-BR\": \"Myanmar\"}", "timezones": "[{\"tzName\": \"Myanmar Standard Time\", \"zoneName\": \"Asia/Yangon\", \"gmtOffset\": 23400, \"abbreviation\": \"MMT\", \"gmtOffsetName\": \"UTC+06:30\"}]", "numeric_code": "104", "iso3": "MMR", "nationality": "Burmese", "capital": "<PERSON><PERSON>", "tld": ".mm", "native": "မြန်မာ", "region": "Asia", "currency": "MMK", "currency_name": "Burmese kyat", "currency_symbol": "K", "wikiDataId": null, "lat": "22.00", "lng": "98.00", "emoji": "🇲🇲", "emojiU": "U+1F1F2 U+1F1F2", "flag": "1", "is_activated": "1"}, {"id": "153", "name": "Namibia", "code": "NA", "phone": "516", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Namibia\", \"cn\": \"纳米比亚\", \"de\": \"Namibia\", \"es\": \"Namibia\", \"fa\": \"نامیبیا\", \"fr\": \"<PERSON><PERSON><PERSON>\", \"hr\": \"<PERSON><PERSON><PERSON>\", \"it\": \"Namibia\", \"ja\": \"ナミビア\", \"kr\": \"나미비아\", \"nl\": \"Nam<PERSON><PERSON>\", \"pt\": \"Namíbia\", \"tr\": \"Namibya\", \"pt-BR\": \"Namíbia\"}", "timezones": "[{\"tzName\": \"West Africa Summer Time\", \"zoneName\": \"Africa/Windhoek\", \"gmtOffset\": 7200, \"abbreviation\": \"WAST\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "516", "iso3": "NAM", "nationality": "Namibian", "capital": "Windhoek", "tld": ".na", "native": "Namibia", "region": "Africa", "currency": "NAD", "currency_name": "Namibian dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "-22.00", "lng": "17.00", "emoji": "🇳🇦", "emojiU": "U+1F1F3 U+1F1E6", "flag": "1", "is_activated": "1"}, {"id": "154", "name": "Nauru", "code": "NR", "phone": "520", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Nauru\", \"cn\": \"瑙鲁\", \"de\": \"Nauru\", \"es\": \"Nauru\", \"fa\": \"نائورو\", \"fr\": \"Nauru\", \"hr\": \"Nauru\", \"it\": \"Nauru\", \"ja\": \"ナウル\", \"kr\": \"나우루\", \"nl\": \"Nauru\", \"pt\": \"Nauru\", \"tr\": \"Nauru\", \"pt-BR\": \"Nauru\"}", "timezones": "[{\"tzName\": \"Nauru Time\", \"zoneName\": \"Pacific/Nauru\", \"gmtOffset\": 43200, \"abbreviation\": \"NRT\", \"gmtOffsetName\": \"UTC+12:00\"}]", "numeric_code": "520", "iso3": "NRU", "nationality": "Nauruan", "capital": "<PERSON><PERSON>", "tld": ".nr", "native": "Nauru", "region": "Oceania", "currency": "AUD", "currency_name": "Australian dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "-0.53", "lng": "166.92", "emoji": "🇳🇷", "emojiU": "U+1F1F3 U+1F1F7", "flag": "1", "is_activated": "1"}, {"id": "155", "name": "Nepal", "code": "NP", "phone": "524", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Nepal\", \"cn\": \"尼泊尔\", \"de\": \"Népal\", \"es\": \"Nepal\", \"fa\": \"نپال\", \"fr\": \"<PERSON>épal\", \"hr\": \"Nepal\", \"it\": \"Nepal\", \"ja\": \"ネパール\", \"kr\": \"네팔\", \"nl\": \"Nepal\", \"pt\": \"Nepal\", \"tr\": \"Nepal\", \"pt-BR\": \"Nepal\"}", "timezones": "[{\"tzName\": \"Nepal Time\", \"zoneName\": \"Asia/Kathmandu\", \"gmtOffset\": 20700, \"abbreviation\": \"NPT\", \"gmtOffsetName\": \"UTC+05:45\"}]", "numeric_code": "524", "iso3": "NPL", "nationality": "Nepali, Nepalese", "capital": "Kathman<PERSON>", "tld": ".np", "native": "नपल", "region": "Asia", "currency": "NPR", "currency_name": "Nepalese rupee", "currency_symbol": "₨", "wikiDataId": null, "lat": "28.00", "lng": "84.00", "emoji": "🇳🇵", "emojiU": "U+1F1F3 U+1F1F5", "flag": "1", "is_activated": "1"}, {"id": "156", "name": "Netherlands", "code": "NL", "phone": "528", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Netherlands\", \"cn\": \"荷兰\", \"de\": \"Niederlande\", \"es\": \"Países Bajos\", \"fa\": \"پادشاهی هلند\", \"fr\": \"Pays-Bas\", \"hr\": \"<PERSON><PERSON><PERSON>ms<PERSON>\", \"it\": \"<PERSON><PERSON>i\", \"ja\": \"オランダ\", \"kr\": \"네덜란드 \", \"nl\": \"Nederland\", \"pt\": \"Países Baixos\", \"tr\": \"Hollanda\", \"pt-BR\": \"Holanda\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Amsterdam\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "528", "iso3": "NLD", "nationality": "Dutch, Netherlandic", "capital": "Amsterdam", "tld": ".nl", "native": "Nederland", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "52.50", "lng": "5.75", "emoji": "🇳🇱", "emojiU": "U+1F1F3 U+1F1F1", "flag": "1", "is_activated": "1"}, {"id": "157", "name": "New Caledonia", "code": "NC", "phone": "540", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"New Caledonia\", \"cn\": \"新喀里多尼亚\", \"de\": \"Neukaledonien\", \"es\": \"Nueva Caledonia\", \"fa\": \"کالدونیای جدید\", \"fr\": \"Nouvelle-Calédonie\", \"hr\": \"Nova Kaledonija\", \"it\": \"Nuova Caledonia\", \"ja\": \"ニューカレドニア\", \"kr\": \"누벨칼레도니\", \"nl\": \"Nieuw-Caledonië\", \"pt\": \"Nova Caledónia\", \"tr\": \"Yeni Kaledonya\", \"pt-BR\": \"Nova Caledônia\"}", "timezones": "[{\"tzName\": \"New Caledonia Time\", \"zoneName\": \"Pacific/Noumea\", \"gmtOffset\": 39600, \"abbreviation\": \"NCT\", \"gmtOffsetName\": \"UTC+11:00\"}]", "numeric_code": "540", "iso3": "NCL", "nationality": "New Caledonian", "capital": "Noumea", "tld": ".nc", "native": "Nouvelle-Calédonie", "region": "Oceania", "currency": "XPF", "currency_name": "CFP franc", "currency_symbol": "₣", "wikiDataId": null, "lat": "-21.50", "lng": "165.50", "emoji": "🇳🇨", "emojiU": "U+1F1F3 U+1F1E8", "flag": "1", "is_activated": "1"}, {"id": "158", "name": "New Zealand", "code": "NZ", "phone": "554", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"New Zealand\", \"cn\": \"新西兰\", \"de\": \"Neuseeland\", \"es\": \"Nueva Zelanda\", \"fa\": \"نیوزیلند\", \"fr\": \"Nouvelle-Zélande\", \"hr\": \"Novi Zeland\", \"it\": \"Nuova Zelanda\", \"ja\": \"ニュージーランド\", \"kr\": \"뉴질랜드\", \"nl\": \"Nieuw-Zeeland\", \"pt\": \"Nova Zelândia\", \"tr\": \"Yeni Zelanda\", \"pt-BR\": \"Nova Zelândia\"}", "timezones": "[{\"tzName\": \"New Zealand Daylight Time\", \"zoneName\": \"Pacific/Auckland\", \"gmtOffset\": 46800, \"abbreviation\": \"NZDT\", \"gmtOffsetName\": \"UTC+13:00\"}, {\"tzName\": \"Chatham Standard Time\", \"zoneName\": \"Pacific/Chatham\", \"gmtOffset\": 49500, \"abbreviation\": \"CHAST\", \"gmtOffsetName\": \"UTC+13:45\"}]", "numeric_code": "554", "iso3": "NZL", "nationality": "New Zealand, NZ", "capital": "Wellington", "tld": ".nz", "native": "New Zealand", "region": "Oceania", "currency": "NZD", "currency_name": "New Zealand dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "-41.00", "lng": "174.00", "emoji": "🇳🇿", "emojiU": "U+1F1F3 U+1F1FF", "flag": "1", "is_activated": "1"}, {"id": "159", "name": "Nicaragua", "code": "NI", "phone": "558", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Nicaragua\", \"cn\": \"尼加拉瓜\", \"de\": \"Nicaragua\", \"es\": \"Nicaragua\", \"fa\": \"نیکاراگوئه\", \"fr\": \"Nicaragua\", \"hr\": \"Nikaragva\", \"it\": \"Nicaragua\", \"ja\": \"ニカラグア\", \"kr\": \"니카라과\", \"nl\": \"Nicaragua\", \"pt\": \"Nicarágua\", \"tr\": \"Nikaragua\", \"pt-BR\": \"Nicarágua\"}", "timezones": "[{\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Managua\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}]", "numeric_code": "558", "iso3": "NIC", "nationality": "Nicaraguan", "capital": "Managua", "tld": ".ni", "native": "Nicaragua", "region": "Americas", "currency": "NIO", "currency_name": "Nicaraguan córdoba", "currency_symbol": "C$", "wikiDataId": null, "lat": "13.00", "lng": "-85.00", "emoji": "🇳🇮", "emojiU": "U+1F1F3 U+1F1EE", "flag": "1", "is_activated": "1"}, {"id": "160", "name": "Niger", "code": "NE", "phone": "562", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Niger\", \"cn\": \"尼日尔\", \"de\": \"Niger\", \"es\": \"Níger\", \"fa\": \"نیجر\", \"fr\": \"Niger\", \"hr\": \"Niger\", \"it\": \"Niger\", \"ja\": \"ニジェール\", \"kr\": \"니제르\", \"nl\": \"Niger\", \"pt\": \"Níger\", \"tr\": \"Nijer\", \"pt-BR\": \"Níger\"}", "timezones": "[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Niamey\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "562", "iso3": "NER", "nationality": "Nigerien", "capital": "Niamey", "tld": ".ne", "native": "Niger", "region": "Africa", "currency": "XOF", "currency_name": "West African CFA franc", "currency_symbol": "CFA", "wikiDataId": null, "lat": "16.00", "lng": "8.00", "emoji": "🇳🇪", "emojiU": "U+1F1F3 U+1F1EA", "flag": "1", "is_activated": "1"}, {"id": "161", "name": "Nigeria", "code": "NG", "phone": "566", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Nigeria\", \"cn\": \"尼日利亚\", \"de\": \"Nigeria\", \"es\": \"Nigeria\", \"fa\": \"نیجریه\", \"fr\": \"<PERSON>géria\", \"hr\": \"Nigerija\", \"it\": \"Nigeria\", \"ja\": \"ナイジェリア\", \"kr\": \"나이지리아\", \"nl\": \"Nigeria\", \"pt\": \"Nigéria\", \"tr\": \"Nijerya\", \"pt-BR\": \"Nigéria\"}", "timezones": "[{\"tzName\": \"West Africa Time\", \"zoneName\": \"Africa/Lagos\", \"gmtOffset\": 3600, \"abbreviation\": \"WAT\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "566", "iso3": "NGA", "nationality": "Nigerian", "capital": "<PERSON>ja", "tld": ".ng", "native": "Nigeria", "region": "Africa", "currency": "NGN", "currency_name": "Nigerian naira", "currency_symbol": "₦", "wikiDataId": null, "lat": "10.00", "lng": "8.00", "emoji": "🇳🇬", "emojiU": "U+1F1F3 U+1F1EC", "flag": "1", "is_activated": "1"}, {"id": "162", "name": "Niue", "code": "NU", "phone": "570", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Niue\", \"cn\": \"纽埃\", \"de\": \"Niue\", \"es\": \"Niue\", \"fa\": \"نیووی\", \"fr\": \"Niue\", \"hr\": \"Niue\", \"it\": \"<PERSON>ue\", \"ja\": \"ニウエ\", \"kr\": \"니우에\", \"nl\": \"Niue\", \"pt\": \"Niue\", \"tr\": \"Niue\", \"pt-BR\": \"Niue\"}", "timezones": "[{\"tzName\": \"Niue Time\", \"zoneName\": \"Pacific/Niue\", \"gmtOffset\": -39600, \"abbreviation\": \"NUT\", \"gmtOffsetName\": \"UTC-11:00\"}]", "numeric_code": "570", "iso3": "NIU", "nationality": "<PERSON><PERSON><PERSON>", "capital": "<PERSON><PERSON><PERSON>", "tld": ".nu", "native": "<PERSON><PERSON><PERSON>", "region": "Oceania", "currency": "NZD", "currency_name": "New Zealand dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "-19.03", "lng": "-169.87", "emoji": "🇳🇺", "emojiU": "U+1F1F3 U+1F1FA", "flag": "1", "is_activated": "1"}, {"id": "163", "name": "Norfolk Island", "code": "NF", "phone": "574", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Norfolk Island\", \"cn\": \"诺福克岛\", \"de\": \"Norfolkinsel\", \"es\": \"Isla de Norfolk\", \"fa\": \"جزیره نورفک\", \"fr\": \"Île de Norfolk\", \"hr\": \"Otok Norfolk\", \"it\": \"Isola Norfolk\", \"ja\": \"ノーフォーク島\", \"kr\": \"노퍽 섬\", \"nl\": \"Norfolkeiland\", \"pt\": \"Ilha Norfolk\", \"tr\": \"Norfolk Adasi\", \"pt-BR\": \"Ilha Norfolk\"}", "timezones": "[{\"tzName\": \"Norfolk Time\", \"zoneName\": \"Pacific/Norfolk\", \"gmtOffset\": 43200, \"abbreviation\": \"NFT\", \"gmtOffsetName\": \"UTC+12:00\"}]", "numeric_code": "574", "iso3": "NFK", "nationality": "Norfolk Island", "capital": "Kingston", "tld": ".nf", "native": "Norfolk Island", "region": "Oceania", "currency": "AUD", "currency_name": "Australian dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "-29.03", "lng": "167.95", "emoji": "🇳🇫", "emojiU": "U+1F1F3 U+1F1EB", "flag": "1", "is_activated": "1"}, {"id": "164", "name": "Northern Mariana Islands", "code": "MP", "phone": "580", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Northern Mariana Islands\", \"cn\": \"北马里亚纳群岛\", \"de\": \"Nördliche Marianen\", \"es\": \"Islas Marianas del Norte\", \"fa\": \"جزایر ماریانای شمالی\", \"fr\": \"Îles Mariannes du Nord\", \"hr\": \"Sjevernomarijanski o<PERSON>ci\", \"it\": \"Isole Marianne Settentrionali\", \"ja\": \"北マリアナ諸島\", \"kr\": \"북마리아나 제도\", \"nl\": \"Noordelijke Marianeneilanden\", \"pt\": \"Ilhas Marianas\", \"tr\": \"<PERSON>zey Mariana Adalari\", \"pt-BR\": \"Ilhas Marianas\"}", "timezones": "[{\"tzName\": \"Chamorro Standard Time\", \"zoneName\": \"Pacific/Saipan\", \"gmtOffset\": 36000, \"abbreviation\": \"ChST\", \"gmtOffsetName\": \"UTC+10:00\"}]", "numeric_code": "580", "iso3": "MNP", "nationality": "Northern Marianan", "capital": "Sai<PERSON>", "tld": ".mp", "native": "Northern Mariana Islands", "region": "Oceania", "currency": "USD", "currency_name": "United States dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "15.20", "lng": "145.75", "emoji": "🇲🇵", "emojiU": "U+1F1F2 U+1F1F5", "flag": "1", "is_activated": "1"}, {"id": "165", "name": "Norway", "code": "NO", "phone": "578", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Norway\", \"cn\": \"挪威\", \"de\": \"Norwegen\", \"es\": \"Noruega\", \"fa\": \"نروژ\", \"fr\": \"Norvège\", \"hr\": \"Norveš<PERSON>\", \"it\": \"Norvegia\", \"ja\": \"ノルウェー\", \"kr\": \"노르웨이\", \"nl\": \"Noorwegen\", \"pt\": \"Noruega\", \"tr\": \"Norveç\", \"pt-BR\": \"Noruega\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Oslo\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "578", "iso3": "NOR", "nationality": "Norwegian", "capital": "Oslo", "tld": ".no", "native": "Norge", "region": "Europe", "currency": "NOK", "currency_name": "Norwegian krone", "currency_symbol": "kr", "wikiDataId": null, "lat": "62.00", "lng": "10.00", "emoji": "🇳🇴", "emojiU": "U+1F1F3 U+1F1F4", "flag": "1", "is_activated": "1"}, {"id": "166", "name": "Oman", "code": "OM", "phone": "512", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Oman\", \"cn\": \"阿曼\", \"de\": \"Oman\", \"es\": \"<PERSON><PERSON>\", \"fa\": \"عمان\", \"fr\": \"Oman\", \"hr\": \"Oman\", \"it\": \"oman\", \"ja\": \"オマーン\", \"kr\": \"오만\", \"nl\": \"Oman\", \"pt\": \"Omã\", \"tr\": \"Umman\", \"pt-BR\": \"Om<PERSON>\"}", "timezones": "[{\"tzName\": \"Gulf Standard Time\", \"zoneName\": \"Asia/Muscat\", \"gmtOffset\": 14400, \"abbreviation\": \"GST\", \"gmtOffsetName\": \"UTC+04:00\"}]", "numeric_code": "512", "iso3": "OMN", "nationality": "Omani", "capital": "Muscat", "tld": ".om", "native": "عمان", "region": "Asia", "currency": "OMR", "currency_name": "<PERSON><PERSON> rial", "currency_symbol": ".ع.ر", "wikiDataId": null, "lat": "21.00", "lng": "57.00", "emoji": "🇴🇲", "emojiU": "U+1F1F4 U+1F1F2", "flag": "1", "is_activated": "1"}, {"id": "167", "name": "Pakistan", "code": "PK", "phone": "586", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Pakistan\", \"cn\": \"巴基斯坦\", \"de\": \"Pakistan\", \"es\": \"Pakistán\", \"fa\": \"پاکستان\", \"fr\": \"Pakistan\", \"hr\": \"Pakistan\", \"it\": \"Pakistan\", \"ja\": \"パキスタン\", \"kr\": \"파키스탄\", \"nl\": \"Pakistan\", \"pt\": \"Paquistão\", \"tr\": \"Pakistan\", \"pt-BR\": \"Paquistão\"}", "timezones": "[{\"tzName\": \"Pakistan Standard Time\", \"zoneName\": \"Asia/Karachi\", \"gmtOffset\": 18000, \"abbreviation\": \"PKT\", \"gmtOffsetName\": \"UTC+05:00\"}]", "numeric_code": "586", "iso3": "PAK", "nationality": "Pakistani", "capital": "Islamabad", "tld": ".pk", "native": "Pakistan", "region": "Asia", "currency": "PKR", "currency_name": "Pakistani rupee", "currency_symbol": "₨", "wikiDataId": null, "lat": "30.00", "lng": "70.00", "emoji": "🇵🇰", "emojiU": "U+1F1F5 U+1F1F0", "flag": "1", "is_activated": "1"}, {"id": "168", "name": "<PERSON><PERSON>", "code": "PW", "phone": "585", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Palau\", \"cn\": \"帕劳\", \"de\": \"<PERSON>lau\", \"es\": \"Palau\", \"fa\": \"پالائو\", \"fr\": \"<PERSON><PERSON>os\", \"hr\": \"<PERSON>lau\", \"it\": \"<PERSON>lau\", \"ja\": \"パラオ\", \"kr\": \"팔라우\", \"nl\": \"Palau\", \"pt\": \"Palau\", \"tr\": \"Palau\", \"pt-BR\": \"Palau\"}", "timezones": "[{\"tzName\": \"Palau Time\", \"zoneName\": \"Pacific/Palau\", \"gmtOffset\": 32400, \"abbreviation\": \"PWT\", \"gmtOffsetName\": \"UTC+09:00\"}]", "numeric_code": "585", "iso3": "PLW", "nationality": "<PERSON><PERSON><PERSON>", "capital": "Melekeok", "tld": ".pw", "native": "<PERSON><PERSON>", "region": "Oceania", "currency": "USD", "currency_name": "United States dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "7.50", "lng": "134.50", "emoji": "🇵🇼", "emojiU": "U+1F1F5 U+1F1FC", "flag": "1", "is_activated": "1"}, {"id": "169", "name": "Palestinian Territory Occupied", "code": "PS", "phone": "275", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Palestinian Territory Occupied\", \"cn\": \"巴勒斯坦\", \"de\": \"Pa<PERSON>äst<PERSON>\", \"es\": \"<PERSON><PERSON><PERSON>\", \"fa\": \"فلسطین\", \"fr\": \"Palestine\", \"hr\": \"<PERSON><PERSON><PERSON>\", \"it\": \"<PERSON><PERSON><PERSON>\", \"ja\": \"パレスチナ\", \"kr\": \"팔레스타인 영토\", \"nl\": \"Palestijnse gebieden\", \"pt\": \"<PERSON>les<PERSON>\", \"tr\": \"<PERSON>listin\", \"pt-BR\": \"<PERSON>les<PERSON>\"}", "timezones": "[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Asia/Gaza\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}, {\"tzName\": \"Eastern European Time\", \"zoneName\": \"Asia/Hebron\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "275", "iso3": "PSE", "nationality": "Palestinian", "capital": "East Jerusalem", "tld": ".ps", "native": "فلسطين", "region": "Asia", "currency": "ILS", "currency_name": "Israeli new shekel", "currency_symbol": "₪", "wikiDataId": null, "lat": "31.90", "lng": "35.20", "emoji": "🇵🇸", "emojiU": "U+1F1F5 U+1F1F8", "flag": "1", "is_activated": "1"}, {"id": "170", "name": "Panama", "code": "PA", "phone": "591", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Panama\", \"cn\": \"巴拿马\", \"de\": \"Panama\", \"es\": \"Panamá\", \"fa\": \"پاناما\", \"fr\": \"Panama\", \"hr\": \"Panama\", \"it\": \"Panama\", \"ja\": \"パナマ\", \"kr\": \"파나마\", \"nl\": \"Panama\", \"pt\": \"Panamá\", \"tr\": \"Panama\", \"pt-BR\": \"Panamá\"}", "timezones": "[{\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Panama\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}]", "numeric_code": "591", "iso3": "PAN", "nationality": "Panamanian", "capital": "Panama City", "tld": ".pa", "native": "Panamá", "region": "Americas", "currency": "PAB", "currency_name": "Panamanian balboa", "currency_symbol": "B/.", "wikiDataId": null, "lat": "9.00", "lng": "-80.00", "emoji": "🇵🇦", "emojiU": "U+1F1F5 U+1F1E6", "flag": "1", "is_activated": "1"}, {"id": "171", "name": "Papua new Guinea", "code": "PG", "phone": "598", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Papua new Guinea\", \"cn\": \"巴布亚新几内亚\", \"de\": \"Papua-Neuguinea\", \"es\": \"Papúa Nueva Guinea\", \"fa\": \"پاپوآ گینه نو\", \"fr\": \"Papouasie-Nouvelle-Guinée\", \"hr\": \"Papua Nova Gvineja\", \"it\": \"Papua Nuova Guinea\", \"ja\": \"パプアニューギニア\", \"kr\": \"파푸아뉴기니\", \"nl\": \"Papoea-Nieuw-Guinea\", \"pt\": \"Papua Nova Guiné\", \"tr\": \"Papua Yeni Gine\", \"pt-BR\": \"Papua Nova Guiné\"}", "timezones": "[{\"tzName\": \"Bougainville Standard Time[6\", \"zoneName\": \"Pacific/Bougainville\", \"gmtOffset\": 39600, \"abbreviation\": \"BST\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Papua New Guinea Time\", \"zoneName\": \"Pacific/Port_Moresby\", \"gmtOffset\": 36000, \"abbreviation\": \"PGT\", \"gmtOffsetName\": \"UTC+10:00\"}]", "numeric_code": "598", "iso3": "PNG", "nationality": "Papua New Guinean, Papuan", "capital": "Port Moresby", "tld": ".pg", "native": "Papua Niugini", "region": "Oceania", "currency": "PGK", "currency_name": "Papua New Guinean kina", "currency_symbol": "K", "wikiDataId": null, "lat": "-6.00", "lng": "147.00", "emoji": "🇵🇬", "emojiU": "U+1F1F5 U+1F1EC", "flag": "1", "is_activated": "1"}, {"id": "172", "name": "Paraguay", "code": "PY", "phone": "600", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Paraguay\", \"cn\": \"巴拉圭\", \"de\": \"Paraguay\", \"es\": \"Paraguay\", \"fa\": \"پاراگوئه\", \"fr\": \"Paraguay\", \"hr\": \"Paragvaj\", \"it\": \"Paraguay\", \"ja\": \"パラグアイ\", \"kr\": \"파라과이\", \"nl\": \"Paraguay\", \"pt\": \"Paraguai\", \"tr\": \"Paraguay\", \"pt-BR\": \"Paraguai\"}", "timezones": "[{\"tzName\": \"Paraguay Summer Time\", \"zoneName\": \"America/Asuncion\", \"gmtOffset\": -10800, \"abbreviation\": \"PYST\", \"gmtOffsetName\": \"UTC-03:00\"}]", "numeric_code": "600", "iso3": "PRY", "nationality": "Paraguayan", "capital": "Asuncion", "tld": ".py", "native": "Paraguay", "region": "Americas", "currency": "PYG", "currency_name": "Paraguayan guarani", "currency_symbol": "₲", "wikiDataId": null, "lat": "-23.00", "lng": "-58.00", "emoji": "🇵🇾", "emojiU": "U+1F1F5 U+1F1FE", "flag": "1", "is_activated": "1"}, {"id": "173", "name": "Peru", "code": "PE", "phone": "604", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Peru\", \"cn\": \"秘鲁\", \"de\": \"Peru\", \"es\": \"Perú\", \"fa\": \"پرو\", \"fr\": \"<PERSON><PERSON><PERSON>\", \"hr\": \"Peru\", \"it\": \"Perù\", \"ja\": \"ペルー\", \"kr\": \"페루\", \"nl\": \"Peru\", \"pt\": \"Peru\", \"tr\": \"Peru\", \"pt-BR\": \"Peru\"}", "timezones": "[{\"tzName\": \"Peru Time\", \"zoneName\": \"America/Lima\", \"gmtOffset\": -18000, \"abbreviation\": \"PET\", \"gmtOffsetName\": \"UTC-05:00\"}]", "numeric_code": "604", "iso3": "PER", "nationality": "Peruvian", "capital": "Lima", "tld": ".pe", "native": "Perú", "region": "Americas", "currency": "PEN", "currency_name": "Peruvian sol", "currency_symbol": "S/.", "wikiDataId": null, "lat": "-10.00", "lng": "-76.00", "emoji": "🇵🇪", "emojiU": "U+1F1F5 U+1F1EA", "flag": "1", "is_activated": "1"}, {"id": "174", "name": "Philippines", "code": "PH", "phone": "608", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Philippines\", \"cn\": \"菲律宾\", \"de\": \"Philippinen\", \"es\": \"Filipinas\", \"fa\": \"جزایر الندفیلیپین\", \"fr\": \"Philippines\", \"hr\": \"Filipini\", \"it\": \"<PERSON>lippine\", \"ja\": \"フィリピン\", \"kr\": \"필리핀\", \"nl\": \"Filipijnen\", \"pt\": \"Filipinas\", \"tr\": \"Filipinler\", \"pt-BR\": \"Filipinas\"}", "timezones": "[{\"tzName\": \"Philippine Time\", \"zoneName\": \"Asia/Manila\", \"gmtOffset\": 28800, \"abbreviation\": \"PHT\", \"gmtOffsetName\": \"UTC+08:00\"}]", "numeric_code": "608", "iso3": "PHL", "nationality": "Philippine, Filipino", "capital": "Manila", "tld": ".ph", "native": "Pilipinas", "region": "Asia", "currency": "PHP", "currency_name": "Philippine peso", "currency_symbol": "₱", "wikiDataId": null, "lat": "13.00", "lng": "122.00", "emoji": "🇵🇭", "emojiU": "U+1F1F5 U+1F1ED", "flag": "1", "is_activated": "1"}, {"id": "175", "name": "Pitcairn Island", "code": "PN", "phone": "612", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Pitcairn Island\", \"cn\": \"皮特凯恩群岛\", \"de\": \"Pitcairn\", \"es\": \"Islas Pitcairn\", \"fa\": \"پیتکرن\", \"fr\": \"Îles Pitcairn\", \"hr\": \"Pitcairnovo otočje\", \"it\": \"Isole Pitcairn\", \"ja\": \"ピトケアン\", \"kr\": \"핏케언 제도\", \"nl\": \"Pitcairneilanden\", \"pt\": \"Ilhas Picárnia\", \"tr\": \"Pitcairn Adalari\", \"pt-BR\": \"Ilhas Pitcairn\"}", "timezones": "[{\"tzName\": \"Pacific Standard Time (North America\", \"zoneName\": \"Pacific/Pitcairn\", \"gmtOffset\": -28800, \"abbreviation\": \"PST\", \"gmtOffsetName\": \"UTC-08:00\"}]", "numeric_code": "612", "iso3": "PCN", "nationality": "Pitcairn Island", "capital": "Adamstown", "tld": ".pn", "native": "Pitcairn Islands", "region": "Oceania", "currency": "NZD", "currency_name": "New Zealand dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "-25.07", "lng": "-130.10", "emoji": "🇵🇳", "emojiU": "U+1F1F5 U+1F1F3", "flag": "1", "is_activated": "1"}, {"id": "176", "name": "Poland", "code": "PL", "phone": "616", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Poland\", \"cn\": \"波兰\", \"de\": \"Polen\", \"es\": \"Polonia\", \"fa\": \"لهستان\", \"fr\": \"Pologne\", \"hr\": \"Polj<PERSON>\", \"it\": \"Polonia\", \"ja\": \"ポーランド\", \"kr\": \"폴란드\", \"nl\": \"Polen\", \"pt\": \"Polónia\", \"tr\": \"Polonya\", \"pt-BR\": \"Polônia\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Warsaw\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "616", "iso3": "POL", "nationality": "Polish", "capital": "Warsaw", "tld": ".pl", "native": "Polska", "region": "Europe", "currency": "PLN", "currency_name": "Polish złoty", "currency_symbol": "zł", "wikiDataId": null, "lat": "52.00", "lng": "20.00", "emoji": "🇵🇱", "emojiU": "U+1F1F5 U+1F1F1", "flag": "1", "is_activated": "1"}, {"id": "177", "name": "Portugal", "code": "PT", "phone": "620", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Portugal\", \"cn\": \"葡萄牙\", \"de\": \"Portugal\", \"es\": \"Portugal\", \"fa\": \"پرتغال\", \"fr\": \"Portugal\", \"hr\": \"Portugal\", \"it\": \"Portogallo\", \"ja\": \"ポルトガル\", \"kr\": \"포르투갈\", \"nl\": \"Portugal\", \"pt\": \"Portugal\", \"tr\": \"Portekiz\", \"pt-BR\": \"Portugal\"}", "timezones": "[{\"tzName\": \"Azores Standard Time\", \"zoneName\": \"Atlantic/Azores\", \"gmtOffset\": -3600, \"abbreviation\": \"AZOT\", \"gmtOffsetName\": \"UTC-01:00\"}, {\"tzName\": \"Western European Time\", \"zoneName\": \"Atlantic/Madeira\", \"gmtOffset\": 0, \"abbreviation\": \"WET\", \"gmtOffsetName\": \"UTC±00\"}, {\"tzName\": \"Western European Time\", \"zoneName\": \"Europe/Lisbon\", \"gmtOffset\": 0, \"abbreviation\": \"WET\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "620", "iso3": "PRT", "nationality": "Portuguese", "capital": "Lisbon", "tld": ".pt", "native": "Portugal", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "39.50", "lng": "-8.00", "emoji": "🇵🇹", "emojiU": "U+1F1F5 U+1F1F9", "flag": "1", "is_activated": "1"}, {"id": "178", "name": "Puerto Rico", "code": "PR", "phone": "630", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Puerto Rico\", \"cn\": \"波多黎各\", \"de\": \"Puerto Rico\", \"es\": \"Puerto Rico\", \"fa\": \"پورتو ریکو\", \"fr\": \"Porto Rico\", \"hr\": \"<PERSON>rik<PERSON>\", \"it\": \"Porto Rico\", \"ja\": \"プエルトリコ\", \"kr\": \"푸에르토리코\", \"nl\": \"Puerto Rico\", \"pt\": \"Porto Rico\", \"tr\": \"Porto Riko\", \"pt-BR\": \"Porto Rico\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Puerto_Rico\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "630", "iso3": "PRI", "nationality": "Puerto Rican", "capital": "San Juan", "tld": ".pr", "native": "Puerto Rico", "region": "Americas", "currency": "USD", "currency_name": "United States dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "18.25", "lng": "-66.50", "emoji": "🇵🇷", "emojiU": "U+1F1F5 U+1F1F7", "flag": "1", "is_activated": "1"}, {"id": "179", "name": "Qatar", "code": "QA", "phone": "634", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Qatar\", \"cn\": \"卡塔尔\", \"de\": \"Katar\", \"es\": \"Catar\", \"fa\": \"قطر\", \"fr\": \"Qatar\", \"hr\": \"Katar\", \"it\": \"Qatar\", \"ja\": \"カタール\", \"kr\": \"카타르\", \"nl\": \"Qatar\", \"pt\": \"Catar\", \"tr\": \"Katar\", \"pt-BR\": \"Catar\"}", "timezones": "[{\"tzName\": \"Arabia Standard Time\", \"zoneName\": \"Asia/Qatar\", \"gmtOffset\": 10800, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC+03:00\"}]", "numeric_code": "634", "iso3": "QAT", "nationality": "Qatari", "capital": "Doha", "tld": ".qa", "native": "قطر", "region": "Asia", "currency": "QAR", "currency_name": "Qatari riyal", "currency_symbol": "ق.ر", "wikiDataId": null, "lat": "25.50", "lng": "51.25", "emoji": "🇶🇦", "emojiU": "U+1F1F6 U+1F1E6", "flag": "1", "is_activated": "1"}, {"id": "180", "name": "Reunion", "code": "RE", "phone": "638", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Reunion\", \"cn\": \"留尼汪岛\", \"de\": \"Réunion\", \"es\": \"Reunión\", \"fa\": \"رئونیون\", \"fr\": \"Réunion\", \"hr\": \"Réunion\", \"it\": \"Riunione\", \"ja\": \"レユニオン\", \"kr\": \"레위니옹\", \"nl\": \"Réunion\", \"pt\": \"<PERSON>uni<PERSON>\", \"tr\": \"Réunion\", \"pt-BR\": \"<PERSON>união\"}", "timezones": "[{\"tzName\": \"Réunion Time\", \"zoneName\": \"Indian/Reunion\", \"gmtOffset\": 14400, \"abbreviation\": \"RET\", \"gmtOffsetName\": \"UTC+04:00\"}]", "numeric_code": "638", "iso3": "REU", "nationality": "Reunionese, Reunionnais", "capital": "Saint-Denis", "tld": ".re", "native": "La Réunion", "region": "Africa", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "-21.15", "lng": "55.50", "emoji": "🇷🇪", "emojiU": "U+1F1F7 U+1F1EA", "flag": "1", "is_activated": "1"}, {"id": "181", "name": "Romania", "code": "RO", "phone": "642", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Romania\", \"cn\": \"罗马尼亚\", \"de\": \"<PERSON>um<PERSON>nien\", \"es\": \"Rumania\", \"fa\": \"رومانی\", \"fr\": \"<PERSON><PERSON>mani<PERSON>\", \"hr\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"it\": \"Romania\", \"ja\": \"ルーマニア\", \"kr\": \"루마니아\", \"nl\": \"Roemenië\", \"pt\": \"Rom<PERSON>ia\", \"tr\": \"Romanya\", \"pt-BR\": \"Romê<PERSON>\"}", "timezones": "[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Bucharest\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "642", "iso3": "ROU", "nationality": "Romanian", "capital": "Bucharest", "tld": ".ro", "native": "România", "region": "Europe", "currency": "RON", "currency_name": "Romanian leu", "currency_symbol": "lei", "wikiDataId": null, "lat": "46.00", "lng": "25.00", "emoji": "🇷🇴", "emojiU": "U+1F1F7 U+1F1F4", "flag": "1", "is_activated": "1"}, {"id": "182", "name": "Russia", "code": "RU", "phone": "643", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Russia\", \"cn\": \"俄罗斯联邦\", \"de\": \"Russland\", \"es\": \"Rusia\", \"fa\": \"روسیه\", \"fr\": \"<PERSON>ie\", \"hr\": \"Rusija\", \"it\": \"Russia\", \"ja\": \"ロシア連邦\", \"kr\": \"러시아\", \"nl\": \"Rusland\", \"pt\": \"Rússia\", \"tr\": \"Rusya\", \"pt-BR\": \"Rússia\"}", "timezones": "[{\"tzName\": \"Anadyr Time[4\", \"zoneName\": \"Asia/Anadyr\", \"gmtOffset\": 43200, \"abbreviation\": \"ANAT\", \"gmtOffsetName\": \"UTC+12:00\"}, {\"tzName\": \"Krasnoyarsk Time\", \"zoneName\": \"Asia/Barnaul\", \"gmtOffset\": 25200, \"abbreviation\": \"KRAT\", \"gmtOffsetName\": \"UTC+07:00\"}, {\"tzName\": \"Yakutsk Time\", \"zoneName\": \"Asia/Chita\", \"gmtOffset\": 32400, \"abbreviation\": \"YAKT\", \"gmtOffsetName\": \"UTC+09:00\"}, {\"tzName\": \"Irkutsk Time\", \"zoneName\": \"Asia/Irkutsk\", \"gmtOffset\": 28800, \"abbreviation\": \"IRKT\", \"gmtOffsetName\": \"UTC+08:00\"}, {\"tzName\": \"Kamchatka Time\", \"zoneName\": \"Asia/Kamchatka\", \"gmtOffset\": 43200, \"abbreviation\": \"PETT\", \"gmtOffsetName\": \"UTC+12:00\"}, {\"tzName\": \"Yakutsk Time\", \"zoneName\": \"Asia/Khandyga\", \"gmtOffset\": 32400, \"abbreviation\": \"YAKT\", \"gmtOffsetName\": \"UTC+09:00\"}, {\"tzName\": \"Krasnoyarsk Time\", \"zoneName\": \"Asia/Krasnoyarsk\", \"gmtOffset\": 25200, \"abbreviation\": \"KRAT\", \"gmtOffsetName\": \"UTC+07:00\"}, {\"tzName\": \"Magadan Time\", \"zoneName\": \"Asia/Magadan\", \"gmtOffset\": 39600, \"abbreviation\": \"MAGT\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Krasnoyarsk Time\", \"zoneName\": \"Asia/Novokuznetsk\", \"gmtOffset\": 25200, \"abbreviation\": \"KRAT\", \"gmtOffsetName\": \"UTC+07:00\"}, {\"tzName\": \"Novosibirsk Time\", \"zoneName\": \"Asia/Novosibirsk\", \"gmtOffset\": 25200, \"abbreviation\": \"NOVT\", \"gmtOffsetName\": \"UTC+07:00\"}, {\"tzName\": \"Omsk Time\", \"zoneName\": \"Asia/Omsk\", \"gmtOffset\": 21600, \"abbreviation\": \"OMST\", \"gmtOffsetName\": \"UTC+06:00\"}, {\"tzName\": \"Sakhalin Island Time\", \"zoneName\": \"Asia/Sakhalin\", \"gmtOffset\": 39600, \"abbreviation\": \"SAKT\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Srednekolymsk Time\", \"zoneName\": \"Asia/Srednekolymsk\", \"gmtOffset\": 39600, \"abbreviation\": \"SRET\", \"gmtOffsetName\": \"UTC+11:00\"}, {\"tzName\": \"Moscow Daylight Time+3\", \"zoneName\": \"Asia/Tomsk\", \"gmtOffset\": 25200, \"abbreviation\": \"MSD+3\", \"gmtOffsetName\": \"UTC+07:00\"}, {\"tzName\": \"Vladivostok Time\", \"zoneName\": \"Asia/Ust-Nera\", \"gmtOffset\": 36000, \"abbreviation\": \"VLAT\", \"gmtOffsetName\": \"UTC+10:00\"}, {\"tzName\": \"Vladivostok Time\", \"zoneName\": \"Asia/Vladivostok\", \"gmtOffset\": 36000, \"abbreviation\": \"VLAT\", \"gmtOffsetName\": \"UTC+10:00\"}, {\"tzName\": \"Yakutsk Time\", \"zoneName\": \"Asia/Yakutsk\", \"gmtOffset\": 32400, \"abbreviation\": \"YAKT\", \"gmtOffsetName\": \"UTC+09:00\"}, {\"tzName\": \"Yekaterinburg Time\", \"zoneName\": \"Asia/Yekaterinburg\", \"gmtOffset\": 18000, \"abbreviation\": \"YEKT\", \"gmtOffsetName\": \"UTC+05:00\"}, {\"tzName\": \"Samara Time\", \"zoneName\": \"Europe/Astrakhan\", \"gmtOffset\": 14400, \"abbreviation\": \"SAMT\", \"gmtOffsetName\": \"UTC+04:00\"}, {\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Kaliningrad\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}, {\"tzName\": \"Moscow Time\", \"zoneName\": \"Europe/Kirov\", \"gmtOffset\": 10800, \"abbreviation\": \"MSK\", \"gmtOffsetName\": \"UTC+03:00\"}, {\"tzName\": \"Moscow Time\", \"zoneName\": \"Europe/Moscow\", \"gmtOffset\": 10800, \"abbreviation\": \"MSK\", \"gmtOffsetName\": \"UTC+03:00\"}, {\"tzName\": \"Samara Time\", \"zoneName\": \"Europe/Samara\", \"gmtOffset\": 14400, \"abbreviation\": \"SAMT\", \"gmtOffsetName\": \"UTC+04:00\"}, {\"tzName\": \"Moscow Daylight Time+4\", \"zoneName\": \"Europe/Saratov\", \"gmtOffset\": 14400, \"abbreviation\": \"MSD\", \"gmtOffsetName\": \"UTC+04:00\"}, {\"tzName\": \"Samara Time\", \"zoneName\": \"Europe/Ulyanovsk\", \"gmtOffset\": 14400, \"abbreviation\": \"SAMT\", \"gmtOffsetName\": \"UTC+04:00\"}, {\"tzName\": \"Moscow Standard Time\", \"zoneName\": \"Europe/Volgograd\", \"gmtOffset\": 14400, \"abbreviation\": \"MSK\", \"gmtOffsetName\": \"UTC+04:00\"}]", "numeric_code": "643", "iso3": "RUS", "nationality": "Russian", "capital": "Moscow", "tld": ".ru", "native": "Россия", "region": "Europe", "currency": "RUB", "currency_name": "Russian ruble", "currency_symbol": "₽", "wikiDataId": null, "lat": "60.00", "lng": "100.00", "emoji": "🇷🇺", "emojiU": "U+1F1F7 U+1F1FA", "flag": "1", "is_activated": "1"}, {"id": "183", "name": "Rwanda", "code": "RW", "phone": "646", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Rwanda\", \"cn\": \"卢旺达\", \"de\": \"Ruanda\", \"es\": \"Ruanda\", \"fa\": \"رواندا\", \"fr\": \"Rwanda\", \"hr\": \"Ruanda\", \"it\": \"<PERSON>uan<PERSON>\", \"ja\": \"ルワンダ\", \"kr\": \"르완다\", \"nl\": \"Rwanda\", \"pt\": \"Ruanda\", \"tr\": \"Ruanda\", \"pt-BR\": \"Ruanda\"}", "timezones": "[{\"tzName\": \"Central Africa Time\", \"zoneName\": \"Africa/Kigali\", \"gmtOffset\": 7200, \"abbreviation\": \"CAT\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "646", "iso3": "RWA", "nationality": "Rwandan", "capital": "Kigali", "tld": ".rw", "native": "Rwanda", "region": "Africa", "currency": "RWF", "currency_name": "Rwandan franc", "currency_symbol": "FRw", "wikiDataId": null, "lat": "-2.00", "lng": "30.00", "emoji": "🇷🇼", "emojiU": "U+1F1F7 U+1F1FC", "flag": "1", "is_activated": "1"}, {"id": "184", "name": "Saint-<PERSON><PERSON><PERSON><PERSON>", "code": "BL", "phone": "652", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\", \"cn\": \"圣巴泰勒米\", \"de\": \"Saint-Barthélemy\", \"es\": \"San Bartolomé\", \"fa\": \"سن-بارتلمی\", \"fr\": \"<PERSON><PERSON>Barthélemy\", \"hr\": \"<PERSON> Barthélemy\", \"it\": \"<PERSON>lle Francesi\", \"ja\": \"サン・バルテルミー\", \"kr\": \"생바르텔레미\", \"nl\": \"Saint Barthélemy\", \"pt\": \"São Bartolomeu\", \"tr\": \"Saint Barthélemy\", \"pt-BR\": \"São Bartolomeu\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/St_Barthelemy\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "652", "iso3": "BLM", "nationality": "<PERSON><PERSON><PERSON><PERSON>", "capital": "Gustavia", "tld": ".bl", "native": "Saint-Bart<PERSON><PERSON><PERSON><PERSON>", "region": "Americas", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "18.50", "lng": "-63.42", "emoji": "🇧🇱", "emojiU": "U+1F1E7 U+1F1F1", "flag": "1", "is_activated": "1"}, {"id": "185", "name": "Saint Helena", "code": "SH", "phone": "654", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"<PERSON> Helena\", \"cn\": \"圣赫勒拿\", \"de\": \"<PERSON><PERSON> <PERSON>\", \"es\": \"Santa Helena\", \"fa\": \"سنت هلنا، اسنشن و تریستان دا کونا\", \"fr\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\", \"hr\": \"<PERSON><PERSON><PERSON>\", \"it\": \"<PERSON> Elena\", \"ja\": \"セントヘレナ・アセンションおよびトリスタンダクーニャ\", \"kr\": \"세인트헬레나\", \"nl\": \"Sint-Helena\", \"pt\": \"Santa Helena\", \"tr\": \"Saint Helena\", \"pt-BR\": \"Santa Helena\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Atlantic/St_Helena\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "654", "iso3": "SHN", "nationality": "<PERSON> <PERSON>", "capital": "Jamestown", "tld": ".sh", "native": "Saint Helena", "region": "Africa", "currency": "SHP", "currency_name": "Saint Helena pound", "currency_symbol": "£", "wikiDataId": null, "lat": "-15.95", "lng": "-5.70", "emoji": "🇸🇭", "emojiU": "U+1F1F8 U+1F1ED", "flag": "1", "is_activated": "1"}, {"id": "186", "name": "Saint Kitts And Nevis", "code": "KN", "phone": "659", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"<PERSON> Kitts And Nevis\", \"cn\": \"圣基茨和尼维斯\", \"de\": \"St. Kitts und Nevis\", \"es\": \"San Cristóbal y Nieves\", \"fa\": \"سنت کیتس و نویس\", \"fr\": \"<PERSON>-Christophe-et-Niévès\", \"hr\": \"<PERSON><PERSON><PERSON> i <PERSON>evis\", \"it\": \"Saint Kitts e Nevis\", \"ja\": \"セントクリストファー・ネイビス\", \"kr\": \"세인트키츠 네비스\", \"nl\": \"Saint Kitts en Nevis\", \"pt\": \"São Cristóvão e Neves\", \"tr\": \"Saint Kitts Ve Nevis\", \"pt-BR\": \"São Cristóvão e Neves\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/St_Kitts\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "659", "iso3": "KNA", "nationality": "Kittitian or Nevisian", "capital": "Basseterre", "tld": ".kn", "native": "Saint Kitts and Nevis", "region": "Americas", "currency": "XCD", "currency_name": "Eastern Caribbean dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "17.33", "lng": "-62.75", "emoji": "🇰🇳", "emojiU": "U+1F1F0 U+1F1F3", "flag": "1", "is_activated": "1"}, {"id": "187", "name": "Saint Lucia", "code": "LC", "phone": "662", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Saint Lucia\", \"cn\": \"圣卢西亚\", \"de\": \"Saint Lucia\", \"es\": \"Santa Lucía\", \"fa\": \"سنت لوسیا\", \"fr\": \"<PERSON><PERSON><PERSON><PERSON>\", \"hr\": \"<PERSON>veta Lucija\", \"it\": \"Santa Lucia\", \"ja\": \"セントルシア\", \"kr\": \"세인트루시아\", \"nl\": \"Saint Lucia\", \"pt\": \"Santa Lúcia\", \"tr\": \"Saint Lucia\", \"pt-BR\": \"Santa Lúcia\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/St_Lucia\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "662", "iso3": "LCA", "nationality": "Saint Lucian", "capital": "Castries", "tld": ".lc", "native": "Saint Lucia", "region": "Americas", "currency": "XCD", "currency_name": "Eastern Caribbean dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "13.88", "lng": "-60.97", "emoji": "🇱🇨", "emojiU": "U+1F1F1 U+1F1E8", "flag": "1", "is_activated": "1"}, {"id": "188", "name": "<PERSON><PERSON><PERSON> (French part)", "code": "MF", "phone": "663", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"<PERSON><PERSON><PERSON> (French part)\", \"cn\": \"密克罗尼西亚\", \"de\": \"<PERSON> Martin\", \"es\": \"Saint Martin\", \"fa\": \"سینت مارتن\", \"fr\": \"<PERSON><PERSON><PERSON>\", \"hr\": \"<PERSON><PERSON><PERSON>\", \"it\": \"<PERSON> Martin\", \"ja\": \"サン・マルタン（フランス領）\", \"kr\": \"세인트마틴 섬\", \"nl\": \"Saint-Martin\", \"pt\": \"Ilha São Martinho\", \"tr\": \"Saint Martin\", \"pt-BR\": \"Saint Martin\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Marigot\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "663", "iso3": "MAF", "nationality": "Saint-Martinoise", "capital": "Marigot", "tld": ".mf", "native": "Saint-<PERSON>", "region": "Americas", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "18.08", "lng": "-63.95", "emoji": "🇲🇫", "emojiU": "U+1F1F2 U+1F1EB", "flag": "1", "is_activated": "1"}, {"id": "189", "name": "Saint Pierre and Miquelon", "code": "PM", "phone": "666", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Saint Pierre and Miquelon\", \"cn\": \"圣皮埃尔和密克隆\", \"de\": \"Saint<PERSON><PERSON> und Miquelon\", \"es\": \"San Pedro y Miquelón\", \"fa\": \"سن پیر و میکلن\", \"fr\": \"Saint-Pierre-et-Miquelon\", \"hr\": \"<PERSON>veti <PERSON> i <PERSON>lon\", \"it\": \"Saint-Pierre e Miquelon\", \"ja\": \"サンピエール島・ミクロン島\", \"kr\": \"생피에르 미클롱\", \"nl\": \"Saint Pierre en Miquelon\", \"pt\": \"São Pedro e Miquelon\", \"tr\": \"Saint Pierre Ve Miquelon\", \"pt-BR\": \"Saint-Pierre e Miquelon\"}", "timezones": "[{\"tzName\": \"Pierre & Miquelon Daylight Time\", \"zoneName\": \"America/Miquelon\", \"gmtOffset\": -10800, \"abbreviation\": \"PMDT\", \"gmtOffsetName\": \"UTC-03:00\"}]", "numeric_code": "666", "iso3": "SPM", "nationality": "Saint-Pierrais or Miquelonnais", "capital": "Saint<PERSON><PERSON>", "tld": ".pm", "native": "Saint-Pierre-et-Miquelon", "region": "Americas", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "46.83", "lng": "-56.33", "emoji": "🇵🇲", "emojiU": "U+1F1F5 U+1F1F2", "flag": "1", "is_activated": "1"}, {"id": "190", "name": "Saint Vincent And The Grenadines", "code": "VC", "phone": "670", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"<PERSON> Vincent And The Grenadines\", \"cn\": \"圣文森特和格林纳丁斯\", \"de\": \"Saint Vincent und die Grenadinen\", \"es\": \"San Vicente y Granadinas\", \"fa\": \"سنت وینسنت و گرنادین‌ها\", \"fr\": \"Saint-Vincent-et-les-Grenadines\", \"hr\": \"<PERSON>veti Vincent i Grenadini\", \"it\": \"Saint Vincent e Grenadine\", \"ja\": \"セントビンセントおよびグレナディーン諸島\", \"kr\": \"세인트빈센트 그레나딘\", \"nl\": \"Saint Vincent en de Grenadines\", \"pt\": \"São Vicente e Granadinas\", \"tr\": \"Saint Vincent Ve Grenadinler\", \"pt-BR\": \"São Vicente e Granadinas\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/St_Vincent\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "670", "iso3": "VCT", "nationality": "<PERSON>, Vincentian", "capital": "Kingstown", "tld": ".vc", "native": "Saint Vincent and the Grenadines", "region": "Americas", "currency": "XCD", "currency_name": "Eastern Caribbean dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "13.25", "lng": "-61.20", "emoji": "🇻🇨", "emojiU": "U+1F1FB U+1F1E8", "flag": "1", "is_activated": "1"}, {"id": "191", "name": "Samoa", "code": "WS", "phone": "882", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"Samoa\", \"cn\": \"萨摩亚\", \"de\": \"Samoa\", \"es\": \"Samoa\", \"fa\": \"ساموآ\", \"fr\": \"Samoa\", \"hr\": \"Samoa\", \"it\": \"Samoa\", \"ja\": \"サモア\", \"kr\": \"사모아\", \"nl\": \"Samoa\", \"pt\": \"Samoa\", \"tr\": \"Samoa\", \"pt-BR\": \"Samoa\"}", "timezones": "[{\"tzName\": \"West Samoa Time\", \"zoneName\": \"Pacific/Apia\", \"gmtOffset\": 50400, \"abbreviation\": \"WST\", \"gmtOffsetName\": \"UTC+14:00\"}]", "numeric_code": "882", "iso3": "WSM", "nationality": "Samoan", "capital": "Apia", "tld": ".ws", "native": "Samoa", "region": "Oceania", "currency": "WST", "currency_name": "Samoan tālā", "currency_symbol": "SAT", "wikiDataId": null, "lat": "-13.58", "lng": "-172.33", "emoji": "🇼🇸", "emojiU": "U+1F1FC U+1F1F8", "flag": "1", "is_activated": "1"}, {"id": "192", "name": "San Marino", "code": "SM", "phone": "674", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:29", "translations": "{\"ar\": \"San Marino\", \"cn\": \"圣马力诺\", \"de\": \"San Marino\", \"es\": \"San Marino\", \"fa\": \"سان مارینو\", \"fr\": \"Saint-Marin\", \"hr\": \"San Marino\", \"it\": \"San Marino\", \"ja\": \"サンマリノ\", \"kr\": \"산마리노\", \"nl\": \"San Marino\", \"pt\": \"São Marinho\", \"tr\": \"San Marino\", \"pt-BR\": \"San Marino\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/San_Marino\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "674", "iso3": "SMR", "nationality": "Sammarinese", "capital": "San Marino", "tld": ".sm", "native": "San Marino", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "43.77", "lng": "12.42", "emoji": "🇸🇲", "emojiU": "U+1F1F8 U+1F1F2", "flag": "1", "is_activated": "1"}, {"id": "193", "name": "Sao Tome and Principe", "code": "ST", "phone": "678", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Sao Tome and Principe\", \"cn\": \"圣多美和普林西比\", \"de\": \"São Tomé und Príncipe\", \"es\": \"Santo Tomé y Príncipe\", \"fa\": \"کواترو دو فرویرو\", \"fr\": \"Sao Tomé-et-Principe\", \"hr\": \"Sveti Toma i Princip\", \"it\": \"São Tomé e Príncipe\", \"ja\": \"サントメ・プリンシペ\", \"kr\": \"상투메 프린시페\", \"nl\": \"Sao Tomé en Principe\", \"pt\": \"São Tomé e Príncipe\", \"tr\": \"Sao Tome Ve Prinsipe\", \"pt-BR\": \"São Tomé e Príncipe\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Sao_Tome\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "678", "iso3": "STP", "nationality": "Sao Tomean", "capital": "Sao Tome", "tld": ".st", "native": "São Tomé e Príncipe", "region": "Africa", "currency": "STD", "currency_name": "Dobra", "currency_symbol": "Db", "wikiDataId": null, "lat": "1.00", "lng": "7.00", "emoji": "🇸🇹", "emojiU": "U+1F1F8 U+1F1F9", "flag": "1", "is_activated": "1"}, {"id": "194", "name": "Saudi Arabia", "code": "SA", "phone": "682", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Saudi Arabia\", \"cn\": \"沙特阿拉伯\", \"de\": \"Saudi-Arabien\", \"es\": \"Arabia Saudí\", \"fa\": \"عربستان سعودی\", \"fr\": \"Arabie Saoudite\", \"hr\": \"Saudijska Arabija\", \"it\": \"Arabia Saudita\", \"ja\": \"サウジアラビア\", \"kr\": \"사우디아라비아\", \"nl\": \"Saoedi-Arabië\", \"pt\": \"Arábia Saudita\", \"tr\": \"Suudi Arabistan\", \"pt-BR\": \"Arábia Saudita\"}", "timezones": "[{\"tzName\": \"Arabia Standard Time\", \"zoneName\": \"Asia/Riyadh\", \"gmtOffset\": 10800, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC+03:00\"}]", "numeric_code": "682", "iso3": "SAU", "nationality": "Saudi, Saudi Arabian", "capital": "Riyadh", "tld": ".sa", "native": "المملكة العربية السعودية", "region": "Asia", "currency": "SAR", "currency_name": "Saudi riyal", "currency_symbol": "﷼", "wikiDataId": null, "lat": "25.00", "lng": "45.00", "emoji": "🇸🇦", "emojiU": "U+1F1F8 U+1F1E6", "flag": "1", "is_activated": "1"}, {"id": "195", "name": "Senegal", "code": "SN", "phone": "686", "created_at": "2020-06-17 10:37:04", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Senegal\", \"cn\": \"塞内加尔\", \"de\": \"Senegal\", \"es\": \"Senegal\", \"fa\": \"سنگال\", \"fr\": \"Sénégal\", \"hr\": \"Senegal\", \"it\": \"Senegal\", \"ja\": \"セネガル\", \"kr\": \"세네갈\", \"nl\": \"Senegal\", \"pt\": \"Senegal\", \"tr\": \"Senegal\", \"pt-BR\": \"Senegal\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Dakar\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "686", "iso3": "SEN", "nationality": "Senegalese", "capital": "<PERSON><PERSON>", "tld": ".sn", "native": "Sénégal", "region": "Africa", "currency": "XOF", "currency_name": "West African CFA franc", "currency_symbol": "CFA", "wikiDataId": null, "lat": "14.00", "lng": "-14.00", "emoji": "🇸🇳", "emojiU": "U+1F1F8 U+1F1F3", "flag": "1", "is_activated": "1"}, {"id": "196", "name": "Serbia", "code": "RS", "phone": "688", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Serbia\", \"cn\": \"塞尔维亚\", \"de\": \"Serbien\", \"es\": \"Serbia\", \"fa\": \"صربستان\", \"fr\": \"Serbie\", \"hr\": \"<PERSON><PERSON><PERSON>\", \"it\": \"Serbia\", \"ja\": \"セルビア\", \"kr\": \"세르비아\", \"nl\": \"Servië\", \"pt\": \"<PERSON><PERSON>r<PERSON>\", \"tr\": \"Sirbistan\", \"pt-BR\": \"<PERSON><PERSON>r<PERSON>\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Belgrade\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "688", "iso3": "SRB", "nationality": "Serbian", "capital": "Belgrade", "tld": ".rs", "native": "Србија", "region": "Europe", "currency": "RSD", "currency_name": "Serbian dinar", "currency_symbol": "din", "wikiDataId": null, "lat": "44.00", "lng": "21.00", "emoji": "🇷🇸", "emojiU": "U+1F1F7 U+1F1F8", "flag": "1", "is_activated": "1"}, {"id": "197", "name": "Seychelles", "code": "SC", "phone": "690", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Seychelles\", \"cn\": \"塞舌尔\", \"de\": \"Seychellen\", \"es\": \"Seychelles\", \"fa\": \"سیشل\", \"fr\": \"Seychelles\", \"hr\": \"<PERSON><PERSON><PERSON><PERSON>\", \"it\": \"Seychelles\", \"ja\": \"セーシェル\", \"kr\": \"세이셸\", \"nl\": \"Seychellen\", \"pt\": \"Seicheles\", \"tr\": \"Seyşeller\", \"pt-BR\": \"Seiche<PERSON>\"}", "timezones": "[{\"tzName\": \"Seychelles Time\", \"zoneName\": \"Indian/Mahe\", \"gmtOffset\": 14400, \"abbreviation\": \"SCT\", \"gmtOffsetName\": \"UTC+04:00\"}]", "numeric_code": "690", "iso3": "SYC", "nationality": "<PERSON><PERSON><PERSON><PERSON>", "capital": "Victoria", "tld": ".sc", "native": "Seychelles", "region": "Africa", "currency": "SCR", "currency_name": "Seychellois rupee", "currency_symbol": "SRe", "wikiDataId": null, "lat": "-4.58", "lng": "55.67", "emoji": "🇸🇨", "emojiU": "U+1F1F8 U+1F1E8", "flag": "1", "is_activated": "1"}, {"id": "198", "name": "Sierra Leone", "code": "SL", "phone": "694", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Sierra Leone\", \"cn\": \"塞拉利昂\", \"de\": \"Sierra Leone\", \"es\": \"Sierra Leone\", \"fa\": \"سیرالئون\", \"fr\": \"Sierra Leone\", \"hr\": \"Sijera Leone\", \"it\": \"Sierra Leone\", \"ja\": \"シエラレオネ\", \"kr\": \"시에라리온\", \"nl\": \"Sierra Leone\", \"pt\": \"Serra Leoa\", \"tr\": \"Sierra Leone\", \"pt-BR\": \"Serra Leoa\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Freetown\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "694", "iso3": "SLE", "nationality": "Sierra Leonean", "capital": "Freetown", "tld": ".sl", "native": "Sierra Leone", "region": "Africa", "currency": "SLL", "currency_name": "Sierra Leonean leone", "currency_symbol": "Le", "wikiDataId": null, "lat": "8.50", "lng": "-11.50", "emoji": "🇸🇱", "emojiU": "U+1F1F8 U+1F1F1", "flag": "1", "is_activated": "1"}, {"id": "199", "name": "Singapore", "code": "SG", "phone": "702", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Singapore\", \"cn\": \"新加坡\", \"de\": \"Singapur\", \"es\": \"Singapur\", \"fa\": \"سنگاپور\", \"fr\": \"Singapour\", \"hr\": \"Singapur\", \"it\": \"Singapore\", \"ja\": \"シンガポール\", \"kr\": \"싱가포르\", \"nl\": \"Singapore\", \"pt\": \"Singapura\", \"tr\": \"Singapur\", \"pt-BR\": \"Singapura\"}", "timezones": "[{\"tzName\": \"Singapore Time\", \"zoneName\": \"Asia/Singapore\", \"gmtOffset\": 28800, \"abbreviation\": \"SGT\", \"gmtOffsetName\": \"UTC+08:00\"}]", "numeric_code": "702", "iso3": "SGP", "nationality": "Singaporean", "capital": "Singapur", "tld": ".sg", "native": "Singapore", "region": "Asia", "currency": "SGD", "currency_name": "Singapore dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "1.37", "lng": "103.80", "emoji": "🇸🇬", "emojiU": "U+1F1F8 U+1F1EC", "flag": "1", "is_activated": "1"}, {"id": "200", "name": "<PERSON><PERSON> Ma<PERSON>n (Dutch part)", "code": "SX", "phone": "534", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Sint Maarten (Dutch part)\", \"cn\": \"圣马丁岛（荷兰部分）\", \"de\": \"Sint Maarten (niederl. Teil)\", \"fa\": \"سینت مارتن\", \"fr\": \"<PERSON> (partie néerlandaise)\", \"it\": \"<PERSON> (parte olandese)\", \"kr\": \"신트마르턴\", \"nl\": \"Sint Maarten\", \"pt\": \"São Martinho\", \"tr\": \"Sint Maarten\", \"pt-BR\": \"Sint Maarten\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Anguilla\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "534", "iso3": "SXM", "nationality": "Sint Maarten", "capital": "Philipsburg", "tld": ".sx", "native": "Sint Maarten", "region": "Americas", "currency": "ANG", "currency_name": "Netherlands Antillean guilder", "currency_symbol": "ƒ", "wikiDataId": null, "lat": "18.03", "lng": "-63.05", "emoji": "🇸🇽", "emojiU": "U+1F1F8 U+1F1FD", "flag": "1", "is_activated": "1"}, {"id": "201", "name": "Slovakia", "code": "SK", "phone": "703", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Slovakia\", \"cn\": \"斯洛伐克\", \"de\": \"Slowakei\", \"es\": \"República Eslovaca\", \"fa\": \"اسلواکی\", \"fr\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"hr\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"it\": \"<PERSON><PERSON><PERSON><PERSON>\", \"ja\": \"スロバキア\", \"kr\": \"슬로바키아\", \"nl\": \"Slowaki<PERSON>\", \"pt\": \"Eslováquia\", \"tr\": \"Slovakya\", \"pt-BR\": \"Eslováquia\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Bratislava\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "703", "iso3": "SVK", "nationality": "Slovak", "capital": "Bratislava", "tld": ".sk", "native": "Slovensko", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "48.67", "lng": "19.50", "emoji": "🇸🇰", "emojiU": "U+1F1F8 U+1F1F0", "flag": "1", "is_activated": "1"}, {"id": "202", "name": "Slovenia", "code": "SI", "phone": "705", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Slovenia\", \"cn\": \"斯洛文尼亚\", \"de\": \"Slowenien\", \"es\": \"Eslovenia\", \"fa\": \"اسلوونی\", \"fr\": \"Slovénie\", \"hr\": \"Slovenija\", \"it\": \"Slovenia\", \"ja\": \"スロベニア\", \"kr\": \"슬로베니아\", \"nl\": \"Slovenië\", \"pt\": \"Eslovénia\", \"tr\": \"Slovenya\", \"pt-BR\": \"Eslovênia\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Ljubljana\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "705", "iso3": "SVN", "nationality": "Slovenian, Slovene", "capital": "Ljubljana", "tld": ".si", "native": "Slovenija", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "46.12", "lng": "14.82", "emoji": "🇸🇮", "emojiU": "U+1F1F8 U+1F1EE", "flag": "1", "is_activated": "1"}, {"id": "203", "name": "Solomon Islands", "code": "SB", "phone": "090", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Solomon Islands\", \"cn\": \"所罗门群岛\", \"de\": \"Salomonen\", \"es\": \"<PERSON>las Salomón\", \"fa\": \"جزایر سلیمان\", \"fr\": \"Îles Salomon\", \"hr\": \"Solomonski Otoci\", \"it\": \"Isole Salomone\", \"ja\": \"ソロモン諸島\", \"kr\": \"솔로몬 제도\", \"nl\": \"Salomonseilanden\", \"pt\": \"<PERSON>has Salomão\", \"tr\": \"Solomon Adalari\", \"pt-BR\": \"<PERSON><PERSON> Salom<PERSON>\"}", "timezones": "[{\"tzName\": \"Solomon Islands Time\", \"zoneName\": \"Pacific/Guadalcanal\", \"gmtOffset\": 39600, \"abbreviation\": \"SBT\", \"gmtOffsetName\": \"UTC+11:00\"}]", "numeric_code": "090", "iso3": "SLB", "nationality": "Solomon Island", "capital": "Honiara", "tld": ".sb", "native": "Solomon Islands", "region": "Oceania", "currency": "SBD", "currency_name": "Solomon Islands dollar", "currency_symbol": "Si$", "wikiDataId": null, "lat": "-8.00", "lng": "159.00", "emoji": "🇸🇧", "emojiU": "U+1F1F8 U+1F1E7", "flag": "1", "is_activated": "1"}, {"id": "204", "name": "Somalia", "code": "SO", "phone": "706", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Somalia\", \"cn\": \"索马里\", \"de\": \"Somalia\", \"es\": \"Somalia\", \"fa\": \"سومالی\", \"fr\": \"Somalie\", \"hr\": \"Somalija\", \"it\": \"Somalia\", \"ja\": \"ソマリア\", \"kr\": \"소말리아\", \"nl\": \"Somalië\", \"pt\": \"Somália\", \"tr\": \"Somali\", \"pt-BR\": \"Somália\"}", "timezones": "[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Africa/Mogadishu\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]", "numeric_code": "706", "iso3": "SOM", "nationality": "Somali, Somalian", "capital": "Mogadishu", "tld": ".so", "native": "<PERSON><PERSON><PERSON><PERSON>", "region": "Africa", "currency": "SOS", "currency_name": "Somali shilling", "currency_symbol": "Sh.so.", "wikiDataId": null, "lat": "10.00", "lng": "49.00", "emoji": "🇸🇴", "emojiU": "U+1F1F8 U+1F1F4", "flag": "1", "is_activated": "1"}, {"id": "205", "name": "South Africa", "code": "ZA", "phone": "710", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"South Africa\", \"cn\": \"南非\", \"de\": \"Republik Südafrika\", \"es\": \"República de Sudáfrica\", \"fa\": \"آفریقای جنوبی\", \"fr\": \"<PERSON><PERSON>rique du Sud\", \"hr\": \"Južnoafrička Republika\", \"it\": \"Sud Africa\", \"ja\": \"南アフリカ\", \"kr\": \"남아프리카 공화국\", \"nl\": \"Zuid-Afrika\", \"pt\": \"República Sul-Africana\", \"tr\": \"Güney Afrika Cumhuriyeti\", \"pt-BR\": \"República Sul-Africana\"}", "timezones": "[{\"tzName\": \"South African Standard Time\", \"zoneName\": \"Africa/Johannesburg\", \"gmtOffset\": 7200, \"abbreviation\": \"SAST\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "710", "iso3": "ZAF", "nationality": "South African", "capital": "Pretoria", "tld": ".za", "native": "South Africa", "region": "Africa", "currency": "ZAR", "currency_name": "South African rand", "currency_symbol": "R", "wikiDataId": null, "lat": "-29.00", "lng": "24.00", "emoji": "🇿🇦", "emojiU": "U+1F1FF U+1F1E6", "flag": "1", "is_activated": "1"}, {"id": "206", "name": "South Georgia", "code": "GS", "phone": "239", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"South Georgia\", \"cn\": \"南乔治亚\", \"de\": \"Südgeorgien und die Südlichen Sandwichinseln\", \"es\": \"Islas Georgias del Sur y Sandwich del Sur\", \"fa\": \"جزایر جورجیای جنوبی و ساندویچ جنوبی\", \"fr\": \"Géorgie du Sud-et-les Îles Sandwich du Sud\", \"hr\": \"Južna Georgija i otočje Južni Sandwich\", \"it\": \"Georgia del Sud e Isole Sandwich Meridionali\", \"ja\": \"サウスジョージア・サウスサンドウィッチ諸島\", \"kr\": \"사우스조지아\", \"nl\": \"Zuid-Georgia en Zuidelijke Sandwicheilanden\", \"pt\": \"Ilhas Geórgia do Sul e Sanduíche do Sul\", \"tr\": \"Güney Georgia\", \"pt-BR\": \"Ilhas Geórgias do Sul e Sandwich do Sul\"}", "timezones": "[{\"tzName\": \"South Georgia and the South Sandwich Islands Time\", \"zoneName\": \"Atlantic/South_Georgia\", \"gmtOffset\": -7200, \"abbreviation\": \"GST\", \"gmtOffsetName\": \"UTC-02:00\"}]", "numeric_code": "239", "iso3": "SGS", "nationality": "South Georgia or South Sandwich Islands", "capital": "Grytviken", "tld": ".gs", "native": "South Georgia", "region": "Americas", "currency": "GBP", "currency_name": "British pound", "currency_symbol": "£", "wikiDataId": null, "lat": "-54.50", "lng": "-37.00", "emoji": "🇬🇸", "emojiU": "U+1F1EC U+1F1F8", "flag": "1", "is_activated": "1"}, {"id": "207", "name": "South Sudan", "code": "SS", "phone": "728", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"South Sudan\", \"cn\": \"南苏丹\", \"de\": \"Südsudan\", \"es\": \"Sudán del Sur\", \"fa\": \"سودان جنوبی\", \"fr\": \"Soudan du Sud\", \"hr\": \"Južni Sudan\", \"it\": \"Sudan del sud\", \"ja\": \"南スーダン\", \"kr\": \"남수단\", \"nl\": \"Zuid-Soedan\", \"pt\": \"Sudão do Sul\", \"tr\": \"Güney Sudan\", \"pt-BR\": \"Sudão do Sul\"}", "timezones": "[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Africa/Juba\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]", "numeric_code": "728", "iso3": "SSD", "nationality": "South Sudanese", "capital": "Juba", "tld": ".ss", "native": "South Sudan", "region": "Africa", "currency": "SSP", "currency_name": "South Sudanese pound", "currency_symbol": "£", "wikiDataId": null, "lat": "7.00", "lng": "30.00", "emoji": "🇸🇸", "emojiU": "U+1F1F8 U+1F1F8", "flag": "1", "is_activated": "1"}, {"id": "208", "name": "Spain", "code": "ES", "phone": "724", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Spain\", \"cn\": \"西班牙\", \"de\": \"Spanien\", \"es\": \"España\", \"fa\": \"اسپانیا\", \"fr\": \"Espagne\", \"hr\": \"Španjols<PERSON>\", \"it\": \"Spagna\", \"ja\": \"スペイン\", \"kr\": \"스페인\", \"nl\": \"Spanje\", \"pt\": \"Espanha\", \"tr\": \"İspanya\", \"pt-BR\": \"Espanha\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Africa/Ceuta\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}, {\"tzName\": \"Western European Time\", \"zoneName\": \"Atlantic/Canary\", \"gmtOffset\": 0, \"abbreviation\": \"WET\", \"gmtOffsetName\": \"UTC±00\"}, {\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Madrid\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "724", "iso3": "ESP", "nationality": "Spanish", "capital": "Madrid", "tld": ".es", "native": "España", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "40.00", "lng": "-4.00", "emoji": "🇪🇸", "emojiU": "U+1F1EA U+1F1F8", "flag": "1", "is_activated": "1"}, {"id": "209", "name": "Sri Lanka", "code": "LK", "phone": "144", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Sri Lanka\", \"cn\": \"斯里兰卡\", \"de\": \"Sri Lanka\", \"es\": \"Sri Lanka\", \"fa\": \"سری‌لانکا\", \"fr\": \"Sri Lanka\", \"hr\": \"Šri Lanka\", \"it\": \"Sri Lanka\", \"ja\": \"スリランカ\", \"kr\": \"스리랑카\", \"nl\": \"Sri Lanka\", \"pt\": \"Sri Lanka\", \"tr\": \"Sri Lanka\", \"pt-BR\": \"Sri Lanka\"}", "timezones": "[{\"tzName\": \"Indian Standard Time\", \"zoneName\": \"Asia/Colombo\", \"gmtOffset\": 19800, \"abbreviation\": \"IST\", \"gmtOffsetName\": \"UTC+05:30\"}]", "numeric_code": "144", "iso3": "LKA", "nationality": "Sri Lankan", "capital": "Colombo", "tld": ".lk", "native": "śrī la<PERSON>va", "region": "Asia", "currency": "LKR", "currency_name": "Sri Lankan rupee", "currency_symbol": "Rs", "wikiDataId": null, "lat": "7.00", "lng": "81.00", "emoji": "🇱🇰", "emojiU": "U+1F1F1 U+1F1F0", "flag": "1", "is_activated": "1"}, {"id": "210", "name": "Sudan", "code": "SD", "phone": "729", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Sudan\", \"cn\": \"苏丹\", \"de\": \"Sudan\", \"es\": \"Sudán\", \"fa\": \"سودان\", \"fr\": \"Soudan\", \"hr\": \"Sudan\", \"it\": \"Sudan\", \"ja\": \"スーダン\", \"kr\": \"수단\", \"nl\": \"Soedan\", \"pt\": \"Sudão\", \"tr\": \"Sudan\", \"pt-BR\": \"Sudão\"}", "timezones": "[{\"tzName\": \"Eastern African Time\", \"zoneName\": \"Africa/Khartoum\", \"gmtOffset\": 7200, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "729", "iso3": "SDN", "nationality": "Sudanese", "capital": "Khartoum", "tld": ".sd", "native": "السودان", "region": "Africa", "currency": "SDG", "currency_name": "Sudanese pound", "currency_symbol": ".س.ج", "wikiDataId": null, "lat": "15.00", "lng": "30.00", "emoji": "🇸🇩", "emojiU": "U+1F1F8 U+1F1E9", "flag": "1", "is_activated": "1"}, {"id": "211", "name": "Suriname", "code": "SR", "phone": "740", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Suriname\", \"cn\": \"苏里南\", \"de\": \"Suriname\", \"es\": \"Surinam\", \"fa\": \"سورینام\", \"fr\": \"Surinam\", \"hr\": \"Surinam\", \"it\": \"Suriname\", \"ja\": \"スリナム\", \"kr\": \"수리남\", \"nl\": \"Suriname\", \"pt\": \"Suriname\", \"tr\": \"Surinam\", \"pt-BR\": \"Suriname\"}", "timezones": "[{\"tzName\": \"Suriname Time\", \"zoneName\": \"America/Paramaribo\", \"gmtOffset\": -10800, \"abbreviation\": \"SRT\", \"gmtOffsetName\": \"UTC-03:00\"}]", "numeric_code": "740", "iso3": "SUR", "nationality": "Surinamese", "capital": "Paramaribo", "tld": ".sr", "native": "Suriname", "region": "Americas", "currency": "SRD", "currency_name": "Surinamese dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "4.00", "lng": "-56.00", "emoji": "🇸🇷", "emojiU": "U+1F1F8 U+1F1F7", "flag": "1", "is_activated": "1"}, {"id": "212", "name": "Svalbard And Jan <PERSON> Islands", "code": "SJ", "phone": "744", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Svalbard And Jan Mayen Islands\", \"cn\": \"斯瓦尔巴和扬马延群岛\", \"de\": \"Svalbard und Jan Mayen\", \"es\": \"Islas Svalbard y Jan Mayen\", \"fa\": \"سوالبارد و یان ماین\", \"fr\": \"Svalbard et Jan Mayen\", \"hr\": \"Svalbard i Jan Mayen\", \"it\": \"Svalbard e Jan Mayen\", \"ja\": \"スヴァールバル諸島およびヤンマイエン島\", \"kr\": \"스발바르 얀마옌 제도\", \"nl\": \"Svalbard en Jan Mayen\", \"pt\": \"Svalbard\", \"tr\": \"Svalbard Ve Jan Mayen\", \"pt-BR\": \"Svalbard\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Arctic/Longyearbyen\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "744", "iso3": "SJM", "nationality": "Svalbard", "capital": "Longyearbyen", "tld": ".sj", "native": "Svalbard og Jan Mayen", "region": "Europe", "currency": "NOK", "currency_name": "Norwegian Krone", "currency_symbol": "kr", "wikiDataId": null, "lat": "78.00", "lng": "20.00", "emoji": "🇸🇯", "emojiU": "U+1F1F8 U+1F1EF", "flag": "1", "is_activated": "1"}, {"id": "213", "name": "Swaziland", "code": "SZ", "phone": "748", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Swaziland\", \"cn\": \"斯威士兰\", \"de\": \"Swasiland\", \"es\": \"Suazilandia\", \"fa\": \"سوازیلند\", \"fr\": \"Swaziland\", \"hr\": \"Svazi\", \"it\": \"Swaziland\", \"ja\": \"スワジランド\", \"kr\": \"에스와티니\", \"nl\": \"Swaziland\", \"pt\": \"Suazilândia\", \"tr\": \"Esvatini\", \"pt-BR\": \"Suazilândia\"}", "timezones": "[{\"tzName\": \"South African Standard Time\", \"zoneName\": \"Africa/Mbabane\", \"gmtOffset\": 7200, \"abbreviation\": \"SAST\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "748", "iso3": "SWZ", "nationality": "Swazi", "capital": "Mbabane", "tld": ".sz", "native": "Swaziland", "region": "Africa", "currency": "SZL", "currency_name": "<PERSON><PERSON><PERSON>", "currency_symbol": "E", "wikiDataId": null, "lat": "-26.50", "lng": "31.50", "emoji": "🇸🇿", "emojiU": "U+1F1F8 U+1F1FF", "flag": "1", "is_activated": "1"}, {"id": "214", "name": "Sweden", "code": "SE", "phone": "752", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Sweden\", \"cn\": \"瑞典\", \"de\": \"Schweden\", \"es\": \"Sue<PERSON>\", \"fa\": \"سوئد\", \"fr\": \"Suède\", \"hr\": \"<PERSON><PERSON><PERSON>\", \"it\": \"Svezia\", \"ja\": \"スウェーデン\", \"kr\": \"스웨덴\", \"nl\": \"Zweden\", \"pt\": \"Su<PERSON>cia\", \"tr\": \"İsveç\", \"pt-BR\": \"Suécia\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Stockholm\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "752", "iso3": "SWE", "nationality": "Swedish", "capital": "Stockholm", "tld": ".se", "native": "Sverige", "region": "Europe", "currency": "SEK", "currency_name": "Swedish krona", "currency_symbol": "kr", "wikiDataId": null, "lat": "62.00", "lng": "15.00", "emoji": "🇸🇪", "emojiU": "U+1F1F8 U+1F1EA", "flag": "1", "is_activated": "1"}, {"id": "215", "name": "Switzerland", "code": "CH", "phone": "756", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Switzerland\", \"cn\": \"瑞士\", \"de\": \"<PERSON><PERSON><PERSON>z\", \"es\": \"Su<PERSON>\", \"fa\": \"سوئیس\", \"fr\": \"Suisse\", \"hr\": \"<PERSON>vicars<PERSON>\", \"it\": \"<PERSON><PERSON><PERSON><PERSON>\", \"ja\": \"スイス\", \"kr\": \"스위스\", \"nl\": \"Zwitserland\", \"pt\": \"Suíça\", \"tr\": \"İsviçre\", \"pt-BR\": \"Suíça\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Zurich\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "756", "iso3": "CHE", "nationality": "Swiss", "capital": "Bern", "tld": ".ch", "native": "Schweiz", "region": "Europe", "currency": "CHF", "currency_name": "Swiss franc", "currency_symbol": "CHf", "wikiDataId": null, "lat": "47.00", "lng": "8.00", "emoji": "🇨🇭", "emojiU": "U+1F1E8 U+1F1ED", "flag": "1", "is_activated": "1"}, {"id": "216", "name": "Syria", "code": "SY", "phone": "760", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Syria\", \"cn\": \"叙利亚\", \"de\": \"<PERSON>yrien\", \"es\": \"<PERSON><PERSON>\", \"fa\": \"سوریه\", \"fr\": \"<PERSON>yrie\", \"hr\": \"<PERSON><PERSON>\", \"it\": \"<PERSON><PERSON>\", \"ja\": \"シリア・アラブ共和国\", \"kr\": \"시리아\", \"nl\": \"Syrië\", \"pt\": \"<PERSON><PERSON><PERSON>\", \"tr\": \"Suriye\", \"pt-BR\": \"<PERSON><PERSON><PERSON>\"}", "timezones": "[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Asia/Damascus\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "760", "iso3": "SYR", "nationality": "Syrian", "capital": "Damascus", "tld": ".sy", "native": "سوريا", "region": "Asia", "currency": "SYP", "currency_name": "Syrian pound", "currency_symbol": "LS", "wikiDataId": null, "lat": "35.00", "lng": "38.00", "emoji": "🇸🇾", "emojiU": "U+1F1F8 U+1F1FE", "flag": "1", "is_activated": "1"}, {"id": "217", "name": "Taiwan", "code": "TW", "phone": "158", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Taiwan\", \"cn\": \"中国台湾\", \"de\": \"Taiwan\", \"es\": \"Taiwán\", \"fa\": \"تایوان\", \"fr\": \"Taïwan\", \"hr\": \"Tajvan\", \"it\": \"Taiwan\", \"ja\": \"台湾（中華民国）\", \"kr\": \"대만\", \"nl\": \"Taiwan\", \"pt\": \"Taiwan\", \"tr\": \"Tayvan\", \"pt-BR\": \"Taiwan\"}", "timezones": "[{\"tzName\": \"China Standard Time\", \"zoneName\": \"Asia/Taipei\", \"gmtOffset\": 28800, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC+08:00\"}]", "numeric_code": "158", "iso3": "TWN", "nationality": "Chinese, Taiwanese", "capital": "Taipei", "tld": ".tw", "native": "臺灣", "region": "Asia", "currency": "TWD", "currency_name": "New Taiwan dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "23.50", "lng": "121.00", "emoji": "🇹🇼", "emojiU": "U+1F1F9 U+1F1FC", "flag": "1", "is_activated": "1"}, {"id": "218", "name": "Tajikistan", "code": "TJ", "phone": "762", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Tajikistan\", \"cn\": \"塔吉克斯坦\", \"de\": \"Tadschikistan\", \"es\": \"Tayikistán\", \"fa\": \"تاجیکستان\", \"fr\": \"Tadjikistan\", \"hr\": \"Tađikistan\", \"it\": \"Tagikistan\", \"ja\": \"タジキスタン\", \"kr\": \"타지키스탄\", \"nl\": \"Tadzjikistan\", \"pt\": \"Tajiquistão\", \"tr\": \"Tacikistan\", \"pt-BR\": \"Tajiquistão\"}", "timezones": "[{\"tzName\": \"Tajikistan Time\", \"zoneName\": \"Asia/Dushanbe\", \"gmtOffset\": 18000, \"abbreviation\": \"TJT\", \"gmtOffsetName\": \"UTC+05:00\"}]", "numeric_code": "762", "iso3": "TJK", "nationality": "Tajikistani", "capital": "<PERSON><PERSON><PERSON>", "tld": ".tj", "native": "Тоҷикистон", "region": "Asia", "currency": "TJS", "currency_name": "<PERSON>i somoni", "currency_symbol": "SM", "wikiDataId": null, "lat": "39.00", "lng": "71.00", "emoji": "🇹🇯", "emojiU": "U+1F1F9 U+1F1EF", "flag": "1", "is_activated": "1"}, {"id": "219", "name": "Tanzania", "code": "TZ", "phone": "834", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Tanzania\", \"cn\": \"坦桑尼亚\", \"de\": \"Tansania\", \"es\": \"Tanzania\", \"fa\": \"تانزانیا\", \"fr\": \"Tanzanie\", \"hr\": \"Tanzanija\", \"it\": \"Tanzania\", \"ja\": \"タンザニア\", \"kr\": \"탄자니아\", \"nl\": \"Tanzania\", \"pt\": \"Tanzânia\", \"tr\": \"Tanzanya\", \"pt-BR\": \"Tanzânia\"}", "timezones": "[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Africa/Dar_es_Salaam\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]", "numeric_code": "834", "iso3": "TZA", "nationality": "Tanzanian", "capital": "Dodoma", "tld": ".tz", "native": "Tanzania", "region": "Africa", "currency": "TZS", "currency_name": "Tanzanian shilling", "currency_symbol": "TSh", "wikiDataId": null, "lat": "-6.00", "lng": "35.00", "emoji": "🇹🇿", "emojiU": "U+1F1F9 U+1F1FF", "flag": "1", "is_activated": "1"}, {"id": "220", "name": "Thailand", "code": "TH", "phone": "764", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Thailand\", \"cn\": \"泰国\", \"de\": \"Thailand\", \"es\": \"Tailandia\", \"fa\": \"تایلند\", \"fr\": \"Thaïlande\", \"hr\": \"Tajland\", \"it\": \"Tailandia\", \"ja\": \"タイ\", \"kr\": \"태국\", \"nl\": \"Thailand\", \"pt\": \"Tailândia\", \"tr\": \"Tayland\", \"pt-BR\": \"Tailândia\"}", "timezones": "[{\"tzName\": \"Indochina Time\", \"zoneName\": \"Asia/Bangkok\", \"gmtOffset\": 25200, \"abbreviation\": \"ICT\", \"gmtOffsetName\": \"UTC+07:00\"}]", "numeric_code": "764", "iso3": "THA", "nationality": "Thai", "capital": "Bangkok", "tld": ".th", "native": "ประเทศไทย", "region": "Asia", "currency": "THB", "currency_name": "Thai baht", "currency_symbol": "฿", "wikiDataId": null, "lat": "15.00", "lng": "100.00", "emoji": "🇹🇭", "emojiU": "U+1F1F9 U+1F1ED", "flag": "1", "is_activated": "1"}, {"id": "221", "name": "East Timor", "code": "TL", "phone": "626", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"East Timor\", \"cn\": \"东帝汶\", \"de\": \"Timor-Leste\", \"es\": \"Timor Oriental\", \"fa\": \"تیمور شرقی\", \"fr\": \"Timor oriental\", \"hr\": \"Istočni Timor\", \"it\": \"Timor Est\", \"ja\": \"東ティモール\", \"kr\": \"동티모르\", \"nl\": \"Oost-Timor\", \"pt\": \"Timor Leste\", \"tr\": \"Doğu Timor\", \"pt-BR\": \"Timor Leste\"}", "timezones": "[{\"tzName\": \"Timor Leste Time\", \"zoneName\": \"Asia/Dili\", \"gmtOffset\": 32400, \"abbreviation\": \"TLT\", \"gmtOffsetName\": \"UTC+09:00\"}]", "numeric_code": "626", "iso3": "TLS", "nationality": "Timorese", "capital": "<PERSON><PERSON>", "tld": ".tl", "native": "Timor-Leste", "region": "Asia", "currency": "USD", "currency_name": "United States dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "-8.83", "lng": "125.92", "emoji": "🇹🇱", "emojiU": "U+1F1F9 U+1F1F1", "flag": "1", "is_activated": "1"}, {"id": "222", "name": "Togo", "code": "TG", "phone": "768", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Togo\", \"cn\": \"多哥\", \"de\": \"Togo\", \"es\": \"Togo\", \"fa\": \"توگو\", \"fr\": \"Togo\", \"hr\": \"Togo\", \"it\": \"Togo\", \"ja\": \"トーゴ\", \"kr\": \"토고\", \"nl\": \"Togo\", \"pt\": \"Togo\", \"tr\": \"Togo\", \"pt-BR\": \"Togo\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Africa/Lome\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "768", "iso3": "TGO", "nationality": "Togolese", "capital": "<PERSON><PERSON>", "tld": ".tg", "native": "Togo", "region": "Africa", "currency": "XOF", "currency_name": "West African CFA franc", "currency_symbol": "CFA", "wikiDataId": null, "lat": "8.00", "lng": "1.17", "emoji": "🇹🇬", "emojiU": "U+1F1F9 U+1F1EC", "flag": "1", "is_activated": "1"}, {"id": "223", "name": "Tokelau", "code": "TK", "phone": "772", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Tokelau\", \"cn\": \"托克劳\", \"de\": \"Tokelau\", \"es\": \"<PERSON>las Tokelau\", \"fa\": \"توکلائو\", \"fr\": \"Tokelau\", \"hr\": \"Tokelau\", \"it\": \"Isole Tokelau\", \"ja\": \"トケラウ\", \"kr\": \"토켈라우\", \"nl\": \"Tokelau\", \"pt\": \"Toquelau\", \"tr\": \"Tokelau\", \"pt-BR\": \"Tokelau\"}", "timezones": "[{\"tzName\": \"Tokelau Time\", \"zoneName\": \"Pacific/Fakaofo\", \"gmtOffset\": 46800, \"abbreviation\": \"TKT\", \"gmtOffsetName\": \"UTC+13:00\"}]", "numeric_code": "772", "iso3": "TKL", "nationality": "Tokelauan", "capital": "", "tld": ".tk", "native": "Tokelau", "region": "Oceania", "currency": "NZD", "currency_name": "New Zealand dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "-9.00", "lng": "-172.00", "emoji": "🇹🇰", "emojiU": "U+1F1F9 U+1F1F0", "flag": "1", "is_activated": "1"}, {"id": "224", "name": "Tonga", "code": "TO", "phone": "776", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Tonga\", \"cn\": \"汤加\", \"de\": \"Tonga\", \"es\": \"Tonga\", \"fa\": \"تونگا\", \"fr\": \"Tonga\", \"hr\": \"Tonga\", \"it\": \"Tonga\", \"ja\": \"トンガ\", \"kr\": \"통가\", \"nl\": \"Tonga\", \"pt\": \"Tonga\", \"tr\": \"Tonga\", \"pt-BR\": \"Tonga\"}", "timezones": "[{\"tzName\": \"Tonga Time\", \"zoneName\": \"Pacific/Tongatapu\", \"gmtOffset\": 46800, \"abbreviation\": \"TOT\", \"gmtOffsetName\": \"UTC+13:00\"}]", "numeric_code": "776", "iso3": "TON", "nationality": "Tongan", "capital": "<PERSON><PERSON> alofa", "tld": ".to", "native": "Tonga", "region": "Oceania", "currency": "TOP", "currency_name": "Tongan paʻanga", "currency_symbol": "$", "wikiDataId": null, "lat": "-20.00", "lng": "-175.00", "emoji": "🇹🇴", "emojiU": "U+1F1F9 U+1F1F4", "flag": "1", "is_activated": "1"}, {"id": "225", "name": "Trinidad And Tobago", "code": "TT", "phone": "780", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Trinidad And Tobago\", \"cn\": \"特立尼达和多巴哥\", \"de\": \"Trinidad und Tobago\", \"es\": \"Trinidad y Tobago\", \"fa\": \"ترینیداد و توباگو\", \"fr\": \"Trinité et Tobago\", \"hr\": \"Trinidad i Tobago\", \"it\": \"Trinidad e Tobago\", \"ja\": \"トリニダード・トバゴ\", \"kr\": \"트리니다드 토바고\", \"nl\": \"Trinidad en Tobago\", \"pt\": \"Trindade e Tobago\", \"tr\": \"Trinidad Ve Tobago\", \"pt-BR\": \"Trinidad e Tobago\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Port_of_Spain\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "780", "iso3": "TTO", "nationality": "Trinidadian or Tobagonian", "capital": "Port of Spain", "tld": ".tt", "native": "Trinidad and Tobago", "region": "Americas", "currency": "TTD", "currency_name": "Trinidad and Tobago dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "11.00", "lng": "-61.00", "emoji": "🇹🇹", "emojiU": "U+1F1F9 U+1F1F9", "flag": "1", "is_activated": "1"}, {"id": "226", "name": "Tunisia", "code": "TN", "phone": "788", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Tunisia\", \"cn\": \"突尼斯\", \"de\": \"Tunesien\", \"es\": \"Túnez\", \"fa\": \"تونس\", \"fr\": \"Tunisie\", \"hr\": \"Tunis\", \"it\": \"Tunisia\", \"ja\": \"チュニジア\", \"kr\": \"튀니지\", \"nl\": \"Tunesië\", \"pt\": \"Tunísia\", \"tr\": \"Tunus\", \"pt-BR\": \"Tunísia\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Africa/Tunis\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "788", "iso3": "TUN", "nationality": "Tunisian", "capital": "<PERSON><PERSON>", "tld": ".tn", "native": "تونس", "region": "Africa", "currency": "TND", "currency_name": "Tunisian dinar", "currency_symbol": "ت.د", "wikiDataId": null, "lat": "34.00", "lng": "9.00", "emoji": "🇹🇳", "emojiU": "U+1F1F9 U+1F1F3", "flag": "1", "is_activated": "1"}, {"id": "227", "name": "Turkey", "code": "TR", "phone": "792", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Turkey\", \"cn\": \"土耳其\", \"de\": \"<PERSON>ürk<PERSON>\", \"es\": \"Turqu<PERSON>\", \"fa\": \"ترکیه\", \"fr\": \"<PERSON>rqui<PERSON>\", \"hr\": \"<PERSON><PERSON><PERSON>\", \"it\": \"<PERSON><PERSON><PERSON>\", \"ja\": \"トルコ\", \"kr\": \"터키\", \"nl\": \"Turkije\", \"pt\": \"Turquia\", \"tr\": \"Türkiye\", \"pt-BR\": \"Turquia\"}", "timezones": "[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Istanbul\", \"gmtOffset\": 10800, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+03:00\"}]", "numeric_code": "792", "iso3": "TUR", "nationality": "Turkish", "capital": "Ankara", "tld": ".tr", "native": "Türkiye", "region": "Asia", "currency": "TRY", "currency_name": "Turkish lira", "currency_symbol": "₺", "wikiDataId": null, "lat": "39.00", "lng": "35.00", "emoji": "🇹🇷", "emojiU": "U+1F1F9 U+1F1F7", "flag": "1", "is_activated": "1"}, {"id": "228", "name": "Turkmenistan", "code": "TM", "phone": "795", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Turkmenistan\", \"cn\": \"土库曼斯坦\", \"de\": \"Turkmenistan\", \"es\": \"Turkmenistán\", \"fa\": \"ترکمنستان\", \"fr\": \"Turkménistan\", \"hr\": \"Turkmenistan\", \"it\": \"Turkmenistan\", \"ja\": \"トルクメニスタン\", \"kr\": \"투르크메니스탄\", \"nl\": \"Turkmenistan\", \"pt\": \"Turquemenistão\", \"tr\": \"Türkmenistan\", \"pt-BR\": \"Turcomenistão\"}", "timezones": "[{\"tzName\": \"Turkmenistan Time\", \"zoneName\": \"Asia/Ashgabat\", \"gmtOffset\": 18000, \"abbreviation\": \"TMT\", \"gmtOffsetName\": \"UTC+05:00\"}]", "numeric_code": "795", "iso3": "TKM", "nationality": "Turkmen", "capital": "Ashgabat", "tld": ".tm", "native": "Türkmenistan", "region": "Asia", "currency": "TMT", "currency_name": "Turkmenistan manat", "currency_symbol": "T", "wikiDataId": null, "lat": "40.00", "lng": "60.00", "emoji": "🇹🇲", "emojiU": "U+1F1F9 U+1F1F2", "flag": "1", "is_activated": "1"}, {"id": "229", "name": "Turks And Caicos Islands", "code": "TC", "phone": "796", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Turks And Caicos Islands\", \"cn\": \"特克斯和凯科斯群岛\", \"de\": \"Turks- und Caicosinseln\", \"es\": \"Islas Turks y Caicos\", \"fa\": \"جزایر تورکس و کایکوس\", \"fr\": \"Îles Turques-et-Caïques\", \"hr\": \"Otoci Turks i Caicos\", \"it\": \"Isole Turks e Caicos\", \"ja\": \"タークス・カイコス諸島\", \"kr\": \"터크스 케이커스 제도\", \"nl\": \"Turks- en Caicoseilanden\", \"pt\": \"Ilhas Turcas e Caicos\", \"tr\": \"Turks Ve Caicos Adalari\", \"pt-BR\": \"Ilhas Turcas e Caicos\"}", "timezones": "[{\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Grand_Turk\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}]", "numeric_code": "796", "iso3": "TCA", "nationality": "Turks and Caicos Island", "capital": "Cockburn Town", "tld": ".tc", "native": "Turks and Caicos Islands", "region": "Americas", "currency": "USD", "currency_name": "United States dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "21.75", "lng": "-71.58", "emoji": "🇹🇨", "emojiU": "U+1F1F9 U+1F1E8", "flag": "1", "is_activated": "1"}, {"id": "230", "name": "Tuvalu", "code": "TV", "phone": "798", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Tuvalu\", \"cn\": \"图瓦卢\", \"de\": \"Tuvalu\", \"es\": \"Tuvalu\", \"fa\": \"تووالو\", \"fr\": \"Tuvalu\", \"hr\": \"Tuvalu\", \"it\": \"Tuvalu\", \"ja\": \"ツバル\", \"kr\": \"투발루\", \"nl\": \"Tuvalu\", \"pt\": \"Tuvalu\", \"tr\": \"Tuvalu\", \"pt-BR\": \"Tuvalu\"}", "timezones": "[{\"tzName\": \"Tuvalu Time\", \"zoneName\": \"Pacific/Funafuti\", \"gmtOffset\": 43200, \"abbreviation\": \"TVT\", \"gmtOffsetName\": \"UTC+12:00\"}]", "numeric_code": "798", "iso3": "TUV", "nationality": "Tuvaluan", "capital": "Funafuti", "tld": ".tv", "native": "Tuvalu", "region": "Oceania", "currency": "AUD", "currency_name": "Australian dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "-8.00", "lng": "178.00", "emoji": "🇹🇻", "emojiU": "U+1F1F9 U+1F1FB", "flag": "1", "is_activated": "1"}, {"id": "231", "name": "Uganda", "code": "UG", "phone": "800", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Uganda\", \"cn\": \"乌干达\", \"de\": \"Uganda\", \"es\": \"Uganda\", \"fa\": \"اوگاندا\", \"fr\": \"Uganda\", \"hr\": \"Uganda\", \"it\": \"Uganda\", \"ja\": \"ウガンダ\", \"kr\": \"우간다\", \"nl\": \"Oeganda\", \"pt\": \"Uganda\", \"tr\": \"Uganda\", \"pt-BR\": \"Uganda\"}", "timezones": "[{\"tzName\": \"East Africa Time\", \"zoneName\": \"Africa/Kampala\", \"gmtOffset\": 10800, \"abbreviation\": \"EAT\", \"gmtOffsetName\": \"UTC+03:00\"}]", "numeric_code": "800", "iso3": "UGA", "nationality": "Ugandan", "capital": "Kampala", "tld": ".ug", "native": "Uganda", "region": "Africa", "currency": "UGX", "currency_name": "Ugandan shilling", "currency_symbol": "USh", "wikiDataId": null, "lat": "1.00", "lng": "32.00", "emoji": "🇺🇬", "emojiU": "U+1F1FA U+1F1EC", "flag": "1", "is_activated": "1"}, {"id": "232", "name": "Ukraine", "code": "UA", "phone": "804", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Ukraine\", \"cn\": \"乌克兰\", \"de\": \"Ukraine\", \"es\": \"Ucrania\", \"fa\": \"وکراین\", \"fr\": \"Ukraine\", \"hr\": \"Ukrajina\", \"it\": \"<PERSON><PERSON>rain<PERSON>\", \"ja\": \"ウクライナ\", \"kr\": \"우크라이나\", \"nl\": \"Oekraïne\", \"pt\": \"Ucrânia\", \"tr\": \"Ukrayna\", \"pt-BR\": \"Ucrânia\"}", "timezones": "[{\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Kiev\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}, {\"tzName\": \"Moscow Time\", \"zoneName\": \"Europe/Simferopol\", \"gmtOffset\": 10800, \"abbreviation\": \"MSK\", \"gmtOffsetName\": \"UTC+03:00\"}, {\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Uzhgorod\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}, {\"tzName\": \"Eastern European Time\", \"zoneName\": \"Europe/Zaporozhye\", \"gmtOffset\": 7200, \"abbreviation\": \"EET\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "804", "iso3": "UKR", "nationality": "Ukrainian", "capital": "Kyiv", "tld": ".ua", "native": "Україна", "region": "Europe", "currency": "UAH", "currency_name": "Ukrainian hryvnia", "currency_symbol": "₴", "wikiDataId": null, "lat": "49.00", "lng": "32.00", "emoji": "🇺🇦", "emojiU": "U+1F1FA U+1F1E6", "flag": "1", "is_activated": "1"}, {"id": "233", "name": "United Arab Emirates", "code": "AE", "phone": "784", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"United Arab Emirates\", \"cn\": \"阿拉伯联合酋长国\", \"de\": \"Vereinigte Arabische Emirate\", \"es\": \"Emiratos Árabes Unidos\", \"fa\": \"امارات متحده عربی\", \"fr\": \"Émirats arabes unis\", \"hr\": \"Ujedinjeni Arapski Emirati\", \"it\": \"Emirati Arabi Uniti\", \"ja\": \"アラブ首長国連邦\", \"kr\": \"아랍에미리트\", \"nl\": \"Verenigde Arabische Emiraten\", \"pt\": \"Emirados árabes Unidos\", \"tr\": \"Birleşik Arap Emirlikleri\", \"pt-BR\": \"Emirados árabes Unidos\"}", "timezones": "[{\"tzName\": \"Gulf Standard Time\", \"zoneName\": \"Asia/Dubai\", \"gmtOffset\": 14400, \"abbreviation\": \"GST\", \"gmtOffsetName\": \"UTC+04:00\"}]", "numeric_code": "784", "iso3": "ARE", "nationality": "Emirati, Emirian, Emiri", "capital": "Abu Dhabi", "tld": ".ae", "native": "دولة الإمارات العربية المتحدة", "region": "Asia", "currency": "AED", "currency_name": "United Arab Emirates dirham", "currency_symbol": "<PERSON><PERSON>د", "wikiDataId": null, "lat": "24.00", "lng": "54.00", "emoji": "🇦🇪", "emojiU": "U+1F1E6 U+1F1EA", "flag": "1", "is_activated": "1"}, {"id": "234", "name": "United Kingdom", "code": "GB", "phone": "826", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"United Kingdom\", \"cn\": \"英国\", \"de\": \"Vereinigtes Königreich\", \"es\": \"Reino Unido\", \"fa\": \"بریتانیای کبیر و ایرلند شمالی\", \"fr\": \"Royaume-Uni\", \"hr\": \"Ujedinjeno Kraljevstvo\", \"it\": \"Regno Unito\", \"ja\": \"イギリス\", \"kr\": \"영국\", \"nl\": \"Verenigd Koninkrijk\", \"pt\": \"Reino Unido\", \"tr\": \"Birleşik Krallik\", \"pt-BR\": \"Reino Unido\"}", "timezones": "[{\"tzName\": \"Greenwich Mean Time\", \"zoneName\": \"Europe/London\", \"gmtOffset\": 0, \"abbreviation\": \"GMT\", \"gmtOffsetName\": \"UTC±00\"}]", "numeric_code": "826", "iso3": "GBR", "nationality": "British, UK", "capital": "London", "tld": ".uk", "native": "United Kingdom", "region": "Europe", "currency": "GBP", "currency_name": "British pound", "currency_symbol": "£", "wikiDataId": null, "lat": "54.00", "lng": "-2.00", "emoji": "🇬🇧", "emojiU": "U+1F1EC U+1F1E7", "flag": "1", "is_activated": "1"}, {"id": "235", "name": "United States", "code": "US", "phone": "840", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"United States\", \"cn\": \"美国\", \"de\": \"Vereinigte Staaten von Amerika\", \"es\": \"Estados Unidos\", \"fa\": \"ایالات متحده آمریکا\", \"fr\": \"États-Unis\", \"hr\": \"Sjedinjene Američke Države\", \"it\": \"Stati Uniti D America\", \"ja\": \"アメリカ合衆国\", \"kr\": \"미국\", \"nl\": \"Verenigde Staten\", \"pt\": \"Estados Unidos\", \"tr\": \"Amerika\", \"pt-BR\": \"Estados Unidos\"}", "timezones": "[{\"tzName\": \"Hawaii–Aleutian Standard Time\", \"zoneName\": \"America/Adak\", \"gmtOffset\": -36000, \"abbreviation\": \"HST\", \"gmtOffsetName\": \"UTC-10:00\"}, {\"tzName\": \"Alaska Standard Time\", \"zoneName\": \"America/Anchorage\", \"gmtOffset\": -32400, \"abbreviation\": \"AKST\", \"gmtOffsetName\": \"UTC-09:00\"}, {\"tzName\": \"Mountain Standard Time (North America\", \"zoneName\": \"America/Boise\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Chicago\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Mountain Standard Time (North America\", \"zoneName\": \"America/Denver\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Detroit\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Indiana/Indianapolis\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Indiana/Knox\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Indiana/Marengo\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Indiana/Petersburg\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Indiana/Tell_City\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Indiana/Vevay\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Indiana/Vincennes\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Indiana/Winamac\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Alaska Standard Time\", \"zoneName\": \"America/Juneau\", \"gmtOffset\": -32400, \"abbreviation\": \"AKST\", \"gmtOffsetName\": \"UTC-09:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Kentucky/Louisville\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/Kentucky/Monticello\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Pacific Standard Time (North America\", \"zoneName\": \"America/Los_Angeles\", \"gmtOffset\": -28800, \"abbreviation\": \"PST\", \"gmtOffsetName\": \"UTC-08:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/Menominee\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Alaska Standard Time\", \"zoneName\": \"America/Metlakatla\", \"gmtOffset\": -32400, \"abbreviation\": \"AKST\", \"gmtOffsetName\": \"UTC-09:00\"}, {\"tzName\": \"Eastern Standard Time (North America\", \"zoneName\": \"America/New_York\", \"gmtOffset\": -18000, \"abbreviation\": \"EST\", \"gmtOffsetName\": \"UTC-05:00\"}, {\"tzName\": \"Alaska Standard Time\", \"zoneName\": \"America/Nome\", \"gmtOffset\": -32400, \"abbreviation\": \"AKST\", \"gmtOffsetName\": \"UTC-09:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/North_Dakota/Beulah\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/North_Dakota/Center\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Central Standard Time (North America\", \"zoneName\": \"America/North_Dakota/New_Salem\", \"gmtOffset\": -21600, \"abbreviation\": \"CST\", \"gmtOffsetName\": \"UTC-06:00\"}, {\"tzName\": \"Mountain Standard Time (North America\", \"zoneName\": \"America/Phoenix\", \"gmtOffset\": -25200, \"abbreviation\": \"MST\", \"gmtOffsetName\": \"UTC-07:00\"}, {\"tzName\": \"Alaska Standard Time\", \"zoneName\": \"America/Sitka\", \"gmtOffset\": -32400, \"abbreviation\": \"AKST\", \"gmtOffsetName\": \"UTC-09:00\"}, {\"tzName\": \"Alaska Standard Time\", \"zoneName\": \"America/Yakutat\", \"gmtOffset\": -32400, \"abbreviation\": \"AKST\", \"gmtOffsetName\": \"UTC-09:00\"}, {\"tzName\": \"Hawaii–Aleutian Standard Time\", \"zoneName\": \"Pacific/Honolulu\", \"gmtOffset\": -36000, \"abbreviation\": \"HST\", \"gmtOffsetName\": \"UTC-10:00\"}]", "numeric_code": "840", "iso3": "USA", "nationality": "American", "capital": "Washington", "tld": ".us", "native": "United States", "region": "Americas", "currency": "USD", "currency_name": "United States dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "38.00", "lng": "-97.00", "emoji": "🇺🇸", "emojiU": "U+1F1FA U+1F1F8", "flag": "1", "is_activated": "1"}, {"id": "236", "name": "United States Minor Outlying Islands", "code": "UM", "phone": "581", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"United States Minor Outlying Islands\", \"cn\": \"美国本土外小岛屿\", \"de\": \"Kleinere Inselbesitzungen der Vereinigten Staaten\", \"es\": \"Islas Ultramarinas Menores de Estados Unidos\", \"fa\": \"جزایر کوچک حاشیه‌ای ایالات متحده آمریکا\", \"fr\": \"Îles mineures éloignées des États-Unis\", \"hr\": \"Mali udaljeni otoci SAD-a\", \"it\": \"Isole minori esterne degli Stati Uniti d America\", \"ja\": \"合衆国領有小離島\", \"kr\": \"미국령 군소 제도\", \"nl\": \"Kleine afgelegen eilanden van de Verenigde Staten\", \"pt\": \"Ilhas Menores Distantes dos Estados Unidos\", \"tr\": \"Abd Küçük Harici Adalari\", \"pt-BR\": \"Ilhas Menores Distantes dos Estados Unidos\"}", "timezones": "[{\"tzName\": \"Samoa Standard Time\", \"zoneName\": \"Pacific/Midway\", \"gmtOffset\": -39600, \"abbreviation\": \"SST\", \"gmtOffsetName\": \"UTC-11:00\"}, {\"tzName\": \"Wake Island Time\", \"zoneName\": \"Pacific/Wake\", \"gmtOffset\": 43200, \"abbreviation\": \"WAKT\", \"gmtOffsetName\": \"UTC+12:00\"}]", "numeric_code": "581", "iso3": "UMI", "nationality": "American", "capital": "", "tld": ".us", "native": "United States Minor Outlying Islands", "region": "Americas", "currency": "USD", "currency_name": "United States dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "0.00", "lng": "0.00", "emoji": "🇺🇲", "emojiU": "U+1F1FA U+1F1F2", "flag": "1", "is_activated": "1"}, {"id": "237", "name": "Uruguay", "code": "UY", "phone": "858", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Uruguay\", \"cn\": \"乌拉圭\", \"de\": \"Uruguay\", \"es\": \"Uruguay\", \"fa\": \"اروگوئه\", \"fr\": \"Uruguay\", \"hr\": \"Urugvaj\", \"it\": \"Uruguay\", \"ja\": \"ウルグアイ\", \"kr\": \"우루과이\", \"nl\": \"Uruguay\", \"pt\": \"Uruguai\", \"tr\": \"Uruguay\", \"pt-BR\": \"Uruguai\"}", "timezones": "[{\"tzName\": \"Uruguay Standard Time\", \"zoneName\": \"America/Montevideo\", \"gmtOffset\": -10800, \"abbreviation\": \"UYT\", \"gmtOffsetName\": \"UTC-03:00\"}]", "numeric_code": "858", "iso3": "URY", "nationality": "Uruguayan", "capital": "Montevideo", "tld": ".uy", "native": "Uruguay", "region": "Americas", "currency": "UYU", "currency_name": "Uruguayan peso", "currency_symbol": "$", "wikiDataId": null, "lat": "-33.00", "lng": "-56.00", "emoji": "🇺🇾", "emojiU": "U+1F1FA U+1F1FE", "flag": "1", "is_activated": "1"}, {"id": "238", "name": "Uzbekistan", "code": "UZ", "phone": "860", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Uzbekistan\", \"cn\": \"乌兹别克斯坦\", \"de\": \"Usbekistan\", \"es\": \"Uzbekistán\", \"fa\": \"ازبکستان\", \"fr\": \"Ouzbékistan\", \"hr\": \"Uzbekistan\", \"it\": \"Uzbekistan\", \"ja\": \"ウズベキスタン\", \"kr\": \"우즈베키스탄\", \"nl\": \"Oezbekistan\", \"pt\": \"Usbequistão\", \"tr\": \"Özbekistan\", \"pt-BR\": \"Uzbequistão\"}", "timezones": "[{\"tzName\": \"Uzbekistan Time\", \"zoneName\": \"Asia/Samarkand\", \"gmtOffset\": 18000, \"abbreviation\": \"UZT\", \"gmtOffsetName\": \"UTC+05:00\"}, {\"tzName\": \"Uzbekistan Time\", \"zoneName\": \"Asia/Tashkent\", \"gmtOffset\": 18000, \"abbreviation\": \"UZT\", \"gmtOffsetName\": \"UTC+05:00\"}]", "numeric_code": "860", "iso3": "UZB", "nationality": "Uzbekistani, Uzbek", "capital": "Tashkent", "tld": ".uz", "native": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "region": "Asia", "currency": "UZS", "currency_name": "Uzbekistani soʻm", "currency_symbol": "лв", "wikiDataId": null, "lat": "41.00", "lng": "64.00", "emoji": "🇺🇿", "emojiU": "U+1F1FA U+1F1FF", "flag": "1", "is_activated": "1"}, {"id": "239", "name": "Vanuatu", "code": "VU", "phone": "548", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Vanuatu\", \"cn\": \"瓦努阿图\", \"de\": \"Vanuatu\", \"es\": \"Vanuatu\", \"fa\": \"وانواتو\", \"fr\": \"Vanuatu\", \"hr\": \"Vanuatu\", \"it\": \"Vanuatu\", \"ja\": \"バヌアツ\", \"kr\": \"바누아투\", \"nl\": \"Vanuatu\", \"pt\": \"Vanuatu\", \"tr\": \"Vanuatu\", \"pt-BR\": \"Vanuatu\"}", "timezones": "[{\"tzName\": \"Vanuatu Time\", \"zoneName\": \"Pacific/Efate\", \"gmtOffset\": 39600, \"abbreviation\": \"VUT\", \"gmtOffsetName\": \"UTC+11:00\"}]", "numeric_code": "548", "iso3": "VUT", "nationality": "Ni-Vanuatu, Vanuatuan", "capital": "Port Vila", "tld": ".vu", "native": "Vanuatu", "region": "Oceania", "currency": "VUV", "currency_name": "Vanuatu vatu", "currency_symbol": "VT", "wikiDataId": null, "lat": "-16.00", "lng": "167.00", "emoji": "🇻🇺", "emojiU": "U+1F1FB U+1F1FA", "flag": "1", "is_activated": "1"}, {"id": "240", "name": "Venezuela", "code": "VE", "phone": "862", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Venezuela\", \"cn\": \"委内瑞拉\", \"de\": \"Venezuela\", \"es\": \"Venezuela\", \"fa\": \"ونزوئلا\", \"fr\": \"Venezuela\", \"hr\": \"Venezuela\", \"it\": \"Venezuela\", \"ja\": \"ベネズエラ・ボリバル共和国\", \"kr\": \"베네수엘라\", \"nl\": \"Venezuela\", \"pt\": \"Venezuela\", \"tr\": \"Venezuela\", \"pt-BR\": \"Venezuela\"}", "timezones": "[{\"tzName\": \"Venezuelan Standard Time\", \"zoneName\": \"America/Caracas\", \"gmtOffset\": -14400, \"abbreviation\": \"VET\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "862", "iso3": "VEN", "nationality": "Venezuelan", "capital": "Caracas", "tld": ".ve", "native": "Venezuela", "region": "Americas", "currency": "VES", "currency_name": "Bolívar", "currency_symbol": "Bs", "wikiDataId": null, "lat": "8.00", "lng": "-66.00", "emoji": "🇻🇪", "emojiU": "U+1F1FB U+1F1EA", "flag": "1", "is_activated": "1"}, {"id": "241", "name": "Vietnam", "code": "VN", "phone": "704", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Vietnam\", \"cn\": \"越南\", \"de\": \"Vietnam\", \"es\": \"Vietnam\", \"fa\": \"ویتنام\", \"fr\": \"Viêt Nam\", \"hr\": \"Vijetnam\", \"it\": \"Vietnam\", \"ja\": \"ベトナム\", \"kr\": \"베트남\", \"nl\": \"Vietnam\", \"pt\": \"Vietname\", \"tr\": \"Vietnam\", \"pt-BR\": \"Vietnã\"}", "timezones": "[{\"tzName\": \"Indochina Time\", \"zoneName\": \"Asia/Ho_Chi_Minh\", \"gmtOffset\": 25200, \"abbreviation\": \"ICT\", \"gmtOffsetName\": \"UTC+07:00\"}]", "numeric_code": "704", "iso3": "VNM", "nationality": "Vietnamese", "capital": "<PERSON><PERSON>", "tld": ".vn", "native": "Việt Nam", "region": "Asia", "currency": "VND", "currency_name": "Vietnamese đồng", "currency_symbol": "₫", "wikiDataId": null, "lat": "16.17", "lng": "107.83", "emoji": "🇻🇳", "emojiU": "U+1F1FB U+1F1F3", "flag": "1", "is_activated": "1"}, {"id": "242", "name": "Virgin Islands (British)", "code": "VG", "phone": "092", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Virgin Islands (British)\", \"cn\": \"圣文森特和格林纳丁斯\", \"de\": \"Britische Jungferninseln\", \"es\": \"Islas Vírgenes del Reino Unido\", \"fa\": \"جزایر ویرجین بریتانیا\", \"fr\": \"Îles Vierges britanniques\", \"hr\": \"Britanski Djevičanski Otoci\", \"it\": \"Isole Vergini Britanniche\", \"ja\": \"イギリス領ヴァージン諸島\", \"kr\": \"영국령 버진아일랜드\", \"nl\": \"Britse Maagdeneilanden\", \"pt\": \"Ilhas Virgens Britânicas\", \"tr\": \"Britanya Virjin Adalari\", \"pt-BR\": \"Ilhas Virgens Britânicas\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/Tortola\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "092", "iso3": "VGB", "nationality": "British Virgin Island", "capital": "Road Town", "tld": ".vg", "native": "British Virgin Islands", "region": "Americas", "currency": "USD", "currency_name": "United States dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "18.43", "lng": "-64.62", "emoji": "🇻🇬", "emojiU": "U+1F1FB U+1F1EC", "flag": "1", "is_activated": "1"}, {"id": "243", "name": "Virgin Islands (US)", "code": "VI", "phone": "850", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Virgin Islands (US)\", \"cn\": \"维尔京群岛（美国）\", \"de\": \"Amerikanische Jungferninseln\", \"es\": \"Islas Vírgenes de los Estados Unidos\", \"fa\": \"جزایر ویرجین آمریکا\", \"fr\": \"Îles Vierges des États-Unis\", \"it\": \"Isole Vergini americane\", \"ja\": \"アメリカ領ヴァージン諸島\", \"kr\": \"미국령 버진아일랜드\", \"nl\": \"Verenigde Staten Maagdeneilanden\", \"pt\": \"Ilhas Virgens Americanas\", \"tr\": \"Abd Virjin <PERSON>\", \"pt-BR\": \"Ilhas Virgens Americanas\"}", "timezones": "[{\"tzName\": \"Atlantic Standard Time\", \"zoneName\": \"America/St_Thomas\", \"gmtOffset\": -14400, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC-04:00\"}]", "numeric_code": "850", "iso3": "VIR", "nationality": "U.S. Virgin Island", "capital": "<PERSON>", "tld": ".vi", "native": "United States Virgin Islands", "region": "Americas", "currency": "USD", "currency_name": "United States dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "18.34", "lng": "-64.93", "emoji": "🇻🇮", "emojiU": "U+1F1FB U+1F1EE", "flag": "1", "is_activated": "1"}, {"id": "244", "name": "Wallis And Futuna Islands", "code": "WF", "phone": "876", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Wallis And Futuna Islands\", \"cn\": \"瓦利斯群岛和富图纳群岛\", \"de\": \"Wallis und Futuna\", \"es\": \"Wallis y Futuna\", \"fa\": \"والیس و فوتونا\", \"fr\": \"Wallis-et-Futuna\", \"hr\": \"Wallis i Fortuna\", \"it\": \"Wallis e Futuna\", \"ja\": \"ウォリス・フツナ\", \"kr\": \"왈리스 푸투나\", \"nl\": \"Wallis en Futuna\", \"pt\": \"Wallis e Futuna\", \"tr\": \"Wallis Ve Futuna\", \"pt-BR\": \"Wallis e Futuna\"}", "timezones": "[{\"tzName\": \"Wallis & Futuna Time\", \"zoneName\": \"Pacific/Wallis\", \"gmtOffset\": 43200, \"abbreviation\": \"WFT\", \"gmtOffsetName\": \"UTC+12:00\"}]", "numeric_code": "876", "iso3": "WLF", "nationality": "Wallis and Futuna, Wallisian or Futunan", "capital": "Mata Utu", "tld": ".wf", "native": "Wallis et Futuna", "region": "Oceania", "currency": "XPF", "currency_name": "CFP franc", "currency_symbol": "₣", "wikiDataId": null, "lat": "-13.30", "lng": "-176.20", "emoji": "🇼🇫", "emojiU": "U+1F1FC U+1F1EB", "flag": "1", "is_activated": "1"}, {"id": "246", "name": "Yemen", "code": "YE", "phone": "887", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Yemen\", \"cn\": \"也门\", \"de\": \"Jemen\", \"es\": \"Yemen\", \"fa\": \"یمن\", \"fr\": \"Yémen\", \"hr\": \"Jemen\", \"it\": \"Yemen\", \"ja\": \"イエメン\", \"kr\": \"예멘\", \"nl\": \"Jemen\", \"pt\": \"Iémen\", \"tr\": \"Yemen\", \"pt-BR\": \"Iêmen\"}", "timezones": "[{\"tzName\": \"Arabia Standard Time\", \"zoneName\": \"Asia/Aden\", \"gmtOffset\": 10800, \"abbreviation\": \"AST\", \"gmtOffsetName\": \"UTC+03:00\"}]", "numeric_code": "887", "iso3": "YEM", "nationality": "Yemeni", "capital": "Sanaa", "tld": ".ye", "native": "اليَمَن", "region": "Asia", "currency": "YER", "currency_name": "Yemeni rial", "currency_symbol": "﷼", "wikiDataId": null, "lat": "15.00", "lng": "48.00", "emoji": "🇾🇪", "emojiU": "U+1F1FE U+1F1EA", "flag": "1", "is_activated": "1"}, {"id": "247", "name": "Zambia", "code": "ZM", "phone": "894", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Zambia\", \"cn\": \"赞比亚\", \"de\": \"Sambia\", \"es\": \"Zambia\", \"fa\": \"زامبیا\", \"fr\": \"Zambie\", \"hr\": \"Zambija\", \"it\": \"Zambia\", \"ja\": \"ザンビア\", \"kr\": \"잠비아\", \"nl\": \"Zambia\", \"pt\": \"Zâmbia\", \"tr\": \"Zambiya\", \"pt-BR\": \"Zâmbia\"}", "timezones": "[{\"tzName\": \"Central Africa Time\", \"zoneName\": \"Africa/Lusaka\", \"gmtOffset\": 7200, \"abbreviation\": \"CAT\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "894", "iso3": "ZMB", "nationality": "Zambian", "capital": "Lusaka", "tld": ".zm", "native": "Zambia", "region": "Africa", "currency": "ZMW", "currency_name": "Zambian kwacha", "currency_symbol": "ZK", "wikiDataId": null, "lat": "-15.00", "lng": "30.00", "emoji": "🇿🇲", "emojiU": "U+1F1FF U+1F1F2", "flag": "1", "is_activated": "1"}, {"id": "248", "name": "Zimbabwe", "code": "ZW", "phone": "716", "created_at": "2020-06-17 10:37:05", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Zimbabwe\", \"cn\": \"津巴布韦\", \"de\": \"Simbabwe\", \"es\": \"Zimbabue\", \"fa\": \"زیمباوه\", \"fr\": \"Zimbabwe\", \"hr\": \"Zimbabve\", \"it\": \"Zimbabwe\", \"ja\": \"ジンバブエ\", \"kr\": \"짐바브웨\", \"nl\": \"Zimbabwe\", \"pt\": \"Zimbabué\", \"tr\": \"Zimbabve\", \"pt-BR\": \"Zimbabwe\"}", "timezones": "[{\"tzName\": \"Central Africa Time\", \"zoneName\": \"Africa/Harare\", \"gmtOffset\": 7200, \"abbreviation\": \"CAT\", \"gmtOffsetName\": \"UTC+02:00\"}]", "numeric_code": "716", "iso3": "ZWE", "nationality": "Zimbabwean", "capital": "Harare", "tld": ".zw", "native": "Zimbabwe", "region": "Africa", "currency": "ZWL", "currency_name": "Zimbabwe Dollar", "currency_symbol": "$", "wikiDataId": null, "lat": "-20.00", "lng": "30.00", "emoji": "🇿🇼", "emojiU": "U+1F1FF U+1F1FC", "flag": "1", "is_activated": "1"}, {"id": "249", "name": "Afghanistan", "code": "AF", "phone": "004", "created_at": "2024-01-21 11:39:22", "updated_at": "2024-01-21 12:54:28", "translations": "{\"ar\": \"Afghanistan\", \"cn\": \"阿富汗\", \"de\": \"Afghanistan\", \"es\": \"Afganistán\", \"fa\": \"افغانستان\", \"fr\": \"Afghanistan\", \"hr\": \"Afganistan\", \"it\": \"Afghanistan\", \"ja\": \"アフガニスタン\", \"kr\": \"아프가니스탄\", \"nl\": \"Afghanistan\", \"pt\": \"Afeganistão\", \"tr\": \"Afganistan\", \"pt-BR\": \"Afeganistão\"}", "timezones": "[{\"tzName\": \"Afghanistan Time\", \"zoneName\": \"Asia/Kabul\", \"gmtOffset\": 16200, \"abbreviation\": \"AFT\", \"gmtOffsetName\": \"UTC+04:30\"}]", "numeric_code": "004", "iso3": "AFG", "nationality": "Afghan", "capital": "Kabul", "tld": ".af", "native": "افغانستان", "region": "Asia", "currency": "AFN", "currency_name": "Afghan afghani", "currency_symbol": "؋", "wikiDataId": null, "lat": "33.00", "lng": "65.00", "emoji": "🇦🇫", "emojiU": "U+1F1E6 U+1F1EB", "flag": "1", "is_activated": "1"}, {"id": "250", "name": "Kosovo", "code": "XK", "phone": "926", "created_at": "2024-01-21 11:39:24", "updated_at": "2024-01-21 12:54:30", "translations": "{\"ar\": \"Kosovo\", \"cn\": \"科索沃\", \"kr\": \"코소보\", \"tr\": \"<PERSON><PERSON><PERSON>\"}", "timezones": "[{\"tzName\": \"Central European Time\", \"zoneName\": \"Europe/Belgrade\", \"gmtOffset\": 3600, \"abbreviation\": \"CET\", \"gmtOffsetName\": \"UTC+01:00\"}]", "numeric_code": "926", "iso3": "XKX", "nationality": "Kosovar, Kosovan", "capital": "P<PERSON><PERSON>", "tld": ".xk", "native": "Republika e Kosovës", "region": "Europe", "currency": "EUR", "currency_name": "Euro", "currency_symbol": "€", "wikiDataId": null, "lat": "42.56", "lng": "20.34", "emoji": "🇽🇰", "emojiU": "U+1F1FD U+1F1F0", "flag": "1", "is_activated": "1"}]