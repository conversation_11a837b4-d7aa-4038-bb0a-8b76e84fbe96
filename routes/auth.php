<?php

use App\Http\Controllers\Auth\Api\DeveloperController;
use App\Http\Controllers\Auth\Api\LoginController;
use App\Http\Controllers\Auth\Api\LogoutController;
use App\Http\Controllers\Auth\Api\OtpController;
use App\Http\Controllers\Auth\Api\PasswordController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\Api\RegistrationController;
use App\Http\Controllers\Settings\Api\SettingsController;

Route::prefix('v1')->group(function () {
    Route::post('/register', [RegistrationController::class, 'register']);

    Route::post('/login', [LoginController::class, 'login']);

    Route::post('/otp/verify', [OtpController::class, 'verify']);
    Route::post('/otp/renew', [OtpController::class, 'renew']);

    Route::post('/password/set', [PasswordController::class, 'set']);
    Route::post('/password/forgot', [PasswordController::class, 'forgot']);
    Route::post('/password/reset', [PasswordController::class, 'reset']);

    Route::post('/developer/deleteAccount', [DeveloperController::class, 'deleteAccount']);
    Route::post('/developer/unlockAccount', [DeveloperController::class, 'unlockAccount']);
    Route::post('/developer/getPlans', [DeveloperController::class, 'getPlans']);
    Route::post('/developer/test', [DeveloperController::class, 'testMail']);
    Route::get('/developer/test-config', [DeveloperController::class, 'testConfig']);
    Route::post('/developer/server-id', [DeveloperController::class, 'getServerID']);

    Route::get('/settings/list', [SettingsController::class, 'list']);
});

Route::prefix('v1')->middleware(['jwt.auth'])->group(function () {
    Route::post('/logout', [LogoutController::class, 'logout']);

    Route::post('/password/change', [PasswordController::class, 'change']);
});