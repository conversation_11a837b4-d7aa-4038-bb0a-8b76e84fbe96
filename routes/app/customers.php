<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Customers\Api\CustomersController;

Route::prefix('v1')->middleware(['jwt.auth'])->group(function () {
    Route::get('/customer/users', [CustomersController::class, 'CustomerUsers']);
    Route::get('/customer/users/{id}', [CustomersController::class, 'CustomerUser']);

    Route::put('/customer', [CustomersController::class, 'updateCustomer']);
    Route::put('/customer/users/{id}', [CustomersController::class, 'updateCustomerUser']);

    Route::post('/customer/users', [CustomersController::class, 'addCustomerUser']);
    Route::post('/customer/users/{id}/invite', [CustomersController::class, 'resendCustomerUserInvitation']);

    Route::patch('/customer/users/{id}/deactivate', [CustomersController::class, 'deactivateCustomerUser']);
    Route::patch('/customer/users/{id}/activate', [CustomersController::class, 'activateCustomerUser']);
    Route::patch('/customer/users/{id}/status', [CustomersController::class, 'changeCustomerUserStatus']);

});