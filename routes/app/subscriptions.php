<?php

use App\Http\Controllers\Subscriptions\Api\SubscriptionsController;
use Illuminate\Support\Facades\Route;

Route::prefix('v1')->middleware(['jwt.auth'])->group(function () {
    
    // LIST
    Route::get('/subscriptions/{id?}', [SubscriptionsController::class, 'list']);

    // SUBSCRIBE
    Route::post('/subscriptions/susbcribe', [SubscriptionsController::class, 'Subscribe']);

    // ADDRESS
    Route::get('/subscriptions/address/{country_id}', [SubscriptionsController::class, 'getAddressDetailsByBillingCountryId']);

    // RECEIPTS
    Route::get('/subscriptions/{subsctiption_id}/receipts', [SubscriptionsController::class, 'getReceiptsFiles']);
    Route::post('/subscriptions/{subsctiption_id}/receipts', [SubscriptionsController::class, 'uploadReceipts']);

    // SERVERS
    Route::post('/subscriptions/{subsctiption_id}/server', [SubscriptionsController::class, 'uploadServerId']);

    // PRICE
    Route::post('/subscriptions/estimate', [SubscriptionsController::class, 'estimatedPrice']);
 
});

# For Testing Purposes
Route::prefix('v1')->group(function () {
      // DEPLOYMENT
    Route::post('/subscriptions/deployment-complete', [SubscriptionsController::class, 'deploymentIsComplete']);
});