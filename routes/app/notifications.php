<?php

use App\Http\Controllers\Notifications\Api\NotificationsController;
use Illuminate\Support\Facades\Route;

Route::prefix('v1')->middleware(['jwt.auth'])->group(function () {
    Route::prefix('notifications')->controller(NotificationsController::class)->group(function () {

        Route::get('/', 'index');

        Route::put('/{notification_id}/read', 'markAsRead')->where('notification_id', '[0-9]+');

        Route::put('/read-all', 'markAllAsRead');

        Route::get('/settings', 'getSettings');

        Route::put('/settings', 'updateSettings');
        
    });
});