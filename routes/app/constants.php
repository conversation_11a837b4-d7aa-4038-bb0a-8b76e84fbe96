<?php

use App\Http\Controllers\Constants\Api\ConstantsController;
use Illuminate\Support\Facades\Route;

Route::prefix('v1')->middleware(['jwt.auth'])->group(function () {
    Route::get('/constants', [ConstantsController::class, 'list']);
    Route::get('/constants/countries', [ConstantsController::class, 'countries']);
    Route::post('/constants/cities', [ConstantsController::class, 'cities']);
    Route::post('/constants/currencies', [ConstantsController::class, 'currencies']);
    Route::post('/constants/banks', [ConstantsController::class, 'banks']);
});