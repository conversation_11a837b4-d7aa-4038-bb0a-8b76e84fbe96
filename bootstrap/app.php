<?php

use App\Console\Commands\UpdateSonarQubeUsage;
use App\Enums\ApiStatus;
use App\Services\ApiResponseService;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Console\Scheduling\Schedule;
use Spatie\Permission\Exceptions\UnauthorizedException;
use Symfony\Component\HttpKernel\Exception\HttpException;

$app = Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        api: __DIR__ . '/../routes/api.php',
        web: __DIR__ . '/../routes/web.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->web(append: [
            \App\Http\Middleware\HandleInertiaRequests::class,
            \Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets::class,
        ]);

        $middleware->api(append: [
            \App\Http\Middleware\ApiResponseTimer::class
        ]);

        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->render(function (Throwable $e, $request) {
            if ($request->is('api/*')) {

                $apiResponse = app(ApiResponseService::class);

                // Unauthenticated
                if ($e instanceof AuthenticationException) {
                    return $apiResponse
                        ->props(ApiStatus::UNAUTHORIZED)
                        ->send();
                }

                // Forbidden (Spatie permissions)
                if ($e instanceof UnauthorizedException) {
                    return $apiResponse
                        ->props(ApiStatus::FORBIDDEN)
                        ->send();
                }

                // Other HTTP exceptions (e.g., 404, 405)
                if ($e instanceof HttpException) {
                    return $apiResponse
                        ->props(ApiStatus::from($e->getStatusCode()), $e->getMessage())
                        ->send();
                }


                // Any other unhandled exception
                return $apiResponse
                    ->props(ApiStatus::SERVER_ERROR)
                    ->send();
            }
        });
    })
    ->withSchedule(function (Schedule $schedule) {
        $schedule->command(UpdateSonarQubeUsage::class)->hourly();
    })
    ->create();

$app->register(OwenIt\Auditing\AuditingServiceProvider::class);

return $app;
