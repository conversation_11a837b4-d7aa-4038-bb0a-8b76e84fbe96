<?php

namespace App\Services;

use App\Enums\ApiStatus;
use App\Enums\ValidationMessages;
use Illuminate\Http\JsonResponse;

class ApiResponseService
{
    protected $responseTime = 0;

    protected ApiStatus $code;

    protected ?string $user_message;

    protected string|ValidationMessages $error_code;

    protected ?string $error_details;

    protected ?array $data = [];

    protected ?array $validation_messages = [];

    
    public function props(ApiStatus $code, ?string $user_message = null): self
    {
        $this->code = $code;
        $this->user_message = $user_message;
        return $this;
    }

    /**
     * Start the response timer.
     */
    public function begin(): void
    {
        $this->responseTime = microtime(true);
    }

    /**
     * Set system messages.
     */
    public function withSystemMessages(string | ValidationMessages $error_code, ?string $error_details = null): self
    {
        if($error_code instanceof ValidationMessages)
        {
            $this->error_code = $error_code->value;
            $this->error_details = $error_code->description();
        } else {
            $this->error_code = $error_code;
            $this->error_details = $error_details;
        }

        return $this;
    }

    /**
     * Set data.
     */
    public function withData(array $data): self
    {
        $this->data = $data;
        return $this;
    }

    /**
     * Set validation messages.
     */
    public function withValidationMessages(array $validation_messages): self
    {
        $this->validation_messages = $validation_messages;
        return $this;
    }

    /**
     * Send data.
     */
    public function send(): JsonResponse
    {
        $system_messages = (empty($this->error_code) && empty($this->error_details)) ? [] : [
            'error_code' => $this->error_code,
            'error_details' => $this->error_details
        ];

        return response()->json([
            'code' => $this->code,
            'description' => $this->code->description(),
            'user_message' => $this->user_message,
            'validation_messages' => $this->validation_messages,
            'system_messages' => $system_messages,
            'processing_time' => round(microtime(true) - $this->responseTime, 4),
            'data' => $this->data,
        ], $this->code->value);
    }
}
