<?php

namespace App\Services;

use App\Enums\DefaultValues;
use App\Enums\Subscriptions;
use App\Mail\SubscriptionLicenseActivation;
use App\Models\Feature;
use App\Models\PlanFeature;
use App\Models\Subscription;
use App\Models\SubscriptionFeature;
use App\Models\SubscriptionLicense;
use App\Models\SubscriptionPayment;
use App\Models\SubscriptionUsage;
use App\Observers\SubscriptionObserver;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SubscriptionService
{
    /**
     * Update subscription features
     */
    public function updateFeatures(Subscription $subscription, array $data): void
    {
        // Delete existing subscription features
        $subscription->subscriptionFeatures()->delete();

        // Also delete existing usage records to avoid duplicates
        $subscription->usage()->delete();

        // Add the subscription type feature if it exists in the form data
        if (isset($data['subscription_type']) && $data['subscription_type']) {
            $subscriptionTypeId = $data['subscription_type'];
            $subscriptionTypeFeature = Feature::find($subscriptionTypeId);

            if ($subscriptionTypeFeature) {
                SubscriptionFeature::create([
                    'subscription_id' => $subscription->id,
                    'feature_id' => $subscriptionTypeFeature->id,
                    'value' => true,
                ]);
            }
        }

        // Add the main plan feature if it exists
        if (isset($data['plan_features']) && $data['plan_features']) {
            $planFeatureId = $data['plan_features'];
            $planFeature = PlanFeature::find($planFeatureId);

            if ($planFeature) {
                // Create subscription feature
                SubscriptionFeature::create([
                    'subscription_id' => $subscription->id,
                    'feature_id' => $planFeature->feature_id,
                    'value' => true,
                    'plan_feature_id' => $planFeature->plan_feature_id,
                ]);

                // Create usage record for this feature
                SubscriptionUsage::create([
                    'subscription_id' => $subscription->id,
                    'feature_id' => $planFeature->feature_id,
                    'plan_feature_id' => $planFeature->plan_feature_id,
                    'used' => false
                ]);

                // Update quota if this feature has a maximum value (we are already updating the quota on updating the subscription itself)
//                if ($planFeature->valid_to_maximum) {
//                    $subscription->update([
//                        'assigned_quota' => $planFeature->valid_to_maximum,
//                        'remaining_quota' => $planFeature->valid_to_maximum
//                    ]);
//                }
            }
        }

        // Add the add-on features if they exist
        if (isset($data['addon_features']) && is_array($data['addon_features']) && !empty($data['addon_features'])) {
            foreach ($data['addon_features'] as $addonFeatureId) {
                $addonFeature = PlanFeature::find($addonFeatureId);

                if ($addonFeature) {
                    // Create subscription feature
                    SubscriptionFeature::create([
                        'subscription_id' => $subscription->id,
                        'feature_id' => $addonFeature->feature_id,
                        'value' => true,
                        'plan_feature_id' => $addonFeature->plan_feature_id,
                    ]);

                    // Create usage record for this addon
                    SubscriptionUsage::create([
                        'subscription_id' => $subscription->id,
                        'feature_id' => $addonFeature->feature_id,
                        'plan_feature_id' => $addonFeature->plan_feature_id,
                        'used' => false
                    ]);
                }
            }
        }
    }

    /**
     * Update subscription licenses
     */
    public function updateLicenses(Subscription $subscription, array $licensesData): void
    {
        $existingLicenses = $subscription->subscriptionLicenses;
        $processedLicenseIds = [];
        $hasLicenseKeyProvided = false;

        // Track licenses that need email
        $licensesToNotify = [];

        foreach ($licensesData as $licenseData) {
            $licenseKey = $licenseData['license_key'] ?? '';
            $serverId = $licenseData['server_id'] ?? '';

            if (!empty($licenseKey)) {
                $hasLicenseKeyProvided = true;
            }

            if (!empty($licenseData['subscription_license_id'])) {
                $license = $existingLicenses->firstWhere('subscription_license_id', $licenseData['subscription_license_id']);
                if ($license) {
                    // Check if license key is actually changing
                    $licenseKeyChanged = !empty($licenseKey) && $license->license_key !== $licenseKey;
                    $emptyToNonEmpty = empty($license->license_key) && !empty($licenseKey);

                    $license->update([
                        'license_type' => $licenseData['license_type'],
                        'environment' => $licenseData['environment'] ?? 'test',
                        'license_key' => $licenseKey,
                        'server_id' => $serverId,
                    ]);

                    if ($licenseKeyChanged || $emptyToNonEmpty) {
                        $this->applyLicenseToSonarQube($license);
                        $licensesToNotify[] = $license;
                    }

                    $processedLicenseIds[] = $license->subscription_license_id;
                }
            } else {
                // Create a new license
                $license = SubscriptionLicense::create([
                    'subscription_id' => $subscription->id,
                    'company_id' => $subscription->subscriber_id,
                    'license_key' => $licenseKey,
                    'server_id' => $serverId,
                    'license_type' => $licenseData['license_type'],
                    'environment' => $licenseData['environment'] ?? 'test',
                    'request_date' => $subscription->starts_at,
                    'issue_date' => now(),
                    'first_use_date' => null,
                ]);
                $processedLicenseIds[] = $license->subscription_license_id;

                if (!empty($licenseKey)) {
                    $this->applyLicenseToSonarQube($license);
                    $licensesToNotify[] = $license;
                }
            }
        }

        // Delete licenses that weren't in the submitted data
        if (!empty($processedLicenseIds)) {
            // Get licenses to delete and delete them individually to trigger observers
            $licensesToDelete = $subscription->subscriptionLicenses()
                ->whereNotIn('subscription_license_id', $processedLicenseIds)
                ->get();

            foreach ($licensesToDelete as $licenseToDelete) {
                $licenseToDelete->delete();
            }
        }

        // Update subscription status to active if license key is provided
        if ($hasLicenseKeyProvided && $subscription->subscription_status !== Subscriptions::SUBSCRIPTION_STATUS_ACTIVE->value) {
            SubscriptionObserver::$eventType = Subscriptions::HISTORY_EVENT_TYPE_ACTIVATION->value;

            $subscription->subscription_status = Subscriptions::SUBSCRIPTION_STATUS_ACTIVE->value;

            if ($subscription->isSaas() && empty($subscription->starts_at) && empty($subscription->ends_at)) {
                $subscription->starts_at = now();
                $subscription->ends_at = now()->addYears($subscription->number_of_years());
            }

            $subscription->save();
        }

        // Send an email for each license that needs notification
        foreach ($licensesToNotify as $license) {
            /**
             * Check if saas  
             */
            if ($subscription->isSaas()) {
                $this->sendSubscriptionLicenseActivationEmail($subscription, $license);
            } else {
                $this->sendSubscriptionLicenseKeyDeliveryEmail($subscription, $license);
            }
        }
    }

    /**
     * Apply the license key to SonarQube and handle notifications.
     *
     * @param SubscriptionLicense $license
     */
    private function applyLicenseToSonarQube(SubscriptionLicense $license): void
    {
        if (empty($license->license_key) || empty($license->sonar_url) || empty($license->sonar_api_token)) {
            Log::warning('Skipped SonarQube license update due to missing key, URL, or token.', [
                'subscription_license_id' => $license->subscription_license_id,
                '$license->license_key' => $license->license_key,
                '$license->sonar_url' => $license->sonar_url,
                '$license->sonar_api_token' => $license->sonar_api_token,
            ]);
            return;
        }

        try {
            $sonarService = new SonarQubeService(
                $license->sonar_url,
                $license->sonar_api_token
            );

            $result = $sonarService->setLicenseKey($license->license_key);

            if ($result['success']) {
                Notification::make()
                    ->title('SonarQube License Updated')
                    ->body('The license key was successfully applied to the SonarQube instance.')
                    ->success()
                    ->send();
            } else {
                Log::error('Failed to apply SonarQube license key.', [
                    'subscription_license_id' => $license->subscription_license_id,
                    'message' => $result['message'] ?? 'No message provided.',
                    'status_code' => $result['status_code'] ?? 'N/A',
                ]);

                Notification::make()
                    ->title('SonarQube Update Failed')
                    ->body('Could not apply the license key. ' . ($result['message'] ?? 'Please check the logs.'))
                    ->danger()
                    ->send();
            }
        } catch (\Exception $e) {
            Log::error('An exception occurred while applying SonarQube license key.', [
                'subscription_license_id' => $license->subscription_license_id,
                'exception_message' => $e->getMessage(),
            ]);

            Notification::make()
                ->title('SonarQube Update Error')
                ->body('An unexpected error occurred. Please check the system logs for details.')
                ->danger()
                ->send();
        }
    }

    /**
     * Create subscription payment
     */
    public function createPayment(Subscription $subscription): void
    {
        // Create a payment record for the subscription
        SubscriptionPayment::create([
            'subscription_id' => $subscription->id,
            'payment_type' => Subscriptions::PAYMENT_TYPE_WIRE_TRANSFER,
            'payment_gateway' => 'payment gateway name',
        ]);
    }

    /**
     * Get subscription data for form filling
     */
    public function getFormData(Subscription $subscription, array $data): array
    {
        // Process translatable fields based on the model's $translatable property
        if (property_exists($subscription, 'translatable') && is_array($subscription->translatable)) {
            foreach ($subscription->translatable as $field) {
                $locale = app()->getLocale();
                $translations = $data[$field];
                $data[$field] = $translations[$locale] ?? $translations['en'] ?? '';
            }
        }

        // Set subscription licenses data
        $subscriptionLicenses = $subscription->subscriptionLicenses()->get();
        if ($subscriptionLicenses->isNotEmpty()) {
            $data['subscription_licenses'] = $subscriptionLicenses->map(function ($license) {
                return [
                    'subscription_license_id' => $license->subscription_license_id,
                    'license_type' => $license->license_type,
                    'license_key' => $license->license_key,
                    'server_id' => $license->server_id,
                    'environment' => $license->environment,
                ];
            })->toArray();
        }

        // Get subscription features
        $subscriptionFeatures = $subscription->subscriptionFeatures()
            ->with('feature')
            ->get();

        // Set subscription_type based on the subscription type feature (SaaS or On-Prem)
        $subscriptionTypeFeature = $subscriptionFeatures
            ->first(function ($feature) {
                return str_contains($feature->feature->slug, Subscription::SUBSCRIPTION_TYPE_SAAS) ||
                    str_contains($feature->feature->slug, Subscription::SUBSCRIPTION_TYPE_ON_PREM);
            });

        if ($subscriptionTypeFeature) {
            $data['subscription_type'] = $subscriptionTypeFeature->feature_id;
        }

        // Find the main plan feature and addon features
        $planId = $subscription->plan_id;
        $mainPlanFeatureId = null;
        $addonFeatureIds = [];

        foreach ($subscriptionFeatures as $subscriptionFeature) {
            // Skip subscription type features
            if (
                str_contains($subscriptionFeature->feature->slug, Subscription::SUBSCRIPTION_TYPE_SAAS) ||
                str_contains($subscriptionFeature->feature->slug, Subscription::SUBSCRIPTION_TYPE_ON_PREM)
            ) {
                continue;
            }

            // Find the corresponding plan feature using feature_id and plan_id
            $planFeature = PlanFeature::where('plan_id', $planId)
                ->where('feature_id', $subscriptionFeature->feature_id)
                ->first();

            if ($planFeature) {
                if ($planFeature->is_addon) {
                    $addonFeatureIds[] = $planFeature->plan_feature_id;
                } else {
                    $mainPlanFeatureId = $planFeature->plan_feature_id;
                }
            }
        }

        if ($mainPlanFeatureId) {
            $data['plan_features'] = $mainPlanFeatureId;
        }

        if (!empty($addonFeatureIds)) {
            $data['addon_features'] = $addonFeatureIds;
        }

        // Add available plan features and add-ons for the current subscription
        if (isset($data['plan_id']) && isset($data['subscription_type'])) {
            $planId = $data['plan_id'];
            $subscriptionTypeId = $data['subscription_type'];

            // Get the feature to determine subscription type
            $feature = Feature::find($subscriptionTypeId);
            if ($feature) {
                // Get all plan features with the same subscription type (by slug)
                $parentFeatures = PlanFeature::whereHas('feature', function ($q) use ($feature) {
                    $q->where('slug', 'like', "%{$feature->slug}%");
                })->pluck('plan_feature_id')->toArray();

                // Get available plan features
                $availablePlanFeatures = PlanFeature::where('plan_id', $planId)
                    ->with('feature')
                    ->whereHas('feature', function ($query) {
                        $query->where(function ($q) {
                            $q->where('slug', 'not like', "%" . Subscription::SUBSCRIPTION_TYPE_ON_PREM . "%")
                                ->where('slug', 'not like', "%" . Subscription::SUBSCRIPTION_TYPE_SAAS . "%");
                        });
                    })
                    ->where('is_addon', false);

                if (!empty($parentFeatures)) {
                    $availablePlanFeatures->whereIn('parent_plan_feature_id', $parentFeatures);
                }

                $data['available_plan_features'] = $availablePlanFeatures->get()
                    ->mapWithKeys(function ($planFeature) {
                        return [
                            $planFeature->plan_feature_id => $planFeature->feature->name
                        ];
                    })
                    ->toArray();

                // Get available add-ons
                $availableAddons = PlanFeature::where('plan_id', $planId)
                    ->with('feature')
                    ->where('is_addon', true);

                if (!empty($parentFeatures)) {
                    $availableAddons->whereIn('parent_plan_feature_id', $parentFeatures);
                }

                $data['available_addon_features'] = $availableAddons->get()
                    ->mapWithKeys(function ($planFeature) {
                        $price = $planFeature->price ? " - Price: {$planFeature->price} USD" : '';
                        return [
                            $planFeature->plan_feature_id => "{$planFeature->feature->name}{$price}"
                        ];
                    })
                    ->toArray();
            }
        }

        return $data;
    }

    /**
     * Prepare subscription data for creation or update
     */
    public function prepareSubscriptionData(array $data): array
    {
        $useCustomDates = isset($data['use_custom_dates']) && (bool)$data['use_custom_dates'];

        $subscriptionData = [
            'subscriber_type' => $data['subscriber_type'],
            'subscriber_id' => $data['subscriber_id'],
            'name' => config('settings.default_product_name'),
            'description' => $data['description'] ?? null,
            'plan_id' => $data['plan_id'],
            'country_id' => $data['country_id'] ?? 1,
            'billing_country_id' => $data['billing_country_id'] ?? 1,
            'currency_id' => $data['currency_id'] ?? 1,
            'suspension_reason_id' => $data['suspension_reason_id'] ?? null,
            'timezone' => $data['timezone'] ?? 'UTC',
            'subscription_status' => $data['subscription_status'] ?? 'pending_payment',
            'payment_status' => $data['payment_status'] ?? 'pending',
            'auto_renew' => $data['auto_renew'] ?? false,
            'assigned_quota' => $data['assigned_quota'] ?? 0,
            'used_quota' => $data['used_quota'] ?? 0,
            'remaining_quota' => $data['remaining_quota'] ?? 0,
            'trial_ends_at' => $useCustomDates ? ($data['trial_ends_at'] ?? null) : null,
            'starts_at' => $useCustomDates ? ($data['starts_at'] ?? null) : now(),
            'ends_at' => $useCustomDates ? ($data['ends_at'] ?? null) : now()->addYears(DefaultValues::SUBSCRIPTION_LIFETIME_YEARS->get()),
            'cancels_at' => $useCustomDates ? ($data['cancels_at'] ?? null) : null,
            'canceled_at' => $useCustomDates ? ($data['canceled_at'] ?? null) : null,
            'last_payment_date' => $useCustomDates ? ($data['last_payment_date'] ?? null) : null,
        ];

        // Check if plan is a trial and set trial_ends_at
        if (!$useCustomDates) {
            $plan = \App\Models\Plan::find($data['plan_id']);
            if ($plan && $plan->isFreeTrial()) {
                $subscriptionData['trial_ends_at'] = now()->addDays(DefaultValues::SUBSCRIPTION_TRIAL_PERIOD_DAYS->get());
                $subscriptionData['ends_at'] = now()->addDays(DefaultValues::SUBSCRIPTION_TRIAL_PERIOD_DAYS->get());
            }
        }

        return $subscriptionData;
    }

    /**
     * Send email notification when subscription is activated
     */
    public static function sendSubscriptionActivatedEmail(Subscription $subscription): void
    {
        // Get the subscriber's primary contact email
        if ($subscription->subscriber && $subscription->subscriber->primaryContact) {
            Mail::to($subscription->subscriber->primaryContact->email)
                ->queue(new \App\Mail\SubscriptionActivatedMail($subscription));
        }
    }

    public static function sendSubscriptionLicenseKeyDeliveryEmail(Subscription $subscription, SubscriptionLicense $license): void
    {
        // Get the subscriber's primary contact email
        if ($subscription->subscriber && $subscription->subscriber->primaryContact) {
            Mail::to($subscription->subscriber->primaryContact->email)
                ->queue(new \App\Mail\SubscriptionLicenseKeyDeliveryMail($subscription, $license));
        }
    }

    public static function sendSubscriptionLicenseActivationEmail(Subscription $subscription, SubscriptionLicense $license): void
    {
        // Get the subscriber's primary contact email
        if ($subscription->subscriber && $subscription->subscriber->primaryContact) {
            Mail::to($subscription->subscriber->primaryContact->email)
                ->queue(new SubscriptionLicenseActivation($subscription, $license));
        }
    }
}
