<?php

namespace App\Services;

use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;
use Illuminate\Support\Facades\Crypt;

class SonarQubeService
{
    protected string $sonarQubeUrl;
    protected ?string $apiToken;
    protected ?string $adminUsername;
    protected ?string $adminPassword;

    /**
     * SonarQubeService constructor.
     *
     * @param string $sonarQubeUrl The base URL of the SonarQube instance.
     * @param string|null $apiToken An API token for Bearer authentication (preferred).
     * @param string|null $adminUsername An admin username for Basic authentication.
     * @param string|null $adminPassword An admin password for Basic authentication.
     */
    public function __construct(
        string $sonarQubeUrl,
        ?string $apiToken = null,
        ?string $adminUsername = null,
        ?string $adminPassword = null
    ) {
        $this->sonarQubeUrl = rtrim($sonarQubeUrl, '/');
        $this->apiToken = optional($apiToken, function () use ($apiToken) {
            return Crypt::decryptString($apiToken);
        });
        $this->adminUsername = $adminUsername;
        $this->adminPassword = $adminPassword;

        if (empty($this->apiToken) && (empty($this->adminUsername) || empty($this->adminPassword))) {
            Log::warning("SonarQubeService initialized for {$this->sonarQubeUrl} without any credentials.");
        }
    }

    /**
     * Makes a request to the SonarQube API, automatically choosing the correct authentication method.
     */
    protected function makeRequest(string $method, string $endpointPath, array $data = [], string $contentType = 'json'): Response
    {
        $fullEndpointUrl = $this->sonarQubeUrl . $endpointPath;
        $httpClient = Http::timeout(30);

        if ($this->apiToken) {
            $httpClient->withToken($this->apiToken);
        } elseif ($this->adminUsername && $this->adminPassword) {
            $httpClient->withBasicAuth($this->adminUsername, $this->adminPassword);
        } else {
            $errorMessage = "SonarQube API request failed for {$fullEndpointUrl}: No valid credentials provided.";
            Log::error($errorMessage);
            throw new Exception($errorMessage);
        }

        // Handle content type for POST/PUT requests
        if (in_array(strtolower($method), ['post', 'put'])) {
            if ($contentType === 'form') {
                $httpClient->asForm();
            }
        }

        return $httpClient->{$method}($fullEndpointUrl, $data);
    }

    /**
    *  Get Server Id
    */
    public function getServerId(): ?string
    {
        try {
            $response = $this->makeRequest('get', '/api/system/info');

            if ($response->successful()) {
                $data = $response->json();

                // Correct path to the server ID in the JSON response
                if (isset($data['System']['Server ID']) && !empty($data['System']['Server ID'])) {
                    $serverId = $data['System']['Server ID'];
                    Log::info("SonarQube API (getServerId for {$this->sonarQubeUrl}): Successfully retrieved server ID: {$serverId}.");
                    return $serverId;
                }

                Log::error("SonarQube API (getServerId for {$this->sonarQubeUrl}): API call successful, but 'System.Server ID' field was not found in the response. Response: " . substr($response->body(), 0, 500));
                return null;
            }

            Log::error("SonarQube API (getServerId for {$this->sonarQubeUrl}): Could not retrieve server info. Status: {$response->status()}, Response: " . substr($response->body(), 0, 500));
            return null;
        } catch (Exception $e) {
            Log::error("SonarQube API Request Exception (getServerId for {$this->sonarQubeUrl}): " . $e->getMessage());
            return null;
        }
    }

    /**
     * **OPTIMIZED**: Fetches usage details (Lines of Code) efficiently.
     */
    public function getUsageDetails(): array
    {
        $usage = [
            'loc_used' => 0,
            'error' => null,
        ];

        try {
            $allProjectKeys = [];
            $page = 1;
            $pageSize = 500; // Max page size for project search

            Log::info("SonarQube API (getUsageDetails for {$this->sonarQubeUrl}): Fetching project list.");

            // Loop to get all project keys, handling pagination
            do {
                $projectsResponse = $this->makeRequest('get', '/api/projects/search', ['p' => $page, 'ps' => $pageSize]);

                if (!$projectsResponse->successful()) {
                    $usage['error'] = "Failed to fetch projects (page {$page}): Status {$projectsResponse->status()} - " . substr($projectsResponse->body(), 0, 500);
                    Log::error("SonarQube API (getUsageDetails): {$usage['error']}");
                    return $usage;
                }

                $projectsData = $projectsResponse->json();
                $projects = $projectsData['components'] ?? [];
                $allProjectKeys = array_merge($allProjectKeys, array_column($projects, 'key'));

                // Exit loop if this was the last page
                if (count($projects) < $pageSize) {
                    break;
                }
                $page++;
            } while (true);

            if (empty($allProjectKeys)) {
                Log::info("SonarQube API (getUsageDetails for {$this->sonarQubeUrl}): No projects found. LOC is 0.");
                return $usage; // No projects, so usage is 0.
            }

            // **PERFORMANCE FIX**: Fetch measures for ALL projects in batches instead of one by one.
            Log::info("SonarQube API (getUsageDetails for {$this->sonarQubeUrl}): Fetching LOC for " . count($allProjectKeys) . " projects.");
            $totalLoc = 0;
            $projectChunks = array_chunk($allProjectKeys, 100); // Process 100 projects per API call

            foreach ($projectChunks as $chunk) {
                $measuresResponse = $this->makeRequest('get', '/api/measures/component_tree', [
                    'componentKeys' => implode(',', $chunk),
                    'metricKeys' => 'ncloc',
                    'strategy' => 'leaves',
                    'ps' => count($chunk),
                ]);

                if ($measuresResponse->successful()) {
                    $measuresData = $measuresResponse->json();
                    foreach ($measuresData['components'] ?? [] as $component) {
                        // The value is often in the first measure element
                        if (isset($component['measures'][0]['value'])) {
                            $totalLoc += (int)$component['measures'][0]['value'];
                        }
                    }
                } else {
                    Log::warning("SonarQube API (getUsageDetails for {$this->sonarQubeUrl}): Could not fetch measures for a chunk of projects. Status: {$measuresResponse->status()}");
                }
            }

            $usage['loc_used'] = $totalLoc;
        } catch (Exception $e) {
            $usage['error'] = "Exception calculating total LOC: " . $e->getMessage();
            Log::error("SonarQube API (getUsageDetails for {$this->sonarQubeUrl}): {$usage['error']}");
        }

        return $usage;
    }

    public function setLicenseKey(string $licenseKey): array
    {
        $endpointPath = '/api/editions/set_license';

        try {
            Log::info("Attempting to set SonarQube license key for {$this->sonarQubeUrl}.");

            $response = $this->makeRequest('post', $endpointPath, ['license' => $licenseKey], 'form');

            if ($response->successful()) {
                return ['success' => true, 'message' => 'License key update call successful.', 'status_code' => $response->status()];
            }

            $message = "Failed to set license key. Status: {$response->status()} - " . $response->body();
            if ($response->status() === 401 || $response->status() === 403) {
                $message = "Authentication failed. Check admin credentials. Status: " . $response->status();
                Log::error("SonarQube API (setLicenseKey): " . $message);
            } else {
                Log::error("SonarQube API (setLicenseKey): " . $message);
            }

            return ['success' => false, 'message' => $message, 'status_code' => $response->status()];
        } catch (Exception $e) {
            $message = "Exception during setLicenseKey: " . $e->getMessage();
            Log::error("SonarQube API (setLicenseKey): " . $message);
            return ['success' => false, 'message' => $message, 'status_code' => null];
        }
    }
}
