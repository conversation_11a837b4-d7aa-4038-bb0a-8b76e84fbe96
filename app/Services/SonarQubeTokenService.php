<?php

namespace App\Services;

use App\Helpers\AppHelper;
use App\Models\SubscriptionLicense; // Assuming this is your Eloquent model
use App\Observers\SubscriptionLicenseObserver;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Http\Client\RequestException;

class SonarQubeTokenService
{
    public function generateAndStoreToken(SubscriptionLicense $license, ?string $tokenName = null): ?string
    {
        // Use the correct primary key name for logging and operations
        $licenseId = $license->subscription_license_id;

        if (empty($license->sonar_username)) {
            Log::error("Cannot generate token: sonar_username not set for SubscriptionLicense ID {$licenseId}.");
            throw new \Exception("SonarQube admin username (sonar_username) is required to generate a token for SubscriptionLicense ID {$licenseId}.");
        }
        if (empty($license->sonar_url)) {
            Log::error("Cannot generate token: sonar_url not set for SubscriptionLicense ID {$licenseId}.");
            throw new \Exception("SonarQube URL (sonar_url) is required to generate a token for SubscriptionLicense ID {$licenseId}.");
        }
        if (empty($license->sonar_password)) {
            Log::error("Cannot generate token: sonar_password not set for SubscriptionLicense ID {$licenseId}.");
            throw new \Exception("SonarQube URL (sonar_password) is required to generate a token for SubscriptionLicense ID {$licenseId}.");
        }

        $endpoint = rtrim($license->sonar_url, '/') . '/api/user_tokens/generate';
        
        $appNameSlug = Str::slug(config('app.name', 'app'), '_');
        // Use the correct primary key for default token name
        $defaultTokenName = 'sub_license_' . $licenseId . '_' . $appNameSlug . '_' . now()->format('Ymd');
        
        $tokenName = $tokenName ?: $defaultTokenName;
        $tokenName = substr($tokenName, 0, 200);

        try {
            Log::info("Attempting to generate SonarQube API token '{$tokenName}' for user '{$license->sonar_username}' at {$license->sonar_url} (License ID: {$licenseId})");

            $response = Http::withBasicAuth($license->sonar_username, AppHelper::decryptString($license->sonar_password))
                            ->asForm()
                            ->timeout(15)
                            ->post($endpoint, ['name' => $tokenName]);

            if ($response->successful()) {
                $tokenData = $response->json();
                if (isset($tokenData['token'])) {
                    $decryptedToken = $tokenData['token'];

                    SubscriptionLicenseObserver::$skipObserver = true; // Skip observer to prevent infinite loop

                    $license->sonar_api_token = Crypt::encryptString($decryptedToken);
                    
                    $license->save(); 

                    SubscriptionLicenseObserver::$skipObserver = false; // Skip observer to prevent infinite loop
                                        
                    Log::info("Successfully generated and stored SonarQube API token for SubscriptionLicense ID {$licenseId}.");
                    return $decryptedToken;
                }
                Log::error("SonarQube token generation response did not contain a token for SubscriptionLicense ID {$licenseId}. Response: " . $response->body());
                return null;
            }
            Log::error("Failed to generate SonarQube API token for SubscriptionLicense ID {$licenseId}. Status: {$response->status()}. Response: " . $response->body());
            return null;

        } catch (RequestException $e) {
            Log::error("HTTP Request Exception while generating SonarQube API token for SubscriptionLicense ID {$licenseId}: " . $e->getMessage() . " - Response: " . ($e->response ? $e->response->body() : 'No response'));
            return null;
        } catch (\Exception $e) { // Catch other exceptions, like the SQL one if $license->save() fails
            // The SQL error you saw would be caught here.
            Log::error("Generic Exception while generating SonarQube API token for SubscriptionLicense ID {$licenseId}: " . $e->getMessage());
            return null;
        }
    }

    public function getDecryptedToken(SubscriptionLicense $license): ?string
    {
        $licenseId = $license->subscription_license_id; // Use correct primary key

        if (empty($license->sonar_api_token)) {
            Log::info("No SonarQube API token found stored for SubscriptionLicense ID {$licenseId}.");
            return null;
        }
        try {
            return AppHelper::decryptString($license->sonar_api_token);
        } catch (\Illuminate\Contracts\Encryption\DecryptException $e) {
            Log::error("Failed to decrypt SonarQube API token for SubscriptionLicense ID {$licenseId}: " . $e->getMessage());
            return null;
        }
    }
}