<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Carbon\Carbon;

class ResetTokenService
{
    public function generateResetToken(string $email): string
    {
        $token = Str::random(60);

        DB::table('password_reset_tokens')->updateOrInsert(
            ['email' => $email],
            ['token' => $token, 'created_at' => Carbon::now()]
        );

        return $token;
    }

    public function validateResetToken(string $token, string $email): ?object
    {
        return DB::table('password_reset_tokens')
            ->where('token', $token)
            ->where('email', $email)
            ->first();
    }

    public function deleteResetToken(string $token, string $email): void
    {
        DB::table('password_reset_tokens')
            ->where('token', $token)
            ->where('email', $email)
            ->delete();
    }

    public function getTokenExpirationMinutes(): int
    {
        return config('auth.passwords.users.expire');
    }
}