<?php

namespace App\Services;

use App\Enums\AuthEvents;
use App\Enums\AuthFailureCauses;
use App\Enums\ValidationMessages;
use App\Models\Log as LogModel;
use App\Models\User;
use App\Traits\Blamable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Jenssegers\Agent\Agent;

class LogService
{
    use Blamable;

    protected string $level = 'info';

    protected string $status = 'success';

    protected string|AuthEvents|ValidationMessages $event;

    protected string $description;

    protected string|ValidationMessages|null $reason;

    protected array $context = [];

    protected string $ip_address;

    protected ?string $email = null;

    protected ?string $user_id = null;

    protected ?Request $request = null;

    /**
     * Set the request.
     */
    public function withRequest(Request $request): self
    {
        $this->request = $request;
        return $this;
    }

    /**
     * Set multiple properties at once.
     *
     * @param string|AuthEvents|ValidationMessages $event
     * @param string $description
     * @param string|ValidationMessages|null $reason
     * @param array $context
     * @param string $ip_address
     * @param string|null $email
     * @param string|null $user_id
     * @return self
     */
    public function props(string | AuthEvents | ValidationMessages $event, string $description, string | ValidationMessages | null $reason = null, array $context = []): self
    {
        $user = self::getBlamable();

        /**
         * Top priority to class email
         * If empty then refer to the blamable email
         * If empty then refer to the request email 
         */
        if (empty($this->email)) {
            if (!empty($user) && !empty($user->email)) {
                $this->email = $user->email;
            } else {
                $this->email = $this->request->email;
            }
        }

        $this->event = $event;
        $this->description = $description;
        $this->reason = $reason;
        $this->context = $context;
        $this->ip_address = $this->request->ip() ?? null;
        $this->user_id = $user->user_id ?? null;

        return $this;
    }

    /**
     * Set the log level.
     */
    public function level(string $level): self
    {
        $this->level = $level;
        return $this;
    }

    /**
     * Set the status to failure.
     */
    public function withFailure(): self
    {
        $this->status = 'failure';
        $this->level('error');
        return $this;
    }

    /**
     * Set the status to failure.
     */
    public function withWarning(): self
    {
        $this->status = 'success';
        $this->level('warning');
        return $this;
    }


    /**
     * Set log level to info.
     */
    public function info(): self
    {
        return $this->level('info');
    }

    /**
     * Set log level to warning.
     */
    public function warning(): self
    {
        return $this->level('warning');
    }

    /**
     * Set log level to error.
     */
    public function error(): self
    {
        return $this->level('error');
    }

    /**
     * Retrieve device information from custom request header.
     */
    public function getDeviceInfo(): array
    {
        if (!$this->request) {
            return [];
        }

        $deviceInfoHeader = $this->request->header('x-device-info') ?? [];


        if (!$deviceInfoHeader) {
            return [];
        }

        $decoded = json_decode($deviceInfoHeader, true);

        return is_array($decoded) ? $decoded : [];
    }

    /**
     * Log authentication events directly to the database.
     */
    public function log(): void
    {
        LogModel::create([
            'level' => $this->level,
            'event' => $this->event,
            'description' => $this->description,
            'reason' => $this->reason,
            'context' => $this->context,
            'ip_address' => $this->ip_address,
            'user_id' => $this->user_id,
            'email' => $this->email,
            'status' => $this->status,
            'device_info' => $this->getDeviceInfo(),
        ]);
    }
}
