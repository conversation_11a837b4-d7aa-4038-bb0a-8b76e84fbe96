<?php

namespace App\Services;

use App\Enums\Subscriptions;
use App\Models\Subscription;
use App\Models\SubscriptionProgress;

class SubscriptionProgressService
{
  protected Subscription $subscription;

  protected string $step_name = Subscriptions::STEP_SELECTED_PLAN->value;

  /**
   * Set the subscription entity.
   * Set the step name.
   */
  public function withSubscription(Subscription $subscription, string $step_name): self
  {
    $this->subscription = $subscription;
    $this->step_name = $step_name;

    return $this;
  }

  /**
   * Log Subscription Step 
   */
  public function stepForward(): void
  {
    SubscriptionProgress::updateOrCreate(
      [
        'subscription_id' => $this->subscription->id,
        'step_name' => $this->step_name,
      ],
      [
        'step_data' => $this->subscription->toArray(),
        'completed_at' => now(),
      ]
    );
  }
}
