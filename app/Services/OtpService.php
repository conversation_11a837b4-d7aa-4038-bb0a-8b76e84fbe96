<?php

namespace App\Services;

use App\Models\User;

class OtpService
{
    protected string $otp;

    protected ?User $user;

    protected int $expireMinutes;

    /**
     * Set the user entity.
     * Set expiry time in minutes.
     */
    public function withUser(User $user, ?int $expireMinutes = null): self
    {
        $this->user = $user;

        if(!empty($expireMinutes))
        {
          $this->expireMinutes = $expireMinutes;
        }

        return $this;
    }

    /**
     *  otp generate
     */
    public function generate(array $range): string
    {
       $this->otp = mt_rand($range[0], $range[1]);

       if(!empty($this->user))
       {
         $this->user->otp = $this->otp;
         $this->user->otp_expires_at = now()->addMinutes($this->expireMinutes);
         $this->user->saveQuietly();
       }

       return $this->otp;
    }

    /**
     *  Reset otp
    */
    public function reset(): void
    {
      if(!empty($this->user))
      {
        $this->user->otp = null;
        $this->user->otp_expires_at = null;
        $this->user->saveQuietly();

        $this->unlock();
      }
    }

    /**
    * Validate otp 
    */
    public function isValid(string $otp): bool
    {
      if(!empty($this->user))
      {
        if($this->user->otp == $otp)
        {
          return true;
        } else if (!$this->isLocked()) {
          $this->incrementOtpAttempt();
        }
      }

      return false;
    }

    /**
    * Increment Otp attempt 
    */
    public function incrementOtpAttempt(): void
    {
      if(!empty($this->user))
      {
        $this->user->otp_attempts = $this->user->otp_attempts + 1;
        $this->user->saveQuietly();
      }
    }

    /**
     *  Lock otp
    */
    public function lock(int $minutes): void
    {
       if(!empty($this->user))
       {
         $this->user->otp_locked_until = now()->addMinutes($minutes);
         $this->user->saveQuietly();
       }
    }

    /**
     *  unlock otp
    */
    public function unlock(): void
    {
       if(!empty($this->user))
       {
         $this->user->otp_attempts = 0;
         $this->user->otp_locked_until = null;
         $this->user->saveQuietly();
       }
    }

    /**
     *  check if otp is locked
    */
    public function isLocked(): bool
    {
       if(!empty($this->user) && !empty($this->user->otp_locked_until)) {
            if ($this->user->otp_locked_until->isPast()) {
                $this->unlock();
                return false; 
            } else {
                return true; 
            }
       }

       return false; 
    }
}
