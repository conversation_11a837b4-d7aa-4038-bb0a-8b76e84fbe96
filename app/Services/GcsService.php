<?php

namespace App\Services;

use App\Enums\DefaultValues;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;
use Exception;

class GcsService
{
  protected string $disk;

  protected string $folder = 'receipts';

  protected string $fileName;

  protected string $filePath;

  /**
   * GcsService constructor. 
   */
  public function __construct()
  {
    $this->disk = config('filesystems.disks.gcs.driver');
  }

  /**
   * Set the folder to use for GCS 
   */
  public function setFolder(string $folder): self
  {
    $this->folder = trim($folder, '/');
    return $this;
  }

  /**
   * Set File Name 
   */
  public function setFileName(string $name): self
  {
    $this->fileName = $name;
    return $this;
  }

  /**
   * Set File Path 
   */
  public function setFilePath(string $path): self
  {
    $this->filePath = $path;
    return $this;
  }

  /**
   * Return File Path 
   */
  public function getFilePath(): string
  {
    return Carbon::now()->format('Y_m_d');
  }

  /**
   * Return File Name 
   */
  public function getFileName(UploadedFile $file): string
  {
    $extension = $file->getClientOriginalExtension();

    return "{$this->fileName}.{$extension}";
  }

  /**
   * Uploads a receipt file to GCS with a unique name
   */
  public function upload(UploadedFile $file): string
  {
    $allowedMimeTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'];

    if (!in_array($file->getMimeType(), $allowedMimeTypes)) {
      throw new Exception("Unsupported file type: {$file->getMimeType()}");
    }

    $path = $this->getFilePath() . "/" . $this->getFileName($file);

    $filePath = "{$this->folder}/{$path}";

    /**
    * Return It If Already Exist 
    */
    if (Storage::disk($this->disk)->exists($filePath)) {
      return $filePath;
    }

    $storeFile = $file->storeAs($this->folder, $path, $this->disk);

    if (!$storeFile) {
      throw new Exception("Failed to upload file to GCS.");
    }

    return $path;
  }

  /**
   * Get a specific file from GCS
   */
  public function getFile(): string
  {
    $diskName = $this->disk;
    $path = "{$this->folder}/{$this->filePath}";

    if (!Storage::disk($diskName)->exists($path)) {
      return '';
    }

    return Storage::disk($diskName)->temporaryUrl(
      $path,
      Carbon::now()->addDay(DefaultValues::GCS_TEMPORARY_LINK_EXPIRATION_DAYS->get())
    );
  }

  /**
   * Delete a specific file from GCS
   */
  public function deleteFile(): bool
  {
    $diskName = $this->disk;
    $path = "{$this->folder}/{$this->filePath}";

    if (!Storage::disk($diskName)->exists($path)) {
      return false;
    }

    return Storage::disk($diskName)->delete($path);
  }
}
