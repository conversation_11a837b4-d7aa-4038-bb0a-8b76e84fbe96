<?php

namespace App\Services;

use App\Enums\DefaultValues;

class MoneyFormatter
{
    protected float $amount;

    protected ?string $currency = null;

    protected bool $abbreviate = false;

    protected bool $asArray = false;
    
    protected string $currencyPosition = 'left' | 'right';

    /**
     * Set the amount and currency 
     */
    public static function price(float | string $amount, ?string $currency = null): self
    {
        $instance = new self();
        $instance->amount = (float) $amount;
        $instance->currency = $currency ?? DefaultValues::CURRENCY->get();
        return $instance;
    }

    /**
     * Abbreviate the amount 
     */
    public function abbreviate(bool $value = true): self
    {
        $this->abbreviate = $value;
        return $this;
    }

    /**
     * Return the formatted amount as an array  
     */
    public function asArray(bool $value = true): self
    {
        $this->asArray = $value;
        return $this;
    }

    /**
     * Set the currency position 
     */
    public function currencyPosition(string $position = "left"): self
    {
        $this->currencyPosition = $position;
        return $this;
    }

    /**
     * Format the amount 
     */
    public function format(): array|string 
    {
        $decimalPlaces = 2;

        $subunitFactor = pow(10, $decimalPlaces);

        $majorUnitAmount = $this->amount / $subunitFactor;

        $formattedMonetaryValue = '';
        $unitAbbreviation = ''; 

        if ($this->abbreviate) { 
            $units = ['', 'K', 'M', 'B'];
            $unitIndex = 0;
            $abbreviatedValue = $majorUnitAmount;

            while (abs($abbreviatedValue) >= 1000 && $unitIndex < count($units) - 1) {
                $abbreviatedValue /= 1000;
                $unitIndex++;
            }
            $unitAbbreviation = $units[$unitIndex];
            $formattedMonetaryValue = number_format(
                $abbreviatedValue,
                ($abbreviatedValue == floor($abbreviatedValue)) ? 0 : 1 // 0 decimals if whole, else 1 for abbreviated
            );
        } else {
            $formattedMonetaryValue = number_format($majorUnitAmount, $decimalPlaces);
        }

        $out = $this->currencyPosition === 'left' 
            ? "{$this->currency} {$formattedMonetaryValue}{$unitAbbreviation}"
            : "{$formattedMonetaryValue}{$unitAbbreviation} {$this->currency}";

        if ($this->asArray) {
            return [
                'currency' => $this->currency,
                'amount_formatted' => $out,
            ];
        }
        return $out;
    }
}
