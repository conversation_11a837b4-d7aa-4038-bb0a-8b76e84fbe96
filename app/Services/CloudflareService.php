<?php

namespace App\Services;

use App\Helpers\AppHelper;
use Illuminate\Support\Facades\Http;

class CloudflareService
{
    protected ?string $zoneId;
    protected ?string $apiToken;

    public function __construct()
    {
        $this->zoneId = config('services.cloudflare.zone_id');
        $this->apiToken = config('services.cloudflare.api_token');
    }

    /**
     * Add a DNS record to Cloudflare
     *
     * @param string $type     DNS record type (A, CNAME, TXT, etc.)
     * @param string $name     Full DNS name (e.g. sub.example.com)
     * @param string $content  IP or target of the record
     * @param int $ttl         Time to live (1 = automatic)
     * @param bool $proxied    Whether Cloudflare proxy is enabled
     *
     * @return array           JSON response decoded to array
     */
    public function addDNSRecord(string $type, string $name, string $content, int $ttl = 1, bool $proxied = false, bool $shouldNormalizeDomain = false): array
    {
        $response = Http::withToken($this->apiToken)
            ->acceptJson()
            ->post("https://api.cloudflare.com/client/v4/zones/{$this->zoneId}/dns_records", [
                'type' => $type,
                'name' => $shouldNormalizeDomain ? AppHelper::normalizeDomain($name) : $name,
                'content' => $content,
                'ttl' => $ttl,
                'proxied' => $proxied,
            ]);

        return $response->json();
    }
}
