<?php

namespace App\Services;

use App\Helpers\RbacHelper;
use App\Models\Role;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class RbacService
{
  protected string $role;

  protected string $guard = 'api';

  protected string $type = RbacHelper::TYPE_CUSTOMER;

  protected ?User $user;


  /**
   * Set the user entity.
   */
  public function withUser(User $user): self
  {
    $this->user = $user;

    return $this;
  }

  /**
   * Set the type.
   */
  public function withType(string $type): self
  {
    $this->type = $type;

    return $this;
  }


  /**
   * Set the role name.
   */
  public function withRole(string $role): self
  {
    $this->role = $role;

    return $this;
  }

  /**
   * Set the guard.
   */
  public function withGuard(string $guard): self
  {
    $this->guard = $guard;

    return $this;
  }

  /**
   * Get Current User Role 
   */
  public function getCurrentRole(): string | null
  {
    return $this->user->getRoleNames()->first();
  }

  /**
   * Check if the user role has changed
   */
  public function roleHasChanged(?string $newRole = null): bool
  {
    $newRole = $this->role ?? $newRole;

    return $this->getCurrentRole() !== $newRole;
  }

  /**
   * Assign a role to a User 
   */
  public function assign(): void
  {
    $existingRole = Role::firstOrCreate(
      ['type' => $this->type, 'name' => $this->role, 'guard_name' => $this->guard]
    );

    DB::table('model_has_roles')->updateOrInsert(
      [
        'model_id' => $this->user->user_id,
        'model_type' => get_class($this->user),
      ],
      [
        'role_id' => $existingRole->id,
      ]
    );
  }
}
