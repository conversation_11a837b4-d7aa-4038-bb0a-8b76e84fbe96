<?php

namespace App\Services;

class NumberFormatter
{
    protected float $amount;

    protected ?string $prefix = null;

    protected bool $abbreviate = false;

    protected bool $useFullWords = false;

    protected bool $asArray = false;

    protected string $prefixPosition = 'left' | 'right';

    protected ?string $parameterName = null;

    protected ?float $total_amount = null;

    /**
     * Set the amount and prefix 
     */
    public static function amount(float $amount, ?string $prefix = null, ?float $total_amount = null): self
    {
        $instance = new self();
        $instance->amount = $amount;
        $instance->prefix = $prefix;
        $instance->total_amount = $total_amount;
        return $instance;
    }

    /**
     * Abbreviate the amount 
     */
    public function abbreviate(bool $value = true): self
    {
        $this->abbreviate = $value;
        return $this;
    }

    /**
     * Use full word format for abbreviations
     */
    public function useFullWords(bool $value = true): self
    {
        $this->useFullWords = $value;
        return $this;
    }

    /**
     * Return the formatted amount as an array  
     */
    public function asArray(bool $value = true): self
    {
        $this->asArray = $value;
        return $this;
    }

    /**
     * Set the prefix position 
     */
    public function prefixPosition(string $position = "left"): self
    {
        $this->prefixPosition = $position;
        return $this;
    }

    /**
     * Set the custom parameter name
     */
    public function parameterName(string $name): self
    {
        $this->parameterName = $name;
        return $this;
    }

    /**
     * Format the amount 
     */
    public function format(): array|string
    {
        $formattedAmount = $this->amount;
        $unit = '';

        /**
         * Check if total amount is set => calculate percentage 
         */
        if (!empty($this->total_amount)) {
           return $this->percentage();
        }

        /**
        * Abbreviate the amount
        * 1K = 1000, 1M = 1000000, 1B = 1000000000, 1T = 1000000000000 
        * Use or not full words for abbreviations
        */
        if ($this->abbreviate) {
            $abbreviations = ['', 'K', 'M', 'B', 'T'];
            $fullWords = ['', 'Thousand', 'Million', 'Billion', 'Trillion'];

            $unitIndex = 0;
            while ($formattedAmount >= 1000 && $unitIndex < count($abbreviations) - 1) {
                $formattedAmount /= 1000;
                $unitIndex++;
            }

            $unit = $this->useFullWords
                ? (!empty($fullWords[$unitIndex]) ? " {$fullWords[$unitIndex]}" : "")
                : $abbreviations[$unitIndex];

            $formattedAmount = number_format($formattedAmount);
        } else {
            $formattedAmount = number_format($formattedAmount);
        }

        /**
        * Prefix Direction 
        */
        $out = $this->prefixPosition === 'left'
            ? "{$this->prefix}{$formattedAmount}{$unit}"
            : "{$formattedAmount}{$unit}{$this->prefix}";

        /**
         * Return formatted amount as array  
         */
        if ($this->asArray) {
            $key = $this->parameterName ?? 'amount';
            return [
                $key => $this->amount,
                "{$key}_formatted" => $out,
            ];
        }

        return $out;
    }

    public function percentage(): string
    {
        if ($this->total_amount == 0.0) {
            return '0.00%';
        }

        $percentage = ($this->amount / $this->total_amount) * 100;
        return number_format($percentage, 2) . $this->prefix;
    }
}
