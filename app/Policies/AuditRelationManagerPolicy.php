<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class AuditRelationManagerPolicy
{
    use HandlesAuthorization;

    /**
     * Determine if the user can audit records.
     */
    public function audit(User $user)
    {
        return $user->hasPermissionTo('audit_records');
    }

    /**
     * Determine if the user can rollback audit records.
     */
    public function rollbackAudit(User $user)
    {
        return $user->hasPermissionTo('rollback_audit');
    }
}
