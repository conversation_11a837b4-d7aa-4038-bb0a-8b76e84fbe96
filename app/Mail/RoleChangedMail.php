<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class RoleChangedMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $user;
    public $previousRole;
    public $newRole;
    public $modifiedBy;

    /**
     * Create a new message instance.
     */
     public function __construct($user, $previousRole, $newRole, $modifiedBy)
    {
        $this->user = $user;
        $this->previousRole = $previousRole;
        $this->newRole = $newRole;
        $this->modifiedBy = $modifiedBy;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: "Your Sonar Role Updated: {$this->previousRole} to {$this->newRole}",
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'emails.role_changed',
            with: [
                'user' => $this->user,
                'previousRole' => $this->previousRole,
                'newRole' => $this->newRole,
                'modifiedBy' => $this->modifiedBy,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
