<?php

namespace App\Mail;

use App\Models\Subscription;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class PaymentRejectedMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public Subscription $subscription;
    public string $rejectionReason;
    public ?string $rejectionDescription;

    /**
     * Create a new message instance.
     */
    public function __construct(Subscription $subscription, string $rejectionReason, ?string $rejectionDescription = null)
    {
        $this->subscription = $subscription;
        $this->rejectionReason = $rejectionReason;
        $this->rejectionDescription = $rejectionDescription;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Payment Rejected for Your Subscription',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'emails.payment_rejected',
            with: [
                'subscription' => $this->subscription,
                'rejectionReason' => $this->rejectionReason,
                'rejectionDescription' => $this->rejectionDescription,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
