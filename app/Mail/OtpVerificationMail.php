<?php

namespace App\Mail;

use App\Enums\ApiSettings;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class OtpVerificationMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public User $user;

    public string $otp;

    public string $action;

    /**
     * Create a new message instance.
     */
    public function __construct(string $otp, User $user, string $action = 'registration')
    {
        $this->otp = $otp;
        $this->user = $user;
        $this->action = $action;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Your OTP Verification Code',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'emails.otp_verification',
            with: [
                'otp' => $this->otp,
                'user' => $this->user,
                'action' => $this->action,
                'otpMinutes' => ApiSettings::OTP_EXPIRY_MINUTES->getValue(),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}