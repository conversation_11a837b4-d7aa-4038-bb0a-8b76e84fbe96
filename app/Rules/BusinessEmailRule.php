<?php

namespace App\Rules;

use App\Enums\ValidationMessages;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class BusinessEmailRule implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $pattern = '/^[a-zA-Z0-9._%+-]+@(?!(gmail\.com|yahoo\.com|hotmail\.com|outlook\.com|aol\.com|icloud\.com|live\.com|protonmail\.com|mail\.com|zoho\.com)$)[a-zA-Z][a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/';
        
        if (!preg_match($pattern, $value)) {
            $fail(ValidationMessages::NON_BUSINESS_EMAIL->description());
        }
    }
}