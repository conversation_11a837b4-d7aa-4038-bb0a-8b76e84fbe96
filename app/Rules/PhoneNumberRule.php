<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use libphonenumber\PhoneNumberUtil;
use libphonenumber\NumberParseException;

class PhoneNumberRule implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if($value === "-")
        {
            $fail("The {$attribute} field is required.");
            return;
        }

        $phoneUtil = PhoneNumberUtil::getInstance();

        try {
            $phoneNumber = $phoneUtil->parse($value);

            if (!$phoneUtil->isValidNumber($phoneNumber)) {
                $fail("The {$attribute} must be a valid phone number.");
            }
        } catch (NumberParseException $e) {
            $fail("The {$attribute} must be a valid phone number.");
        }
    }
}