<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CompanyNameRule implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Check if length is valid (greater than 1, less than 256)
        if (strlen($value) <= 1 || strlen($value) >= 256) {
            $fail('The :attribute must be between 2 and 255 characters.');
            return;
        }

        // Check if it's only spaces or tabs
        if (preg_match('/^[\s\t]+$/', $value)) {
            $fail('The :attribute cannot contain only spaces or tabs.');
            return;
        }

        // Check for spaces or tabs mixed with individual characters (like "1 2 3")
        if (preg_match('/^(\S\s+)+\S$/', $value)) {
            $fail('The :attribute cannot contain individual characters separated by spaces.');
            return;
        }

        // Check for same character repeated 3 or more times with no other characters
        if (preg_match('/^(.)\1{2,}$/', $value)) {
            $fail('The :attribute cannot contain the same character repeated three or more times with no other characters.');
            return;
        }

        // Check if name contains at least one letter
        if (!preg_match('/[\p{L}]/u', $value)) {
            $fail('The :attribute must contain at least one letter.');
            return;
        }

        // Define allowed characters (Unicode letters, numbers, and specified special characters)
        $allowedPattern = '/^[\p{L}0-9\s~`#\+\$@&\(\)\[\]\\\\\/\-_=\'"\{\}<>⟨⟩\.:!?\;^%\*]+$/u';
        
        if (!preg_match($allowedPattern, $value)) {
            $fail('The :attribute contains invalid characters.');
            return;
        }
    }
}