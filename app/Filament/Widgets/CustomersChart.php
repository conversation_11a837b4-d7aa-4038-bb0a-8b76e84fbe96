<?php

namespace App\Filament\Widgets;

use Filament\Widgets\ChartWidget;

class CustomersChart extends ChartWidget
{
    protected static ?int $sort = 3;

    protected static ?string $heading = 'Customers';

    protected function getData(): array
    {
        return [
            'datasets' => [
                [
                    'label' => 'Blog posts created',
                    'data' => [
                        ['x' => 1, 'y' => 10, 'r' => 5],  // January
                        ['x' => 2, 'y' => 5, 'r' => 10],  // February
                        ['x' => 3, 'y' => 2, 'r' => 8],   // March
                        ['x' => 4, 'y' => 21, 'r' => 15], // April
                        ['x' => 5, 'y' => 32, 'r' => 12], // May
                        ['x' => 6, 'y' => 45, 'r' => 18], // June
                        ['x' => 7, 'y' => 74, 'r' => 20], // July
                        ['x' => 8, 'y' => 65, 'r' => 16], // August
                        ['x' => 9, 'y' => 45, 'r' => 14], // September
                        ['x' => 10, 'y' => 77, 'r' => 22], // October
                        ['x' => 11, 'y' => 89, 'r' => 25], // November
                        ['x' => 12, 'y' => 50, 'r' => 17], // December
                    ],
                    'backgroundColor' => '#36A2EB',
                    'borderColor' => '#9BD0F5',
                ],
            ],
        ];
    }

    protected function getType(): string
    {
        return 'bubble';
    }
}
