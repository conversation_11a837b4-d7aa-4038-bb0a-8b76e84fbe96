<?php

namespace App\Filament\Resources\PlatformCountryResource\Pages;

use App\Filament\Resources\PlatformCountryResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditPlatformCountry extends EditRecord
{
    protected static string $resource = PlatformCountryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        if (empty($data['password'])) {
            unset($data['password']); 
        }

        return $data;
    }
}
