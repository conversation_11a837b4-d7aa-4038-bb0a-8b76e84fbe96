<?php

namespace App\Filament\Resources;

use App\Filament\Resources\NotificationTemplateResource\Pages;
use App\Models\NotificationTemplate;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Traits\DynamicFields;
use App\Traits\HasShieldAccess;

class NotificationTemplateResource extends Resource
{
    use DynamicFields, HasShieldAccess;

    protected static ?string $model = NotificationTemplate::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = 'Notifications Setup';

    protected static ?int $navigationSort = 3;

    /**
     * Manual form definition is necessary to use the Repeater component for managing template translations.
     */
    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('template_key')
                ->required()
                ->maxLength(100)
                ->rule(function (callable $get) {
                    return \Illuminate\Validation\Rule::unique('notification_templates', 'template_key')->ignore($get('id'), 'template_id');
                }),
            Forms\Components\Repeater::make('translations')->relationship()->schema([
                Forms\Components\Select::make('language_code')
                    ->label('Language')
                    ->options(config('settings.system_languages'))
                    ->searchable()
                    ->required()
                    ->default('en'),
                Forms\Components\TextInput::make('template_location')->required()->default(config('settings.emails.default_path'))->readOnly(),
                Forms\Components\Textarea::make('template_string')->required()->helperText('Use {variable} for dynamic content.'),
            ])->columnSpanFull(),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('template_key')->searchable(),
                Tables\Columns\TextColumn::make('translations.language_code')
                    ->label('Languages')
                    ->formatStateUsing(fn ($state) => is_array($state) ? implode(', ', $state) : $state)
                    ->sortable(),
                Tables\Columns\TextColumn::make('translations.template_location')
                    ->label('Template Locations')
                    ->formatStateUsing(fn ($state) => is_array($state) ? implode(', ', $state) : $state),
                Tables\Columns\TextColumn::make('translations.template_string')
                    ->label('Template Strings')
                    ->limit(30)
                    ->formatStateUsing(fn ($state) => is_array($state) ? implode(', ', array_map(fn($s) => \Illuminate\Support\Str::limit($s, 30), $state)) : $state),
                Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            \Tapp\FilamentAuditing\RelationManagers\AuditsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNotificationTemplates::route('/'),
            'create' => Pages\CreateNotificationTemplate::route('/create'),
            'edit' => Pages\EditNotificationTemplate::route('/{record}/edit'),
        ];
    }
}
