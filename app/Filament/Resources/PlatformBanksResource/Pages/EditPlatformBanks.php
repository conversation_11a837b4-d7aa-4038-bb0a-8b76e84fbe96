<?php

namespace App\Filament\Resources\PlatformBanksResource\Pages;

use App\Filament\Resources\PlatformBanksResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditPlatformBanks extends EditRecord
{
    protected static string $resource = PlatformBanksResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        if (empty($data['password'])) {
            unset($data['password']); 
        }

        return $data;
    }
}
