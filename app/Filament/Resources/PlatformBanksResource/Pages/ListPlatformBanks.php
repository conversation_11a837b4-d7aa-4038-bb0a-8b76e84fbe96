<?php

namespace App\Filament\Resources\PlatformBanksResource\Pages;

use App\Filament\Resources\PlatformBanksResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPlatformBanks extends ListRecords
{
    protected static string $resource = PlatformBanksResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
