<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LogResource\Pages;
use App\Models\Log;
use App\Traits\HasShieldAccess;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Traits\DynamicFields;

class LogResource extends Resource
{
    use DynamicFields, HasShieldAccess;

    protected static ?string $model = Log::class;

    protected static ?string $navigationIcon = 'heroicon-o-newspaper';

    protected static ?string $navigationGroup = 'CMS';

    protected static ?int $navigationSort = 1;

    /**
     * Required form fields
     * Set to ['*'] to make all columns required
    */
    protected static array $requiredFields = ['name', 'email']; 

    /**
     * Excluded form fields
     * By default these are excluded: ['id', 'created_at', 'updated_at', 'created_by', 'updated_by', 'deleted_at', 'deleted_by']
    */
    protected static array $excludedFields = [];

    /**
     * Wether the field should be a select in both Form & Table
     * Don't specify options in order to set the defaults: (Active, Inactive, Deleted)
    */
    protected static array $dropdownFields = [
        'status' => [
            'options' => [
                'success' => 'Success',
                'failure' => 'Failure',
            ],
            'searchable' => true,
        ],
    ];

    /**
     * Table filter fields
     * Set to ['*'] to make all columns filterable
    */
    protected static array $filterableFields = ['*'];

    /**
     * Excluded Table columns
     * By default these are excluded: ['password', 'remember_token']
    */
    protected static array $excludedTableFields = [];

    /**
     * Sortable Table columns
     * Set to ['*'] to make all columns sortable
    */
    protected static array $sortableTableFields = ['*'];

    /**
     * colors fields
    */
    protected static array $colorFields = ['color'];

    /**
     * Set it to false in order to disable table search
    */
    protected static bool $tableSearch = true;

    /**
     * map keys => labels
    */
    protected static array $attributesLabels = [
         'user_id' => 'User',
    ];

    public static function form(Form $form): Form
    {
        return $form
            ->schema(self::getFormFields(self::$model));
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns(self::getTableFields(self::$model))
            ->filters(self::getTableFilters(self::$model))
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                // Removed all bulk actions to prevent deletion
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLogs::route('/'),
            'view' => Pages\ViewLog::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

}
