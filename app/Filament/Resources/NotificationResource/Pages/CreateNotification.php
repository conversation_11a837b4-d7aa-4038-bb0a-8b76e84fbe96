<?php

namespace App\Filament\Resources\NotificationResource\Pages;

use App\Filament\Resources\NotificationResource;
use App\Models\NotificationType;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateNotification extends CreateRecord
{
    protected static string $resource = NotificationResource::class;

     /**
     * Override the default record creation logic to create a separate
     * notification record for each delivery channel specified by the
     * selected notification type.
     *
     * @param array $data The validated data from the creation form.
     * @return Model The last created Notification model instance.
     */
    protected function handleRecordCreation(array $data): Model
    {
        $notificationType = NotificationType::find($data['notification_type_id']);

        $channels = $notificationType?->default_channels;

        if ($notificationType && is_array($channels) && !empty($channels)) {
            $lastCreatedRecord = null;

            foreach ($channels as $channel) {
                $recordData = $data;

                $recordData['delivery_channels'] = [$channel];

                $lastCreatedRecord = static::getModel()::create($recordData);
            }

            return $lastCreatedRecord;

        } else {
            return static::getModel()::create($data);
        }
    }
}
