<?php

namespace App\Filament\Resources;

use App\Enums\Notifications;
use App\Filament\Resources\NotificationTypeResource\Pages;
use App\Models\NotificationType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Traits\DynamicFields;
use App\Traits\HasShieldAccess;

class NotificationTypeResource extends Resource
{
    use DynamicFields, HasShieldAccess;

    protected static ?string $model = NotificationType::class;

    protected static ?string $navigationIcon = 'heroicon-o-tag';

    protected static ?string $navigationGroup = 'Notifications Setup';

    protected static ?int $navigationSort = 2;

    protected static array $filterableFields = ['name'];

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('name')->required()->unique(ignoreRecord: true)->maxLength(100)->columnSpanFull(),
            Forms\Components\Select::make('default_channels')
                ->multiple()
                ->options(Notifications::getNotificationChannelOptions())
                ->helperText('Default channels like mail, database, etc.'),
            Forms\Components\Select::make('event')
                ->native(false)
                ->searchable()
                ->options(Notifications::getNotificationEventOptions()),
            Forms\Components\KeyValue::make('placeholders')->columnSpanFull(),
            Forms\Components\Repeater::make('translations')
                ->relationship(
                    name: 'translations',
                    modifyQueryUsing: fn($query) => $query->withoutGlobalScopes() 
                )
                ->schema([
                    Forms\Components\Select::make('language_code')
                        ->label('Language')
                        ->options(config('settings.system_languages'))
                        ->searchable()
                        ->required()
                        ->default('en'),

                    Forms\Components\TextInput::make('description')
                        ->required()
                        ->maxLength(255),
                ])
                ->columnSpanFull()
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->searchable(),
                Tables\Columns\TagsColumn::make('default_channels')
                    ->label('Default Channels')
                    ->separator(', ')
                    ->getStateUsing(fn($record) => $record->default_channels),
                Tables\Columns\TextColumn::make('event')
                    ->label('Event')
                    ->getStateUsing(fn($record) => Notifications::getNotificationEventOptions()[$record->event] ?? $record->event),
                Tables\Columns\TextColumn::make('placeholders')
                    ->label('Placeholders')
                    ->formatStateUsing(function ($state) {
                        if (is_array($state)) {
                            return collect($state)->map(fn($v, $k) => "$k: $v")->implode(', ');
                        }
                        return $state;
                    }),
                Tables\Columns\TextColumn::make('translations')
                    ->label('Translations')
                    ->formatStateUsing(function ($state, $record) {
                        return $record->translations
                            ->map(fn($t) => ($t->language_code ?? '') . ': ' . ($t->description ?? ''))
                            ->implode('<br>');
                    })
                    ->html(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            \Tapp\FilamentAuditing\RelationManagers\AuditsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNotificationTypes::route('/'),
            'create' => Pages\CreateNotificationType::route('/create'),
            'edit' => Pages\EditNotificationType::route('/{record}/edit'),
        ];
    }
}
