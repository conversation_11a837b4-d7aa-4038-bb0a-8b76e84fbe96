<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SuspensionReasonResource\Pages;
use App\Filament\Resources\SuspensionReasonResource\RelationManagers;
use App\Models\SuspensionReason;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Traits\DynamicFields;
use App\Traits\HasShieldAccess;

class SuspensionReasonResource extends Resource
{
    use DynamicFields, HasShieldAccess;

    protected static ?string $model = SuspensionReason::class;

    protected static ?string $navigationIcon = 'heroicon-o-exclamation-triangle';
    protected static ?string $navigationGroup = 'System Management';
    protected static ?int $navigationSort = 6;

    /**
     * Required form fields
     * Set to ['*'] to make all columns required
    */
    protected static array $requiredFields = ['name']; 

    /**
     * Excluded form fields
     * By default these are excluded: ['id', 'created_at', 'updated_at', 'created_by', 'updated_by', 'deleted_at', 'deleted_by']
    */
    protected static array $excludedFields = ['suspension_reasons_id', 'slug'];

    /**
     * Wether the field should be a select in both Form & Table
     * Don't specify options in order to set the defaults: (Active, Inactive, Deleted)
    */
    protected static array $dropdownFields = [
    ];

    /**
     * Table filter fields
     * Set to ['*'] to make all columns filterable
    */
    protected static array $filterableFields = ['id', 'name'];

    /**
     * Excluded Table columns
     * By default these are excluded: ['password', 'remember_token']
    */
    protected static array $excludedTableFields = [];

    /**
     * Sortable Table columns
     * Set to ['*'] to make all columns sortable
    */
    protected static array $sortableTableFields = ['*'];

    /**
     * colors fields
    */
    protected static array $colorFields = ['color'];

    /**
     * Set it to false in order to disable table search
    */
    protected static bool $tableSearch = true;

    /**
     * map keys => labels
    */
    protected static array $attributesLabels = [
        // 'sector_id' => 'Sectors',
        // 'industry_id' => 'Industries',
    ];

    public static function form(Form $form): Form
    {
        return $form
            ->schema(self::getFormFields(self::$model));
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableFields(self::$model))
            ->filters([
                ...self::getTableFilters(self::$model),
                Tables\Filters\TrashedFilter::make()
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            \Tapp\FilamentAuditing\RelationManagers\AuditsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSuspensionReasons::route('/'),
            'create' => Pages\CreateSuspensionReason::route('/create'),
            'edit' => Pages\EditSuspensionReason::route('/{record}/edit'),
        ];
    }

}
