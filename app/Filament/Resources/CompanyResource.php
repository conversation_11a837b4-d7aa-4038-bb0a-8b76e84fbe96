<?php

namespace App\Filament\Resources;

use App\Filament\RelationManagers\CustomAuditsRelationManager;
use App\Filament\Resources\CompanyResource\Pages;
use App\Helpers\CustomerHelper;
use App\Helpers\StatusHelper;
use App\Models\Company;
use App\Models\Country;
use App\Models\Currency;
use App\Rules\BusinessEmailRule;
use App\Traits\DynamicFields;
use App\Traits\HasShieldAccess;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use libphonenumber\PhoneNumberUtil;
use libphonenumber\NumberParseException;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;
use Ysfkaya\FilamentPhoneInput\PhoneInputNumberType;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

class CompanyResource extends Resource
{
    use DynamicFields, HasShieldAccess;

    protected static ?string $model = Company::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';
    protected static ?string $navigationLabel = 'Customers | Companies';
    protected static ?string $modelLabel = 'Customers Company';
    protected static ?string $navigationGroup = 'Content Management';
    protected static ?string $slug = 'customers-companies';

    protected static ?int $navigationSort = 1;

    /**
     * Required form fields
     * Set to ['*'] to make all columns required
     */
    protected static array $requiredFields = [
        'status_id',
        'countries',
        'sectors',
        'industries',
        'website',
        'number_of_employees'
    ];

    /**
     * Excluded form fields
     * By default these are excluded: ['id', 'created_at', 'updated_at', 'created_by', 'updated_by', 'deleted_at', 'deleted_by']
     */
    protected static array $excludedFields = [
        'name',
        'company_id',
        'registration_type',
        'public_company_id',
        'last_activity_at'
    ];

    /**
     * Wether the field should be a select in both Form & Table
     * Don't specify options in order to set the defaults: (Active, Inactive, Deleted)
     */
    protected static array $dropdownFields = [
        'status_id' => [
            'relationship' => [
                'model' => \App\Models\Status::class,
                'titleAttribute' => 'name',
                'valueAttribute' => 'status_id',
                'statusType' => \App\Helpers\StatusHelper::STATUS_TYPE_CUSTOMER,
            ],
            'searchable' => true,
            'default' => 'pending',
        ],
        'timezone' => [
            'timezones' => true,
            'searchable' => true
        ]
    ];

    /**
     * Table filter fields
     * Set to ['*'] to make all columns filterable
     */
    protected static array $filterableFields = [
        'company_id',
        'public_company_id',
        'name',
        'timezone',
        'status_id',
        'registration_type',
    ];

    /**
     * Excluded Table columns
     * By default these are excluded: ['password', 'remember_token']
     */
    protected static array $excludedTableFields = [
        //        'last_activity_at',
        //        'created_at',
        //        'updated_at',
        //        'deleted_at',
        //        'created_by',
        //        'updated_by',
        //        'deleted_by'
    ];

    public static function getModelDeletedNotificationMessage(): string
    {
        return 'This action will deactivate the customer accounts and restrict access. Are you sure?';
    }

    /**
     * Sortable Table columns
     * Set to ['*'] to make all columns sortable
     */
    protected static array $sortableTableFields = ['*'];

    /**
     * colors fields
     */
    protected static array $colorFields = ['color'];

    /**
     * Set it to false in order to disable table search
     */
    protected static bool $tableSearch = true;

    /**
     * map keys => labels
     */
    protected static array $attributesLabels = [
        'status_id' => 'Status',
    ];

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->rules([
                        'required',
                        'string',
                        new \App\Rules\CompanyNameRule(),
                    ])
                    ->validationMessages([
                        'required' => 'The company name is required.',
                    ]),
                ...self::getFormFields(self::$model),
                \Filament\Forms\Components\Hidden::make('created_by')
                    ->dehydrated(fn ($livewire) => $livewire instanceof Pages\CreateCompany), // Only dehydrate on create
                \Filament\Forms\Components\TextInput::make('registration_type')
                    ->default(CustomerHelper::CUSTOMER_REGISTRATION_TYPE_INVITED_BY_OPERATOR)
                    ->disabled()
                    ->dehydrated(true),
                Forms\Components\Section::make('Primary Customer')
                    ->description('Main point of contact for this company')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('primary_contact.name')
                                    ->label('Name')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('primary_contact.email')
                                    ->label('Email')
                                    ->email()
                                    ->required()
                                    ->unique(
                                        table: 'users',
                                        column: 'email',
                                        ignorable: fn($record) => $record?->primaryContact
                                    )
                                    ->validationMessages([
                                        'unique' => 'This email is already associated with another customer. Please use a different email.',
                                    ])
                                    ->rules([
                                        'required',
                                        'string',
                                        'email',
                                        'max:255',
                                        new BusinessEmailRule(),
                                    ]),
                                PhoneInput::make('primary_contact.phone_number')
                                    ->label('Phone Number')
                                    ->required()
                                    ->displayNumberFormat(PhoneInputNumberType::INTERNATIONAL) // Show in international format with country code
                                    ->inputNumberFormat(PhoneInputNumberType::E164) // Save in E164 format (standard with country code)
                                    ->initialCountry('SA')
                                    ->countryOrder(['SA', 'AE', 'QA', 'KW', 'BH', 'EG', 'LB', 'OM']) // Prioritize these countries
                                    // Add validation using libphonenumber directly
                                    ->beforeStateDehydrated(function ($component, $state) {
                                        if ($state) {
                                            try {
                                                // Validate the phone number using libphonenumber directly
                                                $phoneUtil = PhoneNumberUtil::getInstance();
                                                $phoneProto = $phoneUtil->parse($state, 'SA'); // Default to Saudi Arabia if no country code
                                                if (!$phoneUtil->isValidNumber($phoneProto)) {
                                                    // Show notification for invalid phone number
                                                    Notification::make()
                                                        ->danger()
                                                        ->title('Invalid Phone Number')
                                                        ->body('The phone number format is invalid. Please check and try again.')
                                                        ->persistent()
                                                        ->send();

                                                    // Clear the invalid phone number
                                                    // $component->state(null);
                                                    throw \Illuminate\Validation\ValidationException::withMessages([
                                                        $component->getName() => 'The phone number format is invalid.',
                                                    ]);
                                                }

                                                // Format to E.164 for consistent storage
//                                                $e164Number = $phoneUtil->format($phoneProto, PhoneNumberFormat::E164);
//                                                $component->state($e164Number);

                                                // Custom format: +<countryCode>-<nationalNumber>
                                                $formattedNumber = '+' . $phoneProto->getCountryCode() . '-' . $phoneProto->getNationalNumber();
                                                $component->state($formattedNumber);

                                            } catch (NumberParseException $e) {
                                                // Show notification for exception
                                                Notification::make()
                                                    ->danger()
                                                    ->title('Invalid Phone Number')
                                                    ->body('The phone number format is invalid: ' . $e->getMessage())
                                                    ->persistent()
                                                    ->send();

                                                // Clear the invalid phone number
                                                // $component->state(null);
                                                throw new \Exception('The phone number format is invalid: ' . $e->getMessage());
                                            }
                                        }
                                    }),
                                Forms\Components\Select::make('primary_contact.timezone')
                                    ->label('Timezone')
                                    ->options(self::getTimezones())
                                    ->searchable(),
                            ]),
                    ]),
                Forms\Components\Section::make('Company Details')
                    ->description('you can select multiple choices here')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('countries')
                                    ->label('Countries')
                                    ->relationship('countries', 'name', fn($query) => $query->sorted())
                                    ->getOptionLabelFromRecordUsing(fn($record) => $record->emoji . ' ' . $record->name)
                                    ->multiple()
                                    ->preload()
                                    ->searchable()
                                    ->required()
                                    ->rules(['required', 'array', 'min:1']),
                                Forms\Components\Select::make('sectors')
                                    ->label('Sectors')
                                    ->relationship('sectors', 'name')
                                    ->multiple()
                                    ->preload()
                                    ->searchable()
                                    ->required()
                                    ->rules(['required', 'array', 'min:1']),
                            ]),
                        Forms\Components\Select::make('industries')
                            ->label('Industries')
                            ->relationship('industries', 'name')
                            ->multiple()
                            ->preload()
                            ->searchable()
                            ->required()
                            ->rules(['required', 'array', 'min:1']),
                    ]),

                Forms\Components\Section::make('Company Addresses')
                    ->schema([
                        Forms\Components\Repeater::make('addresses')
                            ->label('Address')
                            ->relationship('addresses')
                            ->schema([
                                Forms\Components\TextInput::make('tax_id')
                                    ->required(),
                                Forms\Components\TextInput::make('commercial_registration_number')
                                    ->required(),
                                Forms\Components\Select::make('country_id')
                                    ->label('Country')
                                    ->options(function () {
                                        return Country::sorted()
                                            ->get()
                                            ->mapWithKeys(function ($country) {
                                                // Concatenate emoji and name
                                                return [$country->id => $country->emoji . ' ' . $country->name];
                                            });
                                    })
                                    ->searchable()
                                    ->required()
                                    ->required(),
                                Forms\Components\TextInput::make('city'),
                                Forms\Components\TextInput::make('state')
                                    ->required(),
                                Forms\Components\TextInput::make('zip')
                                    ->required(),
                                Forms\Components\Select::make('currency_code')
                                    ->options(Currency::all()->pluck('name', 'iso'))
                                    ->searchable()
                                    ->required(),
                                Forms\Components\Textarea::make('address_1')
                                    ->required()
                                    ->columnSpan(2),
                                Forms\Components\Select::make('address_type')
                                    ->options([
                                        'company_address' => 'Company Address',
                                        'billing_address' => 'Billing Address',
                                    ])
                                    ->searchable()
                                    ->required(),
                                Forms\Components\Select::make('platform_bank_id')
                                    ->relationship('platformBank', 'bank_name')
                                    ->searchable()
                                    ->preload()
                                    ->required()
                            ])
                            ->columnSpanFull()
                            ->columns(3)
                            ->defaultItems(1)
                            ->addable(true)
                            ->addActionLabel('Add Company Address')
                            ->deletable(true),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ...self::getTableFields(self::$model),
                Tables\Columns\TextColumn::make('primaryContact.name')
                    ->label('Primary Customer')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->whereHas('primaryContact', function (Builder $query) use ($search): Builder {
                            return $query->where('name', 'like', "%{$search}%");
                        });
                    })
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query->orderBy(
                            User::select('name')
                                ->whereColumn('companies.company_id', 'users.company_id')
                                ->where('is_primary_customer', true)
                                ->where('type', 'customer')
                                ->limit(1),
                            $direction
                        );
                    }),
                Tables\Columns\TextColumn::make('primaryContact.email')
                    ->label('Contact Email')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->whereHas('primaryContact', function (Builder $query) use ($search): Builder {
                            return $query->where('email', 'like', "%{$search}%");
                        });
                    })
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query->orderBy(
                            User::select('email')
                                ->whereColumn('companies.company_id', 'users.company_id')
                                ->where('is_primary_customer', true)
                                ->where('type', 'customer')
                                ->limit(1),
                            $direction
                        );
                    }),
                Tables\Columns\TextColumn::make('status.name')
                    ->label('Status')
                    ->sortable(),
                Tables\Columns\TextColumn::make('countries.name')
                    ->label('Countries')
                    ->listWithLineBreaks()
                    ->limitList(2)
                    ->expandableLimitedList(),
                Tables\Columns\TextColumn::make('sectors.name')
                    ->label('Sectors')
                    ->listWithLineBreaks()
                    ->limitList(2)
                    ->expandableLimitedList(),
                Tables\Columns\TextColumn::make('industries.name')
                    ->label('Industries')
                    ->listWithLineBreaks()
                    ->limitList(2)
                    ->expandableLimitedList(),
            ])
            ->filters([
                ...self::getTableFilters(self::$model),
                Tables\Filters\SelectFilter::make('countries')
                    ->relationship('countries', 'name', fn($query) => $query->sorted())
                    ->getOptionLabelFromRecordUsing(fn($record) => $record->emoji . ' ' . $record->name)
                    ->multiple()
                    ->preload()
                    ->searchable(),
                Tables\Filters\SelectFilter::make('sectors')
                    ->relationship('sectors', 'name')
                    ->multiple()
                    ->preload()
                    ->searchable(),
                Tables\Filters\SelectFilter::make('industries')
                    ->relationship('industries', 'name')
                    ->multiple()
                    ->preload()
                    ->searchable(),
                Tables\Filters\TrashedFilter::make()
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->modalDescription(self::getModelDeletedNotificationMessage()),
                Tables\Actions\RestoreAction::make()
                    ->after(fn() => Notification::make()->success()->title('Post Restored')->send()),
                Tables\Actions\ForceDeleteAction::make()
                    ->after(fn() => Notification::make()->success()->title('Post Permanently Deleted')->send()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->modalDescription(self::getModelDeletedNotificationMessage()),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            CustomAuditsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCompanies::route('/'),
            'create' => Pages\CreateCompany::route('/create'),
            'view' => Pages\ViewCompany::route('/{record}'),
            'edit' => Pages\EditCompany::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
