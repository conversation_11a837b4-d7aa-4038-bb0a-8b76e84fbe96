<?php

namespace App\Filament\Resources\CompanyResource\Pages;

use App\Filament\Resources\CompanyResource;
use App\Helpers\StatusHelper;
use Filament\Resources\Pages\CreateRecord;

class CreateCompany extends CreateRecord
{
    protected static string $resource = CompanyResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        /**
        * Change Company Status 
        */
        $data['status_id'] = StatusHelper::getStatusByAttributeName('name', 'pending');

        // Store primary contact data in request
        request()->merge(['primary_contact' => $data['primary_contact'] ?? null]);
        unset($data['primary_contact']);

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
