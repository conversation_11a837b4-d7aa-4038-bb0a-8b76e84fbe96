<?php

namespace App\Filament\Resources\RoleResource\Pages;

use App\Filament\Resources\RoleResource;
use App\Models\Role;
use <PERSON>zhanSalleh\FilamentShield\Support\Utils;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class CreateRole extends CreateRecord
{
    protected static string $resource = RoleResource::class;

    public Collection $permissions;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $lastRole = Role::latest('id')->first();

        $data['sort_order'] = !empty($lastRole) ? $lastRole->sort_order + 1 : 1; 

        $this->permissions = collect($data)
            ->filter(function ($permission, $key) {
                return ! in_array($key, ['type', 'name', 'guard_name', 'sort_order', 'select_all', Utils::getTenantModelForeignKey()]);
            })
            ->values()
            ->flatten()
            ->unique();

        if (Arr::has($data, Utils::getTenantModelForeignKey())) {
            return Arr::only($data, ['type', 'name', 'guard_name', 'sort_order', Utils::getTenantModelForeignKey()]);
        }

        return Arr::only($data, ['type', 'name', 'guard_name', 'sort_order']);
    }

    protected function afterCreate(): void
    {
        $permissionModels = collect();
        $permissionIds = collect();

        $guard_name = $this->data['guard_name'];
        $this->permissions->each(function ($permission) use ($permissionModels, $permissionIds, $guard_name) {
            $permissionModel = Utils::getPermissionModel()::firstOrCreate([
                /** @phpstan-ignore-next-line */
                'name' => $permission,
                'guard_name' => $guard_name,
            ]);

            $permissionModels->push($permissionModel);

            $permissionIds->push($permissionModel->id);
        });

        if($guard_name == 'web'){ $this->record->syncPermissions($permissionModels); }

        if($guard_name == 'api'){ $this->syncCustomersPermissions($permissionIds); }
    }

    private function syncCustomersPermissions($permissionIds)
    {
        $this->record->permissions()->sync($permissionIds);
    }
}
