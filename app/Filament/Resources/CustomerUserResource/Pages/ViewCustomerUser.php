<?php

namespace App\Filament\Resources\CustomerUserResource\Pages;

use App\Filament\Resources\CustomerUserResource;
use App\Helpers\InvitationHelper;
use App\Helpers\StatusHelper;
use App\Models\User;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;

class ViewCustomerUser extends ViewRecord
{
    protected static string $resource = CustomerUserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('resendInvitation')
                ->label('Resend Invitation')
                ->icon('heroicon-o-envelope')
                ->color('warning')
                ->requiresConfirmation()
                ->modalHeading('Resend Invitation')
                ->modalDescription('Are you sure you want to resend the invitation email to this user?')
                ->modalSubmitActionLabel('Yes, Resend')
                ->visible(fn (User $record): bool => !$record->accepted &&
                    $record->status_id != StatusHelper::getStatusByAttributeName('name', 'active', StatusHelper::STATUS_TYPE_USER) &&
                    auth()->user()->can('update_customer::user'))
                ->action(function () {
                    if ($user = $this->record) {
                        InvitationHelper::sendUserInvitation($user->email, $user->name);
                        Notification::make()
                            ->title('Invitation email has been sent to ' . $user->email)
                            ->success()
                            ->send();
                    } else {
                        Notification::make()
                            ->title('User has no associated company')
                            ->danger()
                            ->send();
                    }
                }),
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }
}
