<?php

namespace App\Filament\Resources\CustomerUserResource\Pages;

use App\Filament\Resources\CustomerUserResource;
use App\Mail\RoleChangedMail;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Mail;
use Filament\Notifications\Notification;

class EditCustomerUser extends EditRecord
{
    protected static string $resource = CustomerUserResource::class;

    protected $previousRole = null;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }

    protected function beforeSave(): void
    {
        $user = $this->record;
        $user->load('roles');
        
        // Store the current role before saving
        $this->previousRole = $user->roles->first()?->name ?? 'No Role';
    }

    protected function afterSave(): void
    {
        $user = $this->record;

        // Reload roles to get the current state after save
        $user->load('roles');
        $newRole = $user->roles->first()?->name ?? 'No Role';

        // Check if role has changed
        if ($this->previousRole !== $newRole) {
            $modifiedBy = auth()->user()->name ?? 'System';

            // Send email notification
            Mail::to($user->email)->queue(new RoleChangedMail(
                $user,
                $this->previousRole,
                $newRole,
                $modifiedBy
            ));

            // Show notification in admin panel
            Notification::make()
                ->title('Role change notification sent to ' . $user->email)
                ->success()
                ->send();
        }
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Customer user updated')
            ->body('The customer user has been updated successfully.');
    }
}
