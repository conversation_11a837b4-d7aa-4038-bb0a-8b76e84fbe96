<?php

namespace App\Filament\Resources\CustomerUserResource\Pages;

use App\Filament\Resources\CustomerUserResource;
use App\Helpers\InvitationHelper;
use Filament\Resources\Pages\CreateRecord;

class CreateCustomerUser extends CreateRecord
{
    protected static string $resource = CustomerUserResource::class;

    protected function afterCreate(): void
    {
        // Send an invitation email to the newly created user
        $user = $this->record;

        InvitationHelper::sendUserInvitation($user->email, $user->name);
    }
}
