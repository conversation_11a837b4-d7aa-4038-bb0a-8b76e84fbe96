<?php

namespace App\Filament\Resources;

use App\Filament\RelationManagers\CustomAuditsRelationManager;
use App\Filament\Resources\AdminUserResource\Pages;
use App\Helpers\RbacHelper;
use App\Models\Company;
use App\Models\Status;
use App\Models\User;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Traits\DynamicFields;

class AdminUserResource extends Resource
{
    use DynamicFields;

    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-circle';

    protected static ?string $navigationGroup = 'Content Management';

    protected static ?string $navigationLabel = 'Admin Users';

    protected static ?int $navigationSort = 1;

    /**
     * Required form fields
     * Set to ['*'] to make all columns required
    */
    protected static array $requiredFields = [
        'name',
        'email',
        'phone_number',
        'password',
        'status_id',
        'roles'
    ];

    /**
     * Excluded form fields
     * By default these are excluded: ['id', 'created_at', 'updated_at', 'created_by', 'updated_by', 'deleted_at', 'deleted_by', 'two_factor_secret', 'two_factor_recovery_codes', 'two_factor_confirmed_at', 'otp', 'otp_expires_at']
    */
    protected static array $excludedFields = [
        'otp',
        'public_user_id',
        'user_id',
        'remember_token',
        'email_verified_at',
        'accepted',
        'last_activity_at',
        'description',
        'login_attempts',
        'otp_attempts',
        'locked',
        'type',
        'company_id',
        'is_primary_customer',
        'locked_until',
        'otp_locked_until',
    ];

    /**
     * Wether the field should be a select in both Form & Table
     * Don't specify options in order to set the defaults: (Active, Inactive, Deleted)
    */
    protected static array $dropdownFields = [
        'timezone' => [
            'timezones' => true,
            'searchable' => true,
        ],
        'company_id' => [
            'relationship' => [
                'model' => Company::class,
                'titleAttribute' => 'name',
                'valueAttribute' => 'company_id',
            ],
            'searchable' => true,
            'placeholder' => 'Select a company',
        ],
        'status_id' => [
            'relationship' => [
                'model' => Status::class,
                'titleAttribute' => 'name',
                'valueAttribute' => 'status_id',
            ],
            'searchable' => true,
        ],
        'accepted' => [
            'options' => [
                '1' => 'Yes',
                '0' => 'No',
            ],
        ],
        'locked' => [
            'options' => [
                '1' => 'Yes',
                '0' => 'No',
            ],
        ],
    ];

    /**
     * Table filter fields
     * Set to ['*'] to make all columns filterable
    */
    protected static array $filterableFields = [
        'user_id',
        'public_user_id',
        'name',
        'email',
        'phone_number',
        'accepted',
        'locked',
        'status_id',
    ];

    /**
     * Excluded Table columns
     * By default these are excluded: ['password', 'remember_token']
    */
    protected static array $excludedTableFields = [
        'company_id',
        'type',
        'is_primary_customer',
    ];

    /**
     * Sortable Table columns
     * Set to ['*'] to make all columns sortable
    */
    protected static array $sortableTableFields = ['*'];

    /**
     * colors fields
     */
    protected static array $colorFields = ['color'];

    /**
     * Set it to false in order to disable table search
    */
    protected static bool $tableSearch = true;

    /**
     * map keys => labels
    */
    protected static array $attributesLabels = [
         'company_id' => 'Company',
         'status_id' => 'User Status',
    ];

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                ...self::getFormFields(self::$model),
                \Filament\Forms\Components\Hidden::make('type')
                    ->default(RbacHelper::TYPE_ADMIN),
                \Filament\Forms\Components\Select::make('roles')
                    ->relationship('roles', 'name', fn(Builder $query) => $query->where('type', RbacHelper::TYPE_ADMIN))
                    ->multiple()
                    ->preload()
                    ->searchable()
                    ->required()
                    ->rules(['required', 'array', 'min:1']),
                \Filament\Forms\Components\Grid::make(2)
                    ->schema([
                        \Filament\Forms\Components\TextInput::make('login_attempts')
                            ->numeric()
                            ->disabled()
                            ->dehydrated(false),
                        \Filament\Forms\Components\TextInput::make('otp_attempts')
                            ->numeric()
                            ->disabled()
                            ->dehydrated(false),
                        \Filament\Forms\Components\DateTimePicker::make('last_activity_at')
                            ->displayFormat('d/m/Y H:i:s')
                            ->disabled()
                            ->dehydrated(false),
                    ]),
                \Filament\Forms\Components\Toggle::make('locked')
                    ->onColor('danger')
                    ->offColor('success'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ...self::getTableFields(self::$model),
                Tables\Columns\TextColumn::make('roles.name')
                    ->badge()
                    ->separator(','),
                Tables\Columns\TextColumn::make('locked')
                    ->formatStateUsing(fn (bool $state): string => $state ? 'Yes' : 'No')
                    ->sortable()
                    ->badge()
                    ->color(fn (bool $state): string => $state ? 'danger' : 'success'),
                Tables\Columns\TextColumn::make('accepted')
                    ->formatStateUsing(fn (bool $state): string => $state ? 'Yes' : 'No')
                    ->sortable()
                    ->badge()
                    ->color(fn (bool $state): string => $state ? 'success' : 'danger'),
            ])
            ->filters([
                ...self::getTableFilters(self::$model),
                Tables\Filters\TrashedFilter::make()
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make()
                    ->after(fn () => Notification::make()->success()->title('User Restored')->send()),
                Tables\Actions\ForceDeleteAction::make()
                    ->after(fn () => Notification::make()->success()->title('User Permanently Deleted')->send()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            CustomAuditsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAdminUsers::route('/'),
            'create' => Pages\CreateAdminUser::route('/create'),
            'view' => Pages\ViewAdminUser::route('/{record}'),
            'edit' => Pages\EditAdminUser::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('type', RbacHelper::TYPE_ADMIN)
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

//    public static function canViewAny(): bool
//    {
//        return auth()->user()->can('view_admin::user');
//    }

    public static function canAccess(): bool
    {
        return auth()->user()->can('view_admin::user');
    }

    public static function canCreate(): bool
    {
        return auth()->user()->can('create_admin::user');
    }

    public static function canView(Model $record): bool
    {
        return auth()->user()->can('view_admin::user');
    }

    public static function canEdit(Model $record): bool
    {
        return auth()->user()->can('update_admin::user');
    }

    public static function canDelete(Model $record): bool
    {
        return auth()->user()->can('delete_admin::user');
    }

    public static function canForceDelete(Model $record): bool
    {
        return auth()->user()->can('delete_admin::user');
    }

    public static function canRestore(Model $record): bool
    {
        return auth()->user()->can('restore_admin::user');
    }

}
