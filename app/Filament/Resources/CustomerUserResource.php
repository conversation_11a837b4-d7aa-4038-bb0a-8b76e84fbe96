<?php

namespace App\Filament\Resources;

use App\Filament\RelationManagers\CustomAuditsRelationManager;
use App\Filament\Resources\CustomerUserResource\Pages;
use App\Helpers\InvitationHelper;
use App\Helpers\RbacHelper;
use App\Helpers\StatusHelper;
use App\Models\Company;
use App\Models\Status;
use App\Models\User;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Traits\DynamicFields;

class CustomerUserResource extends Resource
{
    use DynamicFields;

    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-user';

    protected static ?string $navigationGroup = 'Content Management';

    protected static ?string $navigationLabel = 'Customer Users';

    protected static ?int $navigationSort = 2;

    /**
     * Required form fields
     * Set to ['*'] to make all columns required
    */
    protected static array $requiredFields = [
        'name',
        'email',
        'phone_number',
        'password',
        'status_id',
        'roles',
        'company_id',
    ];

    /**
     * Excluded form fields
     * By default these are excluded: ['id', 'created_at', 'updated_at', 'created_by', 'updated_by', 'deleted_at', 'deleted_by', 'two_factor_secret', 'two_factor_recovery_codes', 'two_factor_confirmed_at', 'otp', 'otp_expires_at']
    */
    protected static array $excludedFields = [
        'otp',
        'public_user_id',
        'user_id',
        'remember_token',
        'email_verified_at',
        'accepted',
        'last_activity_at',
        'description',
        'login_attempts',
        'otp_attempts',
        'locked',
        'is_primary_customer',
        'type', // Hide type field as it's fixed for customer users
    ];

    /**
     * Wether the field should be a select in both Form & Table
     * Don't specify options in order to set the defaults: (Active, Inactive, Deleted)
    */
    protected static array $dropdownFields = [
        'timezone' => [
            'timezones' => true,
            'searchable' => true,
        ],
        'company_id' => [
            'relationship' => [
                'model' => Company::class,
                'titleAttribute' => 'name',
                'valueAttribute' => 'company_id',
            ],
            'searchable' => true,
            'placeholder' => 'Select a company',
            'required' => true,
        ],
        'status_id' => [
            'relationship' => [
                'model' => Status::class,
                'titleAttribute' => 'name',
                'valueAttribute' => 'status_id',
                'statusType' => StatusHelper::STATUS_TYPE_USER,
            ],
            'searchable' => true,
        ],
        'accepted' => [
            'options' => [
                '1' => 'Yes',
                '0' => 'No',
            ],
        ],
        'locked' => [
            'options' => [
                '1' => 'Yes',
                '0' => 'No',
            ],
        ],
    ];

    /**
     * Table filter fields
     * Set to ['*'] to make all columns filterable
    */
    protected static array $filterableFields = [
        'user_id',
        'public_user_id',
        'name',
        'email',
        'phone_number',
        'accepted',
        'locked',
        'company_id',
        'status_id',
        'last_activity_at',
    ];

    /**
     * Excluded Table columns
     * By default these are excluded: ['password', 'remember_token']
    */
    protected static array $excludedTableFields = [
        'email_verified_at',
//        'last_activity_at',
//        'created_at',
//        'updated_at',
//        'deleted_at',
//        'created_by',
//        'updated_by',
//        'deleted_by'
    ];

    /**
     * Sortable Table columns
     * Set to ['*'] to make all columns sortable
    */
    protected static array $sortableTableFields = ['*'];

    /**
     * colors fields
     */
    protected static array $colorFields = ['color'];

    /**
     * Set it to false in order to disable table search
    */
    protected static bool $tableSearch = true;

    /**
     * map keys => labels
    */
    protected static array $attributesLabels = [
         'company_id' => 'Company',
         'status_id' => 'User Status',
    ];

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                \Filament\Forms\Components\Hidden::make('created_by'),
                ...self::getFormFields(self::$model),
                \Filament\Forms\Components\Hidden::make('type')
                    ->default(RbacHelper::TYPE_CUSTOMER),
                \Filament\Forms\Components\Toggle::make('is_primary_customer')
                    ->label('Primary Customer')
                    ->helperText('This user will be the main point of contact for the company')
                    ->columnSpan('full')
                    ->afterStateUpdated(function ($state, $set, $record) {
                        if ($state && $record) {
                            // Unset other primary contacts for the same company
                            User::where('company_id', $record->company_id)
                                ->where('user_id', '!=', $record->user_id)
                                ->update(['is_primary_customer' => false]);
                        }
                    }),
                \Filament\Forms\Components\Select::make('roles')
                    ->relationship('roles', 'name', fn (Builder $query) => $query->where('type', RbacHelper::TYPE_CUSTOMER))
                    ->preload()
                    ->searchable()
                    ->required()
                    ->rules(['required']),
                \Filament\Forms\Components\Grid::make(2)
                    ->schema([
                        \Filament\Forms\Components\TextInput::make('login_attempts')
                            ->numeric()
                            ->disabled()
                            ->dehydrated(false),
                        \Filament\Forms\Components\TextInput::make('otp_attempts')
                            ->numeric()
                            ->disabled()
                            ->dehydrated(false),
                        \Filament\Forms\Components\DateTimePicker::make('last_activity_at')
                            ->displayFormat('d/m/Y H:i:s')
                            ->disabled()
                            ->dehydrated(false),
                    ]),
                \Filament\Forms\Components\Toggle::make('locked')
                    ->onColor('danger')
                    ->offColor('success'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ...self::getTableFields(self::$model),
                Tables\Columns\TextColumn::make('roles.name')
                    ->badge()
                    ->label('Role'),
                Tables\Columns\IconColumn::make('is_primary_customer')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('gray')
                    ->label('Primary Customer')
                    ->sortable(),
                Tables\Columns\TextColumn::make('locked')
                    ->formatStateUsing(fn (bool $state): string => $state ? 'Yes' : 'No')
                    ->sortable()
                    ->badge()
                    ->color(fn (bool $state): string => $state ? 'danger' : 'success'),
                Tables\Columns\TextColumn::make('accepted')
                    ->formatStateUsing(fn (bool $state): string => $state ? 'Yes' : 'No')
                    ->sortable()
                    ->badge()
                    ->color(fn (bool $state): string => $state ? 'success' : 'danger'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('roles')
                    ->relationship('roles', 'name', fn (Builder $query) => $query->where('type', RbacHelper::TYPE_CUSTOMER))
                    ->preload()
                    ->searchable(),
                ...self::getTableFilters(self::$model),
                Tables\Filters\TrashedFilter::make()
            ])
            ->actions([
                Tables\Actions\Action::make('resendInvitation')
                    ->label('Resend Invitation')
                    ->icon('heroicon-o-envelope')
                    ->color('warning')
                    ->requiresConfirmation()
                    ->modalHeading('Resend Invitation')
                    ->modalDescription('Are you sure you want to resend the invitation email to this user?')
                    ->modalSubmitActionLabel('Yes, Resend')
                    ->visible(fn (User $record): bool => !$record->accepted &&
                        $record->status_id != StatusHelper::getStatusByAttributeName('name', 'active', StatusHelper::STATUS_TYPE_USER) &&
                        auth()->user()->can('update_customer::user'))
                    ->action(function (User $record) {
                        InvitationHelper::sendUserInvitation($record->email, $record->name);

                        Notification::make()
                            ->title('Invitation email has been sent to ' . $record->email)
                            ->success()
                            ->send();
                    }),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make()
                    ->after(fn () => Notification::make()->success()->title('User Restored')->send()),
                Tables\Actions\ForceDeleteAction::make()
                    ->after(fn () => Notification::make()->success()->title('User Permanently Deleted')->send()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            CustomAuditsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCustomerUsers::route('/'),
            'create' => Pages\CreateCustomerUser::route('/create'),
            'view' => Pages\ViewCustomerUser::route('/{record}'),
            'edit' => Pages\EditCustomerUser::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('type', RbacHelper::TYPE_CUSTOMER)
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function canAccess(): bool
    {
        return auth()->user()->can('view_customer::user');
    }

    public static function canView(Model $record): bool
    {
        return auth()->user()->can('view_customer::user');
    }

    public static function canCreate(): bool
    {
        return auth()->user()->can('create_customer::user');
    }

    public static function canEdit(Model $record): bool
    {
        return auth()->user()->can('update_customer::user');
    }

    public static function canDelete(Model $record): bool
    {
        return auth()->user()->can('delete_customer::user');
    }

    public static function canForceDelete(Model $record): bool
    {
        return auth()->user()->can('delete_customer::user');
    }

    public static function canRestore(Model $record): bool
    {
        return auth()->user()->can('restore_customer::user');
    }

}
