<?php

namespace App\Filament\Resources;

use SolutionForest\FilamentCms\Filament\Resources\CmsPageNavigationCategoryResource as BaseCmsPageNavigationCategoryResource;
use Illuminate\Database\Eloquent\Model;

class CmsPageNavigationCategoryResource extends BaseCmsPageNavigationCategoryResource
{
    protected static ?string $navigationIcon = 'heroicon-o-bars-3-center-left';

    public static function canAccess(): bool
    {
        return auth()->user()->can('view_cms::page::navigation::category');
    }

    public static function canCreate(): bool
    {
        return auth()->user()->can('create_cms::page::navigation::category');
    }

    public static function canEdit(Model $record): bool
    {
        return auth()->user()->can('update_cms::page::navigation::category');
    }

    public static function canDelete(Model $record): bool
    {
        return auth()->user()->can('delete_cms::page::navigation::category');
    }
}