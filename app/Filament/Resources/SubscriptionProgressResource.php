<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SubscriptionProgressResource\Pages;
use App\Models\SubscriptionProgress;
use App\Models\Subscription;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Traits\DynamicFields;
use App\Traits\HasShieldAccess;

class SubscriptionProgressResource extends Resource
{
    use DynamicFields, HasShieldAccess;

    protected static ?string $model = SubscriptionProgress::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';
    protected static ?string $navigationLabel = 'Subscription Progress';
    protected static ?string $navigationGroup = 'Payments';
    protected static ?int $navigationSort = 4;

    /**
     * Required form fields
     * Set to ['*'] to make all columns required
     */
    protected static array $requiredFields = [];

    /**
     * Excluded form fields
     * By default these are excluded: ['id', 'created_at', 'updated_at', 'created_by', 'updated_by', 'deleted_at', 'deleted_by']
     */
    protected static array $excludedFields = ['subscription_progress_id'];

    /**
     * Dropdown fields configuration
     */
    protected static array $dropdownFields = [
        'subscription_id' => [
            'relationship' => [
                'model' => Subscription::class,
                'titleAttribute' => 'name',
                'valueAttribute' => 'id',
            ],
            'searchable' => true,
        ],
        'step_name' => [
            'options' => [
                'selected_plan' => 'Selected Plan',
                'provided_address' => 'Provided Address',
                'entered_payment_details' => 'Entered Payment Details',
                'confirmed_order' => 'Confirmed Order',
            ],
        ],
    ];

    /**
     * Table filter fields
     * Set to ['*'] to make all columns filterable
     */
    protected static array $filterableFields = ['subscription_id', 'step_name', 'completed_at'];

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('subscription_id')
                    ->relationship('subscription', 'name')
                    ->required()
                    ->disabled(),
                Forms\Components\Select::make('step_name')
                    ->options([
                        'selected_plan' => 'Selected Plan',
                        'provided_address' => 'Provided Address',
                        'entered_payment_details' => 'Entered Payment Details',
                        'confirmed_order' => 'Confirmed Order',
                    ])
                    ->required()
                    ->disabled(),
//                Forms\Components\KeyValue::make('step_data')
//                    ->keyLabel('Property')
//                    ->valueLabel('Value')
//                    ->disabled()
//                    ->dehydrated(false),

                Forms\Components\Section::make('Step Data')
                    ->schema([
                        Forms\Components\ViewField::make('step_data')
                            ->view('filament.forms.components.json-data-viewer')
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),
                Forms\Components\DateTimePicker::make('completed_at')
                    ->disabled(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('completed_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('subscription_progress_id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('subscription.public_subscription_id')
                    ->label('Parent Subscription Public ID')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('subscription.name')
                    ->label('Subscription')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('step_name')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'selected_plan' => 'gray',
                        'provided_address' => 'info',
                        'entered_payment_details' => 'warning',
                        'confirmed_order' => 'success',
                        default => 'gray',
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('completed_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\Filter::make('subscription_progress_id')
                    ->form([
                        Forms\Components\TextInput::make('value')
                            ->label('ID'),
                    ])
                    ->query(function ($query, array $data) {
                        return $query->when(
                            $data['value'],
                            fn ($query) => $query->where('subscription_progress_id', 'like', "%{$data['value']}%")
                        );
                    })
                    ->indicateUsing(function (array $data) {
                        return filled($data['value'] ?? null)
                            ? ['label' => 'subscription progress id: ' . $data['value']]
                            : null;
                    }),
                Tables\Filters\Filter::make('public_subscription_id')
                    ->form([
                        Forms\Components\TextInput::make('value')
                            ->label('Parent Subscription Public ID'),
                    ])
                    ->query(function ($query, array $data) {
                        return $query->when(
                            $data['value'],
                            fn ($query) => $query->whereHas('subscription', function ($subQuery) use ($data) {
                                $subQuery->where('public_subscription_id', 'like', "%{$data['value']}%");
                            })
                        );
                    })
                    ->indicateUsing(function (array $data) {
                        return filled($data['value'] ?? null)
                            ? ['label' => 'Public subscription id: ' . $data['value']]
                            : null;
                    }),
                Tables\Filters\SelectFilter::make('step_name')
                    ->options([
                        'selected_plan' => 'Selected Plan',
                        'provided_address' => 'Provided Address',
                        'entered_payment_details' => 'Entered Payment Details',
                        'confirmed_order' => 'Confirmed Order',
                    ]),
                Tables\Filters\Filter::make('completed_at')
                    ->form([
                        Forms\Components\DatePicker::make('completed_from'),
                        Forms\Components\DatePicker::make('completed_until'),
                    ])
                    ->query(function ($query, array $data) {
                        return $query
                            ->when(
                                $data['completed_from'],
                                fn ($query) => $query->whereDate('completed_at', '>=', $data['completed_from']),
                            )
                            ->when(
                                $data['completed_until'],
                                fn ($query) => $query->whereDate('completed_at', '<=', $data['completed_until']),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSubscriptionProgress::route('/'),
            'view' => Pages\ViewSubscriptionProgress::route('/{record}'),
        ];
    }
}
