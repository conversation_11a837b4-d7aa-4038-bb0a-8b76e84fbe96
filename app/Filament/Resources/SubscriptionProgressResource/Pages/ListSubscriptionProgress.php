<?php

namespace App\Filament\Resources\SubscriptionProgressResource\Pages;

use App\Filament\Resources\SubscriptionProgressResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSubscriptionProgress extends ListRecords
{
    protected static string $resource = SubscriptionProgressResource::class;

    protected function getHeaderActions(): array
    {
        return [];
    }
}