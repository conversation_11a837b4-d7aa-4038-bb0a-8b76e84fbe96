<?php

namespace App\Filament\Resources;

use App\Enums\DefaultValues;
use App\Filament\Resources\SubscriptionHistoryResource\Pages;
use App\Helpers\AppHelper;
use App\Models\Country;
use App\Models\Currency;
use App\Models\SubscriptionHistory;
use App\Models\SuspensionReason;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Traits\DynamicFields;
use App\Traits\HasShieldAccess;
use Illuminate\Database\Eloquent\Builder;

class SubscriptionHistoryResource extends Resource
{
    use DynamicFields, HasShieldAccess;

    protected static ?string $model = SubscriptionHistory::class;

    protected static ?string $navigationIcon = 'heroicon-o-clock';
    protected static ?string $navigationLabel = 'Subscription History';
    protected static ?string $navigationGroup = 'Payments';
    protected static ?int $navigationSort = 3;


    /**
     * Required form fields
     * Set to ['*'] to make all columns required
     */
    protected static array $requiredFields = [];

    /**
     * Excluded form fields
     * By default these are excluded: ['id', 'created_at', 'updated_at', 'created_by', 'updated_by', 'deleted_at', 'deleted_by', 'two_factor_secret', 'two_factor_recovery_codes', 'two_factor_confirmed_at', 'otp', 'otp_expires_at']
     */
    protected static array $excludedFields = [
        'subscription_id',
        'public_subscription_id',
        'name',
    ];

    /**
     * Wether the field should be a select in both Form & Table
     * Don't specify options in order to set the defaults: (Active, Inactive, Deleted)
     */
    protected static array $dropdownFields = [
        'country_id' => [
            'relationship' => [
                'model' => Country::class,
                'titleAttribute' => 'name',
                'valueAttribute' => 'id',
            ],
            'searchable' => true,
            'default' => 1,
        ],
        'billing_country_id' => [
            'relationship' => [
                'model' => Country::class,
                'titleAttribute' => 'name',
                'valueAttribute' => 'id',
            ],
            'searchable' => true,
            'default' => 1,
        ],
        'currency_id' => [
            'relationship' => [
                'model' => Currency::class,
                'titleAttribute' => 'name',
                'valueAttribute' => 'id',
            ],
            'searchable' => true,
            'default' => 1,
            'required' => true,
        ],
        'plan_id' => [
            'relationship' => [
                'model' => \App\Models\Plan::class,
                'titleAttribute' => 'name',
                'valueAttribute' => 'id',
            ],
            'searchable' => true,
            'required' => true,
        ],
        'suspension_reason_id' => [
            'relationship' => [
                'model' => SuspensionReason::class,
                'titleAttribute' => 'name',
                'valueAttribute' => 'suspension_reasons_id',
            ],
            'searchable' => true,
        ],
        'subscriber_id' => [
            'relationship' => [
                'model' => \App\Models\Company::class,
                'titleAttribute' => 'name',
                'valueAttribute' => 'company_id',
            ],
            'searchable' => true,
            'required' => true,
        ],
    ];

    /**
     * Table filter fields
     * Set to ['*'] to make all columns filterable
     */
    protected static array $filterableFields = [
        'public_subscription_id',
//        'plan_id',
//        'country_id',
//        'currency_id',
        'suspension_reason_id',
        'subscriber_id',
//        'history_event',
    ];

    /**
     * Excluded Table columns
     * By default these are excluded: ['password', 'remember_token']
     */
    protected static array $excludedTableFields = [
        'subscription_history_id',
        'subscription_id',
        'plan_id',
        'country_id',
        'currency_id',
        'suspension_reason_id',
//        'description',
        'timezone',
        'trial_ends_at',
        'starts_at',
        'ends_at',
        'cancels_at',
        'canceled_at',
        'auto_renew',
        'name',
        'slug',
        'subscriber_type',
        'billing_country_id',
    ];

    /**
     * Sortable Table columns
     * Set to ['*'] to make all columns sortable
     */
    protected static array $sortableTableFields = ['*'];

    /**
     * colors fields
     */
    protected static array $colorFields = [];

    /**
     * Set it to false in order to disable table search
     */
    protected static bool $tableSearch = true;

    /**
     * map keys => labels
     */
    protected static array $attributesLabels = [
        'currency_id' => 'Currency',
        'country_id' => 'Country',
        'billing_country_id' => 'Billing Country',
        'plan_id' => 'Plan',
        'suspension_reason_id' => 'Suspension Reason',
        'subscriber_id' => 'Subscriber',
    ];

    /**
     * Fields that should be treated as translatable
     */
    protected static array $translatableFields = [
        'description',
    ];

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Read-only form for viewing
                Forms\Components\TextInput::make('public_subscription_history_id')
                    ->label('Public ID')
                    ->formatStateUsing(fn ($record) => AppHelper::getFormattedId((string) $record->subscription_history_id, DefaultValues::SUBSCRIPTION_HISTORY_PREFIX->get()))
                    ->disabled(),
                Forms\Components\TextInput::make('public_subscription_id')
                    ->label('Public Subscription ID')
                    ->disabled(),

                ...self::getFormFields(self::$model),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('subscription_history_id')
                    ->label('Public ID')
                    ->formatStateUsing(fn ($state) => AppHelper::getFormattedId((string) $state, DefaultValues::SUBSCRIPTION_HISTORY_PREFIX->get()))
                    ->sortable(),
                Tables\Columns\TextColumn::make('billingCountry.name')
                    ->label('Billing Country')
                    ->searchable()
                    ->sortable(),

                ...self::getTableFields(self::$model),
            ])
//            ->searchable(false)
            ->filters([
                Tables\Filters\Filter::make('subscription_history_id')
                    ->form([
                        Forms\Components\TextInput::make('value')
                            ->label('Subscription History ID'),
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query
                            ->when($data['value'],
                                fn ($q, $value) => $q->where('subscription_history_id', 'like', "%{$value}%")
                            );
                    }),

                ...self::getTableFilters(self::$model),
                Tables\Filters\Filter::make('public_subscription_id')
                    ->query(function ($query, $data) {
                        if (isset($data['value']) && $data['value'] !== '') {
                            $query->where('public_subscription_id', 'like', '%' . $data['value'] . '%');
                        }
                    })
                    ->indicateUsing(function (array $data) {
                        return filled($data['value'] ?? null)
                            ? ['label' => 'Public subscription id: ' . $data['value']]
                            : null;
                    })
                    ->form([
                        Forms\Components\TextInput::make('value')
                            ->label('Public subscription id')
                            ->placeholder("Enter public subscription id value"),
                    ]),
                Tables\Filters\Filter::make('date_range')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('From'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Until'),
                    ])
                    ->query(function ($query, array $data) {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn($query) => $query->whereDate('created_at', '>=', $data['created_from']),
                            )
                            ->when(
                                $data['created_until'],
                                fn($query) => $query->whereDate('created_at', '<=', $data['created_until']),
                            );
                    })
                    ->indicateUsing(function (array $data) {
                        $indicators = [];

                        if (!empty($data['created_from'])) {
                            $indicators[] = 'From: ' . \Carbon\Carbon::parse($data['created_from'])->format('Y-m-d');
                        }

                        if (!empty($data['created_until'])) {
                            $indicators[] = 'Until: ' . \Carbon\Carbon::parse($data['created_until'])->format('Y-m-d');
                        }

                        return $indicators ? ['label' => implode(' | ', $indicators)] : null;
                    })
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSubscriptionHistory::route('/'),
            'view' => Pages\ViewSubscriptionHistory::route('/{record}'),
        ];
    }
}
