<?php

namespace App\Filament\Resources;

use App\Enums\DefaultValues;
use TomatoPHP\FilamentLocations\Models\Currency;
use App\Filament\Resources\PlanResource\Pages;
use App\Filament\Resources\PlanResource\RelationManagers\FeatureManager;
use App\Helpers\CurrencyHelper;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Laravelcm\Subscriptions\Interval;
use App\Models\Plan;
use App\Traits\HasShieldAccess;
use TomatoPHP\FilamentTranslationComponent\Components\Translation;

class PlanResource extends Resource
{
    use HasShieldAccess;

    protected static ?string $model = Plan::class;

    protected static ?string $navigationIcon = 'heroicon-o-bookmark';

    protected static ?int $navigationSort = 1;

    public static function getNavigationGroup(): ?string
    {
        return trans('filament-subscriptions::messages.group');
    }

    public static function getNavigationLabel(): string
    {
        return trans('filament-subscriptions::messages.plans.title');
    }

    public static function getPluralLabel(): ?string
    {
        return trans('filament-subscriptions::messages.plans.title');
    }

    public static function getLabel(): ?string
    {
        return trans('filament-subscriptions::messages.plans.title');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Translation::make('name')
                            ->columnSpanFull()
                            ->label(trans('filament-subscriptions::messages.plans.columns.name'))
                            ->required(),
                        Translation::make('description')
                            ->columnSpanFull()
                            ->label(trans('filament-subscriptions::messages.plans.columns.description')),
                        Forms\Components\TextInput::make('badge')
                            ->label('badge'),
                        Forms\Components\Select::make('currency')
                            ->default(DefaultValues::CURRENCY->get())
                            ->searchable()
                            ->label(trans('filament-subscriptions::messages.plans.columns.currency'))
                            ->options(Currency::query()->pluck('name', 'iso')->toArray())
                            ->required()
                            ->live(),
                        Forms\Components\TextInput::make('price')
                            ->default(0)
                            ->label(trans('filament-subscriptions::messages.plans.columns.price'))
                            ->required()
                            ->numeric()
                            ->prefix(fn(callable $get) => CurrencyHelper::getCurrencySymbol($get('currency'))),
                        Forms\Components\TextInput::make('signup_fee')
                            ->label(trans('filament-subscriptions::messages.plans.columns.signup_fee'))
                            ->default(0)
                            ->numeric()
                            ->prefix(fn(callable $get) => CurrencyHelper::getCurrencySymbol($get('currency'))),
                        Forms\Components\Select::make('invoice_interval')
                            ->default(Interval::MONTH->value)
                            ->label(trans('filament-subscriptions::messages.plans.columns.invoice_interval'))
                            ->options([
                                Interval::DAY->value => trans('filament-subscriptions::messages.plans.columns.day'),
                                Interval::MONTH->value => trans('filament-subscriptions::messages.plans.columns.month'),
                                Interval::YEAR->value => trans('filament-subscriptions::messages.plans.columns.year'),
                            ])->required(),
                        Forms\Components\TextInput::make('invoice_period')
                            ->label(trans('filament-subscriptions::messages.plans.columns.invoice_period'))
                            ->default(0)
                            ->numeric()
                            ->required(),
                        Forms\Components\Select::make('trial_interval')
                            ->default(Interval::MONTH->value)
                            ->label(trans('filament-subscriptions::messages.plans.columns.trial_interval'))
                            ->default(0)
                            ->options([
                                Interval::DAY->value => trans('filament-subscriptions::messages.plans.columns.day'),
                                Interval::MONTH->value => trans('filament-subscriptions::messages.plans.columns.month'),
                                Interval::YEAR->value => trans('filament-subscriptions::messages.plans.columns.year'),
                            ]),
                        Forms\Components\TextInput::make('trial_period')
                            ->label(trans('filament-subscriptions::messages.plans.columns.trial_period'))
                            ->default(0)
                            ->numeric(),
                        Forms\Components\Toggle::make('is_active')
                            ->label(trans('filament-subscriptions::messages.plans.columns.is_active')),
                    ])->columns(2)
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->reorderable('sort_order')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(trans('filament-subscriptions::messages.plans.columns.name'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('badge')
                    ->label('badge')
                    ->searchable()
                    ->sortable(),
                // Tables\Columns\TextColumn::make('invoice_interval')
                //     ->label(trans('filament-subscriptions::messages.plans.columns.invoice_interval'))
                //     ->sortable()
                //     ->searchable(),
                // Tables\Columns\TextColumn::make('invoice_period')
                //     ->label(trans('filament-subscriptions::messages.plans.columns.invoice_period'))
                //     ->sortable()
                //     ->searchable(),
                Tables\Columns\TextColumn::make('price')
                    ->label(trans('filament-subscriptions::messages.plans.columns.price'))
                    ->sortable()
                    ->searchable()
                    ->formatStateUsing(function ($record) {
                        $price = number_format($record->price, 0, '.', ',');
                        $currency = $record->currency;
                        $interval = $record->invoice_interval;
                        $period = $record->invoice_period > 1 ? $record->invoice_period . ' ' . $interval . 's' : $interval;

                        return "{$price}{$currency}/{$period}";
                    }),
                Tables\Columns\ToggleColumn::make('is_active')
                    ->label(trans('filament-subscriptions::messages.plans.columns.is_active')),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make()->native(false),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            FeatureManager::class,
            // PlanFeaturesManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPlans::route('/'),
            'create' => Pages\CreatePlan::route('/create'),
            'edit' => Pages\EditPlan::route('/{record}/edit'),
        ];
    }
}
