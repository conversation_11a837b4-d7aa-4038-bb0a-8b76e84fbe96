<?php

namespace App\Filament\Resources;

use SolutionForest\FilamentCms\Filament\Resources\CmsTagResource as BaseCmsTagResource;
use Illuminate\Database\Eloquent\Model;

class CmsTagResource extends BaseCmsTagResource
{
    protected static ?string $navigationIcon = 'heroicon-o-tag';

    public static function canAccess(): bool
    {
        return auth()->user()->can('view_cms::tag');
    }

    public static function canCreate(): bool
    {
        return auth()->user()->can('create_cms::tag');
    }

    public static function canEdit(Model $record): bool
    {
        return auth()->user()->can('update_cms::tag');
    }

    public static function canDelete(Model $record): bool
    {
        return auth()->user()->can('delete_cms::tag');
    }
}