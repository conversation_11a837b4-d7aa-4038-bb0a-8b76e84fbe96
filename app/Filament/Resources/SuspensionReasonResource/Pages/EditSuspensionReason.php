<?php

namespace App\Filament\Resources\SuspensionReasonResource\Pages;

use App\Filament\Resources\SuspensionReasonResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditSuspensionReason extends EditRecord
{
    protected static string $resource = SuspensionReasonResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        if (empty($data['password'])) {
            unset($data['password']); 
        }

        return $data;
    }
}
