<?php

namespace App\Filament\Resources\SubscriptionResource\Pages;

use App\Filament\Resources\SubscriptionResource;
use App\Models\Subscription;
use App\Services\SubscriptionService;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditSubscription extends EditRecord
{
    protected static string $resource = SubscriptionResource::class;
    
    protected function getSubscriptionService(): SubscriptionService
    {
        return app(SubscriptionService::class);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        return $this->getSubscriptionService()->getFormData($this->record, $data);
    }

    protected function handleRecordUpdate(Subscription|Model $record, array $data): Model
    {
        // Update the subscription record
        $record->update($this->getSubscriptionService()->prepareSubscriptionData($data));

        // Update subscription features
        $this->getSubscriptionService()->updateFeatures($record, $data);

        // Update subscription license
        $this->getSubscriptionService()->updateLicenses($record, $data['subscription_licenses']);

        return $record;
    }
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->record]);
    }
}
