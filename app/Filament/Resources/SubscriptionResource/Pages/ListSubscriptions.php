<?php

namespace App\Filament\Resources\SubscriptionResource\Pages;

use Filament\Resources\Pages\ManageRecords;
use App\Filament\Resources\SubscriptionResource;
use Filament\Actions;

class ListSubscriptions extends ManageRecords
{
    protected static string $resource = SubscriptionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
