<?php

namespace App\Filament\Resources\SubscriptionResource\Pages;

use App\Enums\Subscriptions;
use App\Filament\Resources\SubscriptionResource;
use App\Observers\SubscriptionObserver;
use App\Services\SubscriptionService;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateSubscription extends CreateRecord
{
    protected static string $resource = SubscriptionResource::class;

    protected function getSubscriptionService(): SubscriptionService
    {
        return app(SubscriptionService::class);
    }

    /**
     * @throws \Exception
     */
    protected function handleRecordCreation(array $data): Model
    {
        $subscriberType = $data['subscriber_type'];
        $subscriberModel = $subscriberType::find($data['subscriber_id']);

        if (!$subscriberModel) {
            Notification::make()
                ->warning()
                ->title('Subscriber not found.')
                ->send();

            throw new \Exception('Subscriber not found');
        }

        // Create the subscription
        SubscriptionObserver::$eventType = Subscriptions::HISTORY_EVENT_TYPE_NEW->value;
        $subscription = $this->getModel()::create(
            $this->getSubscriptionService()->prepareSubscriptionData($data)
        );

        // Create subscription features
        $this->getSubscriptionService()->updateFeatures($subscription, $data);

        // Create subscription license
        if (isset($data['subscription_licenses'])) {
            $this->getSubscriptionService()->updateLicenses($subscription, $data['subscription_licenses']);
        }

        // Create subscription payment record
        $this->getSubscriptionService()->createPayment($subscription);

        return $subscription;
    }
}