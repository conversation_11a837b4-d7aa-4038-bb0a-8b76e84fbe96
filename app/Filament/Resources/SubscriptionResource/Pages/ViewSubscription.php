<?php

namespace App\Filament\Resources\SubscriptionResource\Pages;

use App\Enums\SubscriptionLicenses;
use App\Filament\Resources\SubscriptionResource;
use App\Services\SubscriptionService;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\View\View;

class ViewSubscription extends ViewRecord
{
    protected static string $resource = SubscriptionResource::class;

    protected function getSubscriptionService(): SubscriptionService
    {
        return app(SubscriptionService::class);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('viewLicenseHistory')
                ->label('License History')
                ->icon('heroicon-o-key')
                ->color('info')
                ->tooltip('View license history')
                ->visible(fn() => auth()->user()->can('view_subscription::license'))
                ->modalHeading('License History')
                ->modalWidth(\Filament\Support\Enums\MaxWidth::SevenExtraLarge)
                ->modalContent(function (): View {
                    return view('filament.resources.subscription-resource.license-history', [
                        'subscription' => $this->record,
                    ]);
                })
                ->modalSubmitAction(false)
                ->modalCancelAction(false),
            Actions\Action::make('updateLicense')
                ->label('Update License')
                ->icon('heroicon-o-key')
                ->color('primary')
                ->tooltip('Update license key information')
                ->visible(function ($record) {
                    if (!$record) {
                        return false;
                    }

                    $isActive = optional($record->getActiveEnvironment())->environment_status === SubscriptionLicenses::ENVIRONMENT_STATUS_ACTIVE->value;

                    // Hide if SaaS and environment is active
                    if ($record->isSaas() && !$isActive) {
                        return false;
                    }

                    return auth()->user()->can('update_subscription::license');
                })
                ->form(function () {
                    return SubscriptionResource::getLicenseUpdateForm($this->record);
                })
                ->action(function (array $data) {
                    SubscriptionResource::handleLicenseUpdate($this->record, $data);
                }),
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        return $this->getSubscriptionService()->getFormData($this->record, $data);
    }
}