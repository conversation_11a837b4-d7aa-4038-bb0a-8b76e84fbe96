<?php

namespace App\Filament\RelationManagers;

use Illuminate\Support\Facades\Log;
use Tapp\FilamentAuditing\RelationManagers\AuditsRelationManager;
use Filament\Tables;
use Filament\Facades\Filament;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Columns\Column;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Arr;
use Illuminate\View\View;
use OwenIt\Auditing\Contracts\Audit;
use Illuminate\Support\Str;

class CustomAuditsRelationManager extends AuditsRelationManager
{
    public function table(Tables\Table $table): Tables\Table
    {
        $oldValuesColumn =
                Tables\Columns\TextColumn::make('old_values')
                ->formatStateUsing(fn (Column $column, $record, $state): View =>
                view('filament-auditing::tables.columns.key-value', [
                    'state' => $this->mapRelatedColumnsWithPrefixes($column->getState(), $record),
                ])
                )
                ->wrap()
                ->label(trans('filament-auditing::filament-auditing.column.old_values'));

        $newValuesColumn =
                Tables\Columns\TextColumn::make('new_values')
                ->formatStateUsing(fn (Column $column, $record, $state): View =>
                view('filament-auditing::tables.columns.key-value', [
                    'state' => $this->mapRelatedColumnsWithPrefixes($column->getState(), $record),
                ])
                )
                ->wrap()
                ->label(trans('filament-auditing::filament-auditing.column.new_values'));

        return $table
            ->modifyQueryUsing(fn (Builder $query) =>
            $query->with('user')->orderBy(
                config('filament-auditing.audits_sort.column'),
                config('filament-auditing.audits_sort.direction')
            )
            )
            ->content(fn (): ?View =>
            config('filament-auditing.custom_audits_view')
                ? view('filament-auditing::tables.custom-audit-content', Arr::add(self::customViewParameters(), 'owner', $this->getOwnerRecord()))
                : null
            )
            ->columns(Arr::flatten([
                Tables\Columns\TextColumn::make('user.name')
                    ->label(trans('filament-auditing::filament-auditing.column.user_name'))
                    ->formatStateUsing(fn ($state) => 'User: ' . $state),
                Tables\Columns\TextColumn::make('event')
                    ->label(trans('filament-auditing::filament-auditing.column.event'))
                    ->formatStateUsing(fn ($state) => 'Event: ' . ucfirst($state)),
                Tables\Columns\TextColumn::make('created_at')
                    ->since()
                    ->label(trans('filament-auditing::filament-auditing.column.created_at')),
                $oldValuesColumn,
                $newValuesColumn,
                self::extraColumns(),
            ]))
            ->filters([
                // Add filters here if needed
            ])
            ->headerActions([
                // Add header actions here if needed
            ])
            ->actions([
                Tables\Actions\Action::make('restore')
                    ->label(trans('filament-auditing::filament-auditing.action.restore'))
                    ->action(fn (Audit $record) => static::restoreAuditSelected($record))
                    ->icon('heroicon-o-arrow-path')
                    ->requiresConfirmation()
                    ->visible(fn (Audit $record, RelationManager $livewire): bool =>
                        Filament::auth()->user()->can('restoreAudit', $livewire->ownerRecord)
                        && $record->event === 'updated'
                    )
                    ->after(fn ($livewire) => $livewire->dispatch('auditRestored')),
            ])
            ->bulkActions([
                // Add bulk actions here if needed
            ]);
    }

    protected function mapRelatedColumnsWithPrefixes(array $state, $record): array
    {
        $mapped = [];

        // Skip updated_by since it's already shown in a separate column
        if (isset($state['updated_by'])) {
            unset($state['updated_by']);
        }

        foreach ($state as $field => $value) {
            $displayValue = $value;

            // fields that the id will be replaced by name
            $relationFields = ['status_id'];
            if (str_ends_with($field, '_id')) {
                $fieldWithoutId = str_replace('_id', '', $field);

                if (in_array($field, $relationFields)) {
                    $relationName = $fieldWithoutId;
                    $modelClass = $this->getRelatedModelClass($relationName);

                    if ($modelClass && class_exists($modelClass)) {
                        $relatedRecord = $modelClass::find($value);
                        if ($relatedRecord && isset($relatedRecord->name)) {
                            $displayValue = $relatedRecord->name;
                        }
                    }
                }
            } elseif (str_ends_with($field, '_by')) {
                $relationName = 'user';
                $modelClass = $this->getRelatedModelClass($relationName);

                if ($modelClass && class_exists($modelClass)) {
                    $relatedRecord = $modelClass::find($value);
                    if ($relatedRecord && isset($relatedRecord->name)) {
                        $displayValue = $relatedRecord->name;
                    }
                }
            }

            // Format field name - remove _id suffix and convert to title case
            $field = str_ends_with($field, '_id') && in_array($field, $relationFields)
                ? ucfirst(str_replace('_', ' ', str_replace('_id', '', $field)))
                : ucfirst(str_replace('_', ' ', $field));
            $mapped[$field] = $displayValue;
        }

        return $mapped;
    }

    /**
     * Get the related model class based on the relation name
     */
    protected function getRelatedModelClass(string $relationName): ?string
    {
        // Common model mappings
        $modelMappings = [
            'user' => \App\Models\User::class,
            'status' => \App\Models\Status::class,
        ];

        if (isset($modelMappings[$relationName])) {
            return $modelMappings[$relationName];
        }

        // Try to guess the model class using convention
        $modelName = ucfirst(Str::singular($relationName));
        $modelClass = "\\App\\Models\\{$modelName}";

        return class_exists($modelClass) ? $modelClass : null;
    }

}
