<?php

namespace App\Traits;

use App\Enums\Subscriptions;
use Carbon\Carbon;
use Filament\Notifications\Notification;
use libphonenumber\PhoneNumberUtil;
use libphonenumber\NumberParseException;
use Illuminate\Support\Facades\Schema;
use Filament\Forms;
use Filament\Forms\Components\ColorPicker;
use Filament\Tables;
use Illuminate\Support\Facades\DB;
use Malzariey\FilamentDaterangepickerFilter\Enums\DropDirection;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;
use Ysfkaya\FilamentPhoneInput\PhoneInputNumberType;

/**
 * These properties are defined in the class using this trait:
 * - filterableFields: array
 * - tableSearch: bool
 */
trait DynamicFields
{
    use Locations, Timezones;

    private static $defaultFormExcludedFields = ['id', 'deleted_at', 'deleted_by', 'two_factor_secret', 'two_factor_recovery_codes', 'two_factor_confirmed_at', 'otp', 'otp_expires_at'];

    private static $defaultTableExcludedFields = ['remember_token', 'password', 'two_factor_secret', 'two_factor_recovery_codes', 'otp', 'otp_expires_at'];

    // Forms
    public static function getFormFields(string $modelClass): array
    {
        $tableName = (new $modelClass)->getTable();
        $columns = Schema::getColumnListing($tableName);
        $schema = [];

        foreach ($columns as $column) {
            if (
                in_array($column, self::$defaultFormExcludedFields) || in_array($column, self::$excludedFields)
                || $column == (new $modelClass)->getKeyName()
            ) {
                continue;
            }

            $columnType = Schema::getColumnType($tableName, $column);

            $input = self::getFormInput($column, $columnType, (in_array($column, self::$requiredFields) || in_array('*', self::$requiredFields)));

            if ($input) {
                $schema[] = $input;
            }
        }

        return $schema;
    }

    private static function getFormInput(string $column, string $columnType, bool $required = false)
    {
        $label = self::getColumnLabel($column);

        $DropdownField = self::$dropdownFields[$column] ?? [];
        extract($DropdownField);

        $options = $options ?? self::getDefaultStatuses();
        $isSearchable = $searchable ?? false;
        $isMultiple = $multiple ?? false;
        $placeholder = $placeholder ?? 'Select an option';
        $countries = $countries ?? false;
        $timezones = $timezones ?? false;
        $isColor = in_array($column, self::$colorFields);
        $relationship = $relationship ?? null;
        $default = $default ?? null;  // Extract the default value

        // Handle subscription status field
        if ($column === 'subscription_status') {
            return Forms\Components\Select::make($column)
                ->options(\App\Enums\Subscriptions::getSubscriptionStatusOptions())
                ->default(\App\Enums\Subscriptions::SUBSCRIPTION_STATUS_PENDING_PAYMENT->value)
                ->required($required)
                ->searchable($isSearchable);
        }

        // Handle payment status field
        if ($column === 'payment_status') {
            return Forms\Components\Select::make($column)
                ->options(\App\Enums\Subscriptions::getPaymentStatusOptions())
                ->default(\App\Enums\Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PENDING)
                ->required($required)
                ->disabled()
                ->dehydrated(true);
        }

        // Handle history event field
        if ($column === 'history_event') {
            return Forms\Components\TextInput::make($column)
                ->formatStateUsing(fn (string $state): string => Subscriptions::getLabel($state) ?? $state)
                ->disabled();
        }

        // Handle the public id of model
        if (str_starts_with($column, 'public_')) {
            // return a read-only public id
            return Forms\Components\TextInput::make($column)
                ->label('Public ID')
                ->disabled()
                ->dehydrated(false);
        }

        // Handle the _at and _by
        if (str_ends_with($column, '_at')) {
            // return a read-only date
            return Forms\Components\TextInput::make($column)
                ->disabled()
                ->dehydrated(false);
        }

        if (str_ends_with($column, '_by')) {
            // return a read-only username
            return Forms\Components\TextInput::make($column)
                ->label($label)
                ->disabled()
                ->dehydrated(false)
                ->formatStateUsing(function ($state) {
                    if (!$state) return '-';
                    $user = \App\Models\User::find($state);
                    return $user ? $user->name : $state;
                });
        }

        if ($column === 'user_id') {
            // Return a select dropdown for user_id
            return Forms\Components\Select::make($column)
                ->label($label)
                ->options(function () {
                    return \App\Models\User::all()->pluck('name', 'user_id')->toArray();
                })
                ->searchable()
                ->required($required);
        }

        // Special handling for email fields
        if ($column === 'email') {
            return Forms\Components\TextInput::make($column)
                ->label($label)
                ->email()
                ->required($required)
                ->unique(
                    table: (new (static::$model))->getTable(),
                    column: 'email',
                    ignorable: fn($record) => $record
                )
                ->rules(self::getEmailValidationRules());
        }

        // Special handling for phone_number fields with country dropdown and validation
        if ($column === 'phone_number') {
            return PhoneInput::make($column)
                ->label($label)
                ->required($required)
                ->displayNumberFormat(PhoneInputNumberType::INTERNATIONAL) // Show in international format with country code
                ->inputNumberFormat(PhoneInputNumberType::E164) // Save in E164 format (standard with country code)
                ->initialCountry('SA')
                ->countryOrder(['SA', 'AE', 'QA', 'KW', 'BH', 'EG', 'LB', 'OM']) // Prioritize these countries
                // Add validation using libphonenumber directly
                ->beforeStateDehydrated(function ($component, $state) {
                    if ($state) {
                        try {
                            // Validate the phone number using libphonenumber directly
                            $phoneUtil = PhoneNumberUtil::getInstance();
                            $phoneProto = $phoneUtil->parse($state, 'SA'); // Default to Saudi Arabia if no country code
                            if (!$phoneUtil->isValidNumber($phoneProto)) {
                                // Show notification for invalid phone number
                                Notification::make()
                                    ->danger()
                                    ->title('Invalid Phone Number')
                                    ->body('The phone number format is invalid. Please check and try again.')
                                    ->persistent()
                                    ->send();

                                throw \Illuminate\Validation\ValidationException::withMessages([
                                    $component->getName() => 'The phone number format is invalid.',
                                ]);
                            }

                            // Format to E.164 for consistent storage
//                            $e164Number = $phoneUtil->format($phoneProto, PhoneNumberFormat::E164);
//                            $component->state($e164Number);

                            // Custom format: +<countryCode>-<nationalNumber>
                            $formattedNumber = '+' . $phoneProto->getCountryCode() . '-' . $phoneProto->getNationalNumber();
                            $component->state($formattedNumber);

                        } catch (NumberParseException $e) {
                            // Show notification for exception
                            Notification::make()
                                ->danger()
                                ->title('Invalid Phone Number')
                                ->body('The phone number format is invalid: ' . $e->getMessage())
                                ->persistent()
                                ->send();

                            throw new \Exception('The phone number format is invalid: ' . $e->getMessage());
                        }
                    }
                });
        }

        if ($column === 'device_info') {
            return Forms\Components\KeyValue::make($column)
                ->label($label)
                ->keyLabel('Property')
                ->valueLabel('Value')
                ->disabled()
                ->dehydrated(false);
        }

        if ($countries) {
            $options = self::getCountries(true);
        }

        if ($timezones) {
            $options = self::getTimezones();
        }

        if ($isColor) {
            return ColorPicker::make('color');
        }

        if (!empty($DropdownField)) {
            // Handle relationship-based dropdowns
            if ($relationship) {
                $relationName = str_replace('_id', '', $column);

                // Create a dynamic relationship select
                $field = Forms\Components\Select::make($column)
                    ->label($label);

                // Get the model class
                $modelClass = $relationship['model'];

                // Get the title and value attributes
                $titleAttribute = $relationship['titleAttribute'];
                $valueAttribute = $relationship['valueAttribute'] ?? (new $modelClass)->getKeyName();

                // Apply query modifications if provided
                $query = $modelClass::query();
                if (isset($relationship['modifyQueryUsing']) && is_callable($relationship['modifyQueryUsing'])) {
                    $query = $relationship['modifyQueryUsing']($query);
                } else if ($modelClass === \App\Models\Status::class && isset($relationship['statusType'])) {
                    // Special handling for Status model to filter by type
                    $query->where('type', $relationship['statusType']);
                } else if ($modelClass === \App\Models\Status::class && $column === 'status_id') {
                    // Auto-detect status type based on model class name
                    $modelName = class_basename(static::$model);
                    if (str_contains($modelName, 'Customer')) {
                        $query->where('type', \App\Helpers\StatusHelper::STATUS_TYPE_CUSTOMER);
                    } else if (str_contains($modelName, 'User') || str_contains($modelName, 'Admin')) {
                        $query->where('type', \App\Helpers\StatusHelper::STATUS_TYPE_USER);
                    }
                }

                // Get records from the model with applied filters
                $records = $query->get();

                // Create options array from the records
                $relationOptions = [];
                foreach ($records as $record) {
                    if ($record->$titleAttribute == null) {
                        continue;
                    }
                    $relationOptions[$record->$valueAttribute] = $record->$titleAttribute;
                }

                // Set the options
                $field->options($relationOptions)
                    ->searchable($isSearchable)
                    ->multiple($isMultiple)
                    ->placeholder($placeholder)
                    ->required($required);

                if ($default) {
                    $field->default($default);
                }

                return $field;
            }

            if ($columnType === 'enum') {
                $options = self::getPossibleEnumValues($column);
            }

            // Handle options-based dropdowns
            $field = Forms\Components\Select::make($column)
                ->label($label)
                ->options($options)
                ->searchable($isSearchable)
                ->multiple($isMultiple)
                ->placeholder($placeholder)
                ->required($required);

            if ($default) {
                $field->default($default);
            }

            return $field;
        }

        if ($column === 'password') {
            return Forms\Components\TextInput::make($column)
                ->label($label)
                ->dehydrated(true)
                //                ->dehydrateStateUsing(fn ($state) => Hash::make($state))
                ->required()
                ->hiddenOn('edit')  // Hide on edit form
                ->password()
                ->revealable();
        }

        // Special handling for translatable fields
        if (in_array($column, self::$translatableFields ?? [])) {
            // Get current locale
            $locale = app()->getLocale();

            return Forms\Components\Textarea::make("{$column}.{$locale}")
                ->label($label)
                ->required($required)
                ->rows(4)
                ->helperText("Content will be saved for {$locale} locale");
        }

        switch ($columnType) {
            case 'integer':
            case 'bigint':
            case 'smallint':
                return Forms\Components\TextInput::make($column)
                    ->label($label)
                    ->numeric()
                    ->required($required);

            case 'decimal':
            case 'float':
            case 'double':
                return Forms\Components\TextInput::make($column)
                    ->label($label)
                    ->numeric()
                    ->step(0.01)
                    ->required($required);

            case 'boolean':
                return Forms\Components\Toggle::make($column)
                    ->label($label)
                    ->required($required);

            case 'text':
            case 'longtext':
                return Forms\Components\Textarea::make($column)
                    ->label($label)
                    ->rows(4)
                    ->required($required);

            case 'date':
                return Forms\Components\DatePicker::make($column)
                    ->label($label)
                    ->required($required);

            case 'datetime':
            case 'timestamp':
                return Forms\Components\DateTimePicker::make($column)
                    ->label($label)
                    ->required($required);

            case 'string':
            default:
                return Forms\Components\TextInput::make($column)
                    ->label($label)
                    ->required($required);
        }
    }

    // Tables
    public static function getTableFields(string $modelClass): array
    {
        $tableName = (new $modelClass)->getTable();
        $columns = Schema::getColumnListing($tableName);
        $fields = [];
        $deletedFields = [];

        foreach ($columns as $column) {
            if (in_array($column, self::$defaultTableExcludedFields) || in_array($column, self::$excludedTableFields)) {
                continue;
            }

            // Store deleted_at and deleted_by separately
            if (in_array($column, ['deleted_at', 'deleted_by'])) {
                $deletedFields[$column] = self::getTableColumn($column, (in_array($column, self::$sortableTableFields) || in_array('*', self::$sortableTableFields)));
            } else {
                $fields[] = self::getTableColumn($column, (in_array($column, self::$sortableTableFields) || in_array('*', self::$sortableTableFields)));
            }
        }

        // Add deleted_by and deleted_at at the end, ensuring they are next to each other
        if (isset($deletedFields['deleted_by'])) {
            $fields[] = $deletedFields['deleted_by'];
        }
        if (isset($deletedFields['deleted_at'])) {
            $fields[] = $deletedFields['deleted_at'];
        }

        return $fields;
    }

    public static function getTableFilters(string $modelClass): array
    {
        $tableName = (new $modelClass)->getTable();
        $columns = Schema::getColumnListing($tableName);
        $filters = [];

        foreach ($columns as $column) {
            if (in_array($column, ['deleted_at']) || (!in_array($column, static::$filterableFields) && !in_array('*', static::$filterableFields))) {
                continue;
            }

            $columnType = Schema::getColumnType($tableName, $column);

            $filters[] = self::getTableFiltersByType($column, $columnType);
        }

        return $filters;
    }

    private static function getTableFiltersByType(string $column, string $columnType)
    {
        $label = self::getColumnLabel($column);

        $DropdownField = self::$dropdownFields[$column] ?? [];
        extract($DropdownField);

        $options = $options ?? self::getDefaultStatuses();
        $isSearchable = $searchable ?? false;
        $isMultiple = $multiple ?? false;
        $placeholder = $placeholder ?? 'Select an option';
        $countries = $countries ?? false;
        $timezones = $timezones ?? false;
        $relationship = $relationship ?? null;

        if ($countries) {
            $options = self::getCountries(true);
        }

        if ($timezones) {
            $options = self::getTimezones();
        }

        // Handle relationship-based filters
        if ($relationship) {
            $modelClass = $relationship['model'];
            $titleAttribute = $relationship['titleAttribute'];
            $valueAttribute = $relationship['valueAttribute'] ?? (new $modelClass)->getKeyName();

            // Apply query modifications if provided
            $query = $modelClass::query();
            if (isset($relationship['modifyQueryUsing']) && is_callable($relationship['modifyQueryUsing'])) {
                $query = $relationship['modifyQueryUsing']($query);
            } else if ($modelClass === \App\Models\Status::class && isset($relationship['statusType'])) {
                // Special handling for Status model to filter by type
                $query->where('type', $relationship['statusType']);
            } else if ($modelClass === \App\Models\Status::class && $column === 'status_id') {
                // Auto-detect status type based on model class name
                $modelName = class_basename(static::$model);
                if (str_contains($modelName, 'Customer')) {
                    $query->where('type', \App\Helpers\StatusHelper::STATUS_TYPE_CUSTOMER);
                } else if (str_contains($modelName, 'User') || str_contains($modelName, 'Admin')) {
                    $query->where('type', \App\Helpers\StatusHelper::STATUS_TYPE_USER);
                }
            }

            // Get records from the model with applied filters
            $records = $query->get();

            $options = $records
                ->filter(function ($record) use ($titleAttribute) {
                    return $record->$titleAttribute !== null;
                })
                ->pluck($titleAttribute, $valueAttribute)
                ->toArray();

            return Tables\Filters\SelectFilter::make($column)
                ->label($label)
                ->options($options)
                ->searchable($isSearchable)
                ->multiple($isMultiple)
                ->placeholder($placeholder);
        }

        // Handle regular dropdown fields
        if (!empty($DropdownField)) {
            $input = Tables\Filters\SelectFilter::make($column)
                ->label($label)
                ->options($options)
                ->searchable($isSearchable)
                ->multiple($isMultiple)
                ->placeholder($placeholder);

            if ($isMultiple) {
                $input->query(function ($query, $data) use ($column) {
                    if (!empty($data)) {
                        $query->where(function ($q) use ($data, $column) {
                            foreach ($data as $value) {
                                if (!empty($value)) {
                                    $q->orWhereJsonContains($column, $value);
                                }
                            }
                        });
                    }
                });
            }

            return $input;
        }

        if ($columnType == 'boolean') {
            return Tables\Filters\SelectFilter::make($column)
                ->label($label)
                ->options([
                    '1' => 'Yes',
                    '0' => 'No',
                ]);
        }

        if (in_array($columnType, ['date', 'datetime']) || str_ends_with($column, '_at')) {
            return DateRangeFilter::make($column)
                ->drops(DropDirection::UP)
                ->label($label)
                ->query(function ($query, $data) use ($column) {
                    $dateRange = explode(' - ', $data[$column]);
                    $startDate = $dateRange[0] ?? null;
                    $endDate = $dateRange[1] ?? null;
                    if (!empty($startDate) && !empty($endDate)) {
                        $startDate = Carbon::createFromFormat('d/m/Y', $startDate)->format('Y-m-d');
                        $endDate = Carbon::createFromFormat('d/m/Y', $endDate)->format('Y-m-d');
                        $query->whereBetween($column, [$startDate . ' 00:00:00', $endDate . ' 23:59:59']);
                    }
                });
        }

        return Tables\Filters\Filter::make($column)
            ->query(function ($query, $data) use ($column) {
                if (isset($data['value']) && $data['value'] !== '') {
                    $query->where($column, 'like', '%' . $data['value'] . '%');
                }
            })
            ->form([
                Forms\Components\TextInput::make('value')->label($label)->placeholder("Enter $label value"),
            ]);
    }

    private static function getTableColumn(string $column, bool $sortable)
    {
        $label = self::getColumnLabel($column);

        $DropdownField = self::$dropdownFields[$column] ?? [];
        extract($DropdownField);

        $countries = $countries ?? false;
        $relationship = $relationship ?? null;
        $isColor = in_array($column, self::$colorFields);

        // Handle subscription status and payment status columns
        if (in_array($column, ['subscription_status', 'payment_status', 'history_event'])) {
            return Tables\Columns\TextColumn::make($column)
                ->label($label)
                ->sortable($sortable)
                ->searchable(self::$tableSearch)
                ->toggleable()
                ->formatStateUsing(fn (string $state): string => \App\Enums\Subscriptions::getLabel($state) ?? $state);
        }

        // Handle audit fields (created_by, updated_by, deleted_by)
        if (in_array($column, ['created_by', 'updated_by', 'deleted_by', 'user_id'])) {
            return Tables\Columns\TextColumn::make($column)
                ->label($label)
                ->searchable(self::$tableSearch)
                ->toggleable()
                ->sortable($sortable)
                ->formatStateUsing(function ($state) {
                    if (!$state) return '-';
                    $user = \App\Models\User::find($state);
                    return $user ? $user->name : $state;
                });
        }

        // Handle relationship-based fields
        if ($relationship) {
            $relationName = str_replace('_id', '', $column);
            $titleAttribute = $relationship['titleAttribute'];

            return Tables\Columns\TextColumn::make("{$relationName}.{$titleAttribute}")
                ->searchable(self::$tableSearch)
                ->toggleable()
                ->sortable($sortable)
                ->label($label);
        }

        if ($column === 'device_info') {
            return Tables\Columns\TextColumn::make($column)
                ->label($label)
                ->searchable(self::$tableSearch)
                ->toggleable()
                ->sortable($sortable)
                ->formatStateUsing(function ($state) {
                    if (empty($state)) return '-';

                    // Try to parse as JSON first
                    if (is_string($state) && (str_starts_with($state, '{') || str_starts_with($state, '['))) {
                        $deviceInfo = json_decode($state, true);

                        if (is_array($deviceInfo)) {
                            $formatted = [];
                            if (!empty($deviceInfo['device'])) $formatted[] = "Device: " . ucfirst($deviceInfo['device']);
                            if (!empty($deviceInfo['platform'])) $formatted[] = "Platform: " . $deviceInfo['platform'];
                            if (!empty($deviceInfo['browser'])) $formatted[] = "Browser: " . $deviceInfo['browser'];

                            return empty($formatted) ? '-' : implode(' | ', $formatted);
                        }
                    }

                    // Handle comma-separated string format
                    if (is_string($state) && str_contains($state, ',')) {
                        $parts = array_map('trim', explode(',', $state));
                        $formatted = [];

                        if (!empty($parts[0])) $formatted[] = "Device: " . ucfirst($parts[0]);
                        if (!empty($parts[1])) $formatted[] = "Platform: " . $parts[1];
                        if (!empty($parts[2])) $formatted[] = "Browser: " . $parts[2];

                        return empty($formatted) ? '-' : implode(' | ', $formatted);
                    }

                    // If it's just a simple string, return it
                    return $state;
                });
        }

        $field = Tables\Columns\TextColumn::make($column)
            ->searchable(self::$tableSearch)
            ->toggleable()
            ->sortable($sortable)
            ->label($label);

        if ($isColor) {
            return $field->badge()
                ->formatStateUsing(fn() => '')
                ->extraAttributes(fn($state) => [
                    'style' => "width: 20px; height: 20px; display: inline-block; border-radius: 4px; padding:0px; background-color: {$state}",
                ])->alignCenter();
        }

        if ($countries) {
            $field->badge()->formatStateUsing(function ($state) {
                return self::getCountry($state, true);
            });
        }

        // Handle translatable fields
        if (in_array($column, self::$translatableFields ?? [])) {
            $locale = app()->getLocale();

            return Tables\Columns\TextColumn::make("{$column}.{$locale}")
                ->searchable(self::$tableSearch)
                ->toggleable()
                ->sortable($sortable)
                ->label($label);
        }

        //        if (!empty($DropdownField)) {
        //            return $field->badge()
        //                ->color(fn ($state) => match ($state) {
        //                    'active' => 'success',
        //                    'inactive' => 'danger',
        //                    'deleted' => 'gray',
        //                    default => 'primary',
        //                });
        //        }

        return $field;
    }

    private static function getDefaultStatuses(): array
    {
        return [
            'active' => 'Active',
            'inactive' => 'Inactive',
            'deleted' => 'Deleted',
        ];
    }

    private static function getRelationalOptions(array $relation): array
    {
        $options = [];
        $table = $relation[0];
        $key = $relation[1];
        $value = $relation[2];

        $options = DB::table($table)->pluck($value, $key)->toArray();

        return $options;
    }

    private static function getColumnLabel(string $column): string
    {
        $label = ucwords(str_replace('_', ' ', $column));

        if (in_array(strtolower($column), array_keys(self::$attributesLabels))) {
            return self::$attributesLabels[$column];
        }

        return $label;
    }

    /**
     * Get common email validation rules
     */
    public static function getEmailValidationRules()
    {
        return [
            'required',
            'string',
            'email',
            'max:255',
            new \App\Rules\BusinessEmailRule(),
        ];
    }

    /**
     * Get enum options dynamically from db
     */
    public static function getPossibleEnumValues($column)
    {
        $instance = new static::$model;

        $arr = DB::select('SHOW COLUMNS FROM ' . $instance->getTable() . ' WHERE Field = ?', [$column]);

        if (count($arr) == 0) {
            return array();
        }

        $enumStr = $arr[0]->Type;

        preg_match_all("/'([^']+)'/", $enumStr, $matches);

        if (!isset($matches[1])) {
            return [];
        }

        $enumValues = [];
        foreach ($matches[1] as $value) {
            $enumValues[$value] = self::parseLabel($value);
        }

        return $enumValues;
    }

    /**
    * Format Labels
    */
    public static function parseLabel($label)
    {
        $label = str_replace(['-', '_'], ' ', $label);

        return ucwords($label);
    }
}
