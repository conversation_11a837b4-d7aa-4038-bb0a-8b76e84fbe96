<?php

namespace App\Traits;

use App\Enums\DefaultValues;
use Carbon\Carbon;

trait Timezones
{
    use Blamable;

    /**
     * used to override Eloqent models date time attributes ( a datetime cast is required ) 
     */
    public function getAttribute($key)
    {
        $value = parent::getAttribute($key);

        if ($this->isDateAttribute($key) && $value) {
            /**
             * We'll return the formatted date in UTC format for now as per Ihab request 
             */
            // return Carbon::parse($value, 'UTC')->setTimezone(self::getUserTimezone());
            return Carbon::parse($value, 'UTC')->setTimezone(DefaultValues::TIMEZONE->get());
        }

        return $value;
    }

    /**
     * get user timezone 
     */
    public static function getUserTimezone(): string
    {
        $user = self::getBlamable();
        $timezone = $user->timezone ?? DefaultValues::TIMEZONE->get();

        return $timezone;
    }

    /**
     * Fetch all timezones 
     */
    public static function getTimezones(): array
    {
        $timezones = [];

        foreach (\DateTimeZone::listIdentifiers() as $timezone) {
            $dateTime = new \DateTime('now', new \DateTimeZone($timezone));
            $offset = $dateTime->format('P');
            $timezones[$timezone] = "(UTC $offset) $timezone";
        }

        return $timezones;
    }

    /**
     * Format Date Attributes 
     */
    public function getFormattedDateAttribute(string $key, bool $dateTime = true)
    {
        $value = $this->getAttribute($key);

        if ($value instanceof Carbon) {
            return $value->format($dateTime ? DefaultValues::DATE_TIME_FORMAT->get() : DefaultValues::DATE_FORMAT->get());
        }

        return $value;
    }

    /**
     * Allow the user to call attr_formatted
     */
    public function __get($key)
    {
        if (str_ends_with($key, '_formatted')) {
            $attribute = str_replace('_formatted', '', $key);
            return $this->getFormattedDateAttribute($attribute);
        }

        if (str_ends_with($key, '_formatted_date')) {
            $attribute = str_replace('_formatted_date', '', $key);
            // return $this->getFormattedDateAttribute($attribute, false);
            return $this->getFormattedDateAttribute($attribute);
        }

        return parent::__get($key);
    }

    /**
     * Format a date time value to a specific format. 
     */
    public static function formatDateTime($value, ?string $format = null): ?string
    {
        if (!$value) {
            return null;
        }

        $outputFormat = $format ?? DefaultValues::DATE_TIME_FORMAT->get();

        if ($value instanceof Carbon) {
            return $value->format($outputFormat);
        }

        try {
            $inputFormat = DefaultValues::DATE_TIME_FORMAT->get();
            $date = Carbon::createFromFormat($inputFormat, $value, 'UTC');
            return $date->format($outputFormat);
        } catch (\Exception $e) {
            try {
                $date = Carbon::parse($value, 'UTC');
                return $date->format($outputFormat);
            } catch (\Exception $e2) {
                return null;
            }
        }
    }
}
