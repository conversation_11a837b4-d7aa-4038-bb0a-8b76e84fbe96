<?php

declare(strict_types=1);

namespace App\Traits;

use App\Support\Utils;
use <PERSON>zhanSalleh\FilamentShield\Facades\FilamentShield;
use BezhanSalleh\FilamentShield\FilamentShieldPlugin;
use Filament\Forms;
use Filament\Forms\Components\Component;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;

trait HasShieldFormComponents
{
    public static function getShieldFormComponents(): Component
    {
        return Forms\Components\Tabs::make('Permissions')
            ->contained()
            ->tabs([
                static::getTabFormComponentForResources()->hidden(fn($get) => $get('type') !== 'admin'),
                static::getTabFormComponentForPage()->hidden(fn($get) => $get('type') !== 'admin'),
                static::getTabFormComponentForWidget()->hidden(fn($get) => $get('type') !== 'admin'),
                static::getTabFormComponentForCustomPermissions()->hidden(fn($get) => $get('type') !== 'admin'),
                static::getTabFormComponentForCustomersPermissions()->hidden(fn($get) => $get('type') !== 'customer'),
            ])
            ->columnSpan('full');
    }

    public static function getResourceEntitiesSchema(): ?array
    {
        return collect(FilamentShield::getResources())
            ->sortKeys()
            ->map(function ($entity) {
                $sectionLabel = strval(
                    static::shield()->hasLocalizedPermissionLabels()
                        ? FilamentShield::getLocalizedResourceLabel($entity['fqcn'])
                        : $entity['model']
                );

                // Differentiate between Admin User and Customer User resources
                if (str_contains($entity['fqcn'], 'AdminUserResource')) {
                    $sectionLabel = 'Admin Users';
                } elseif (str_contains($entity['fqcn'], 'CustomerUserResource')) {
                    $sectionLabel = 'Customer Users';
                }

                return Forms\Components\Section::make($sectionLabel)
//                    ->description(fn() => new HtmlString('<span style="word-break: break-word;">' . Utils::showModelPath($entity['fqcn']) . '</span>'))
                    ->compact()
                    ->schema([
                        static::getCheckBoxListComponentForResource($entity),
                    ])
                    ->columnSpan(static::shield()->getSectionColumnSpan())
                    ->collapsible();
            })
            ->toArray();
    }

    public static function getCustomersSchema(): ?array
    {
        $permissions = config('customer-permissions', []);

        return collect($permissions)->map(function ($actions, $module) {
            $sectionLabel = static::shield()->hasLocalizedPermissionLabels()
                ? ucwords(str_replace('-', ' ', $module))
                : $module;

            $permissionsList = collect($actions)->mapWithKeys(function ($action) use ($module) {
                return [$action . '::' . $module => ucwords(str_replace('_', ' ', $action))];
            })->toArray();

            return Forms\Components\Section::make($sectionLabel)
                ->description(fn() => new HtmlString('<span>Manage ' . $sectionLabel . ' permissions</span>'))
                ->compact()
                ->schema([self::getCheckboxListFormComponent('customer_' . $module, $permissionsList, false)])
                ->collapsible();
        })->values()->toArray();
    }


    public static function getResourceTabBadgeCount(): ?int
    {
        return collect(FilamentShield::getResources())
            ->map(fn($resource) => count(static::getResourcePermissionOptions($resource)))
            ->sum();
    }

    public static function getResourcePermissionOptions(array $entity): array
    {
        return collect(Utils::getResourcePermissionPrefixes($entity['fqcn']))
            ->flatMap(function ($permission) use ($entity) {
                $name = $permission . '_' . $entity['resource'];
                $label = static::shield()->hasLocalizedPermissionLabels()
                    ? FilamentShield::getLocalizedResourcePermissionLabel($permission)
                    : $name;

                return [
                    $name => $label,
                ];
            })
            ->toArray();
    }

    public static function setPermissionStateForRecordPermissions(Component $component, string $operation, array $permissions, ?Model $record): void
    {
        if (in_array($operation, ['edit', 'view'])) {

            if (blank($record)) {
                return;
            }
            if ($component->isVisible() && count($permissions) > 0) {
                $component->state(
                    collect($permissions)
                        /** @phpstan-ignore-next-line */
                        ->filter(fn($value, $key) => $record->checkPermissionTo($key))
                        ->keys()
                        ->toArray()
                );
            }
        }
    }

    public static function getPageOptions(): array
    {
        return collect(FilamentShield::getPages())
            ->flatMap(fn($page) => [
                $page['permission'] => static::shield()->hasLocalizedPermissionLabels()
                    ? FilamentShield::getLocalizedPageLabel($page['class'])
                    : $page['permission'],
            ])
            ->toArray();
    }

    public static function getWidgetOptions(): array
    {
        return collect(FilamentShield::getWidgets())
            ->flatMap(fn($widget) => [
                $widget['permission'] => static::shield()->hasLocalizedPermissionLabels()
                    ? FilamentShield::getLocalizedWidgetLabel($widget['class'])
                    : $widget['permission'],
            ])
            ->toArray();
    }

    public static function getCustomPermissionOptions(): ?array
    {
        return FilamentShield::getCustomPermissions()
            ->mapWithKeys(fn($customPermission) => [
                $customPermission => static::shield()->hasLocalizedPermissionLabels() ? str($customPermission)->headline()->toString() : $customPermission,
            ])
            ->toArray();
    }

    public static function getCustomersPermissionOptions(): ?array
    {
        $permissions = config('customer-permissions', []);

        $formattedPermissions = collect($permissions)->flatMap(function ($actions, $module) {
            return collect($actions)->mapWithKeys(function ($action) use ($module) {

                $permissionName = "$module.$action";

                $label = static::shield()->hasLocalizedPermissionLabels()
                    ? str("$module $action")->headline()->toString()
                    : $permissionName;

                return [$permissionName => $label];
            });
        })->toArray();

        return $formattedPermissions;
    }

    public static function getTabFormComponentForResources(): Component
    {
        return static::shield()->hasSimpleResourcePermissionView()
            ? static::getTabFormComponentForSimpleResourcePermissionsView()
            : Forms\Components\Tabs\Tab::make('resources')
            ->label(__('filament-shield::filament-shield.resources'))
            ->visible(fn(): bool => (bool) Utils::isResourceEntityEnabled())
            ->badge(static::getResourceTabBadgeCount())
            ->schema([
                Forms\Components\Grid::make()
                    ->schema(static::getResourceEntitiesSchema())
                    ->columns(static::shield()->getGridColumns()),
            ]);
    }

    public static function getCheckBoxListComponentForResource(array $entity): Component
    {
        $permissionsArray = static::getResourcePermissionOptions($entity);

        return static::getCheckboxListFormComponent(
            name: $entity['resource'],
            options: $permissionsArray,
            columns: static::shield()->getResourceCheckboxListColumns(),
            columnSpan: static::shield()->getResourceCheckboxListColumnSpan(),
            searchable: false
        );
    }

    public static function getTabFormComponentForPage(): Component
    {
        $options = static::getPageOptions();
        $count = count($options);

        return Forms\Components\Tabs\Tab::make('pages')
            ->label(__('filament-shield::filament-shield.pages'))
            ->visible(fn(): bool => (bool) Utils::isPageEntityEnabled() && $count > 0)
            ->badge($count)
            ->schema([
                static::getCheckboxListFormComponent(
                    name: 'pages_tab',
                    options: $options,
                ),
            ]);
    }

    public static function getTabFormComponentForWidget(): Component
    {
        $options = static::getWidgetOptions();
        $count = count($options);

        return Forms\Components\Tabs\Tab::make('widgets')
            ->label(__('filament-shield::filament-shield.widgets'))
            ->visible(fn(): bool => (bool) Utils::isWidgetEntityEnabled() && $count > 0)
            ->badge($count)
            ->schema([
                static::getCheckboxListFormComponent(
                    name: 'widgets_tab',
                    options: $options,
                ),
            ]);
    }

    public static function getTabFormComponentForCustomPermissions(): Component
    {
        $options = static::getCustomPermissionOptions();
        $count = count($options);

        return Forms\Components\Tabs\Tab::make('custom')
            ->label(__('filament-shield::filament-shield.custom'))
            ->visible(fn(): bool => (bool) Utils::isCustomPermissionEntityEnabled() && $count > 0)
            ->badge($count)
            ->schema([
                static::getCheckboxListFormComponent(
                    name: 'custom_permissions',
                    options: $options,
                ),
            ]);
    }

    public static function getTabFormComponentForCustomersPermissions(): Component
    {
        $options = static::getCustomersPermissionOptions();
        $count = count($options);

        return Forms\Components\Tabs\Tab::make('custom')
            ->label(__('Customers'))
            ->visible(fn(): bool => (bool) Utils::isCustomersPermissionEntityEnabled() && $count > 0)
            ->badge($count)
            ->schema(self::getCustomersSchema());
    }

    public static function getTabFormComponentForSimpleResourcePermissionsView(): Component
    {
        $options = FilamentShield::getAllResourcePermissions();
        $count = count($options);

        return Forms\Components\Tabs\Tab::make('resources')
            ->label(__('filament-shield::filament-shield.resources'))
            ->visible(fn(): bool => (bool) Utils::isResourceEntityEnabled() && $count > 0)
            ->badge($count)
            ->schema([
                static::getCheckboxListFormComponent(
                    name: 'resources_tab',
                    options: $options,
                ),
            ]);
    }

    public static function getCheckboxListFormComponent(string $name, array $options, bool $searchable = true, array | int | string | null $columns = null, array | int | string | null $columnSpan = null): Component
    {
        return Forms\Components\CheckboxList::make($name)
            ->label('')
            ->options(fn(): array => $options)
            ->searchable($searchable)
            ->afterStateHydrated(
                fn(Component $component, string $operation, ?Model $record) => static::setPermissionStateForRecordPermissions(
                    component: $component,
                    operation: $operation,
                    permissions: $options,
                    record: $record
                )
            )
            ->dehydrated(fn($state) => ! blank($state))
            ->bulkToggleable()
            ->gridDirection('row')
            ->columns($columns ?? static::shield()->getCheckboxListColumns())
            ->columnSpan($columnSpan ?? static::shield()->getCheckboxListColumnSpan());
    }

    public static function shield(): FilamentShieldPlugin
    {
        return FilamentShieldPlugin::get();
    }
}
