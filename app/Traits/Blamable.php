<?php

namespace App\Traits;

use App\Enums\DefaultValues;
use App\Helpers\RbacHelper;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

trait Blamable 
{
    /**
    * Set Created & Updated By ( Insert ) 
    */
    public static function setCreatedUpdatedBy(Model $model): void
    {
        $blamableId = self::getBlamableId();

        if(self::usesCreatedBy($model) ) { $model->created_by = $blamableId; }
        if(self::usesUpdatedBy($model) ) { $model->updated_by = $blamableId; }
    }

    /**
    * Set Updated By ( Update ) 
    */
    public static function setUpdatedBy(Model $model): void
    {
        if(self::usesUpdatedBy($model) ) { $model->updated_by = self::getBlamableId(); }
    }

    /**
    * Set Deleted By ( Soft Delete ) 
    */
    public static function setDeletedBy(Model $model): void
    {
        if(self::hasSoftDeletes($model))
        {
            $model->deleted_by = self::getBlamableId();
            $model->saveQuietly();
        }
    }

    /**
     * Check if the model supports created by.
     */
    public static function usesCreatedBy(Model $model): bool
    {
        return in_array('created_by', $model->getFillable());
    }

    /**
     * Check if the model supports updated_by.
     */
    public static function usesUpdatedBy(Model $model): bool
    {
        return in_array('updated_by', $model->getFillable());
    }

    /**
     * Check if the model supports soft deletes.
     */
    public static function hasSoftDeletes(Model $model): bool
    {
        return in_array('deleted_by', $model->getFillable());
    }

    /**
     * Get Blamable Name Using His Id ( DB )
     */
    public static function getBlamableNameById(?string $id = null): string | null
    {
        if(!empty($id)){
            $user = User::where('user_id', $id)->first();

            if(!empty($user)){ 
                $role = $user->roles()->first();
                
                if(!empty($role) && $role->type === RbacHelper::TYPE_ADMIN)
                {
                    return config('settings.default_operator_name');
                }

                return $user->name;
            }
        }

        return null;
    }

    /**
     * Get Blamable Id ( Auth )
     */
    public static function getBlamableId(): string | null
    {
        $user = self::getBlamable();

        return !empty($user) ? $user->user_id : DefaultValues::SYSTEM_USER_ID->get();
    }

    /**
     * Get Blamable Using Auth Object 
     */
    public static function getBlamable()
    {
        return Auth::user() ?? Auth::guard('api')->user();
    }
}
