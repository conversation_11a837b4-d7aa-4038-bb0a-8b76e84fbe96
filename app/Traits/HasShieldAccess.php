<?php

namespace App\Traits;

use Illuminate\Support\Str;

trait HasShieldAccess
{
    use Blamable;

    /**
     * Check Resource Access.
     */
    public static function canAccess(): bool
    {
        $user = self::getBlamable();

        if (! $user || ! isset(static::$model)) {
            return false;
        }

        $modelName = class_basename(static::$model);
        $snake = Str::snake($modelName); 
        $parts = explode('_', $snake);  

        if (count($parts) >= 2) {
            $prefix = array_shift($parts);               
            $suffix = implode('_', $parts);             
            $permission = "view_{$prefix}::{$suffix}";   
        } else {
            $permission = "view_{$snake}";
        }

        return $user->hasPermissionTo($permission);
    }
}
