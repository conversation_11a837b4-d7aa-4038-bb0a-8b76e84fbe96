<?php

namespace App\Traits;

use App\Models\Country;

trait Locations
{
    public static $countries = [];

    public static function getCountriesJson(bool $withIcons = false): array
    {
        if (!empty(self::$countries)) {
            return self::$countries;
        }

        $path = public_path('json/countries.json');

        if (!file_exists($path)) {
            return [];
        }

        $json = file_get_contents($path);
        $countries = json_decode($json, true) ?? [];

        self::$countries = array_reduce($countries, function ($carry, $country) use ($withIcons) {
            if (!empty($country['code']) && !empty($country['name'])) {
                $carry[$country['code']] = $withIcons ? $country['emoji'] . ' ' . $country['name'] : $country['name'];
            }
            return $carry;
        }, []);

        return self::$countries;
    }

    public static function getCountryJson(string $code, bool $withIcon = false): string
    {
        $countries = self::getCountries($withIcon);

        return $countries[$code] ?? "";
    }

    /**
    * Get Dropdown Key => Value countries list 
    */
    public static function getCountries(bool $withIcons = false): array
    {
        if (!empty(self::$countries)) {
            return self::$countries;
        }

        $countries = Country::all();
        self::$countries = $countries->reduce(function ($carry, $country) use ($withIcons) {
            if (!empty($country->code) && !empty($country->name)) {
                $carry[$country->code] = $withIcons ? $country->emoji . ' ' . $country->name : $country->name;
            }
            return $carry;
        }, []);

        return self::$countries;
    }

    public static function getCountry(string $code, bool $withIcon = false): string
    {
        $country = Country::where('code', $code)->first();

        if ($country) {
            return $withIcon ? $country->emoji . ' ' . $country->name : $country->name;
        }
    
        return "";
    }
}
