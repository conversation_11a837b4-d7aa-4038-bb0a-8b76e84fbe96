<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Process\Process;

class ClocReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clock:report';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate a report of the lines of code in the app';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $excludeDirs = 'node_modules,storage,vendor';

        // Print the excluded directories before running cloc
        $this->info("\nExcluding directories: " . $excludeDirs . "\n");

        $process = new Process(['cloc', '.', '--exclude-dir=' . $excludeDirs]);
        $process->run();

        if (!$process->isSuccessful()) {
            $this->error('Error running cloc: ' . $process->getErrorOutput());
            return 1;
        }

        // Print cloc output with spacing
        $this->info("\n" . $process->getOutput() . "\n");

        return 0;
    }
}
