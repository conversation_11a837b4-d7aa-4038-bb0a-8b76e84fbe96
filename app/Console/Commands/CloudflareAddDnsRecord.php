<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CloudflareService;

class CloudflareAddDnsRecord extends Command
{
    protected $signature = 'cloudflare:add-dns {name : The DNS record name (e.g. sub.example.com)}';

    protected $description = 'Add a DNS record to Cloudflare for testing purpose';

    protected CloudflareService $cloudflare;

    public function __construct(CloudflareService $cloudflare)
    {
        parent::__construct();

        $this->cloudflare = $cloudflare;
    }

    public function handle()
    {
        $name = $this->argument('name');

        $this->info("Adding DNS record for: {$name}");

        $response = $this->cloudflare->addDNSRecord(
            type: 'A',
            name: $name,
            content: '***************', // You can make this dynamic if you want
            ttl: 0,
            proxied: false,
        );

        if ($response['success']) {
            $this->info('DNS record added successfully!');
            return 0;
        }

        $this->error('Failed to add DNS record:');
        foreach ($response['errors'] ?? [] as $error) {
            $this->error(" - [{$error['code']}] {$error['message']}");
        }

        return 1;
    }
}
