<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\ProvisionClientResourcesJob; // Make sure this path is correct
use Illuminate\Support\Str;

class ProvisionClientGcpResourcesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'gcp:provision-client
                            {clientIdentifier : A unique identifier for the client (e.g., "my-awesome-client")}
                            {--ipName= : Optional specific name for the static IP address}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dispatches a job to provision Google Cloud resources (Secret and Static IP) for a client.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $clientIdentifier = Str::slug($this->argument('clientIdentifier')); // Sanitize for resource naming
        $ipAddressName = $this->option('ipName') ? Str::slug($this->option('ipName')) : null;

        if (empty($clientIdentifier)) {
            $this->error('Client identifier cannot be empty.');
            return Command::FAILURE;
        }

        $this->info("Dispatching job to provision resources for client: {$clientIdentifier}");
        if ($ipAddressName) {
            $this->info("Requested IP address name: {$ipAddressName}");
        } else {
            $this->info("An IP address name will be auto-generated based on the client identifier.");
        }

        ProvisionClientResourcesJob::dispatch($clientIdentifier, true, $ipAddressName);

        $this->info("✅ Job dispatched successfully for client: {$clientIdentifier}.");
        $this->comment("Don't forget to run your queue worker to process the job: php artisan queue:work");

        return Command::SUCCESS;
    }
}
