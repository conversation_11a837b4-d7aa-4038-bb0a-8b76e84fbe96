<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use function Laravel\Prompts\text;

class MakeRequest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:aj-request {name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate a custom request with dynamic validation rules for create or update';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $request = (string) str($this->argument('name') ?? text(
            label: 'What is the request name?',
            placeholder: 'UpdateCustomerRequest',
            required: true,
        ))
            ->studly()
            ->beforeLast('Request')
            ->trim('/')
            ->trim('\\')
            ->trim(' ')
            ->studly()
            ->replace('/', '\\');

        $stubPath = base_path('stubs/app/FormRequest.stub');
        $requestPath = app_path("Http/Requests/{$request}.php");

        if (!\Illuminate\Support\Facades\File::exists($stubPath)) {
            $this->components->error("Stub file not found: {$stubPath}");
            return static::FAILURE;
        }

        $stubContent = \Illuminate\Support\Facades\File::get($stubPath);

        $requestContent = str_replace('{{ requestName }}', $request, $stubContent);

        \Illuminate\Support\Facades\File::put($requestPath, $requestContent);

        $this->components->info("Request [{$request}] created successfully at [{$requestPath}].");

        return static::SUCCESS;
    }
}
