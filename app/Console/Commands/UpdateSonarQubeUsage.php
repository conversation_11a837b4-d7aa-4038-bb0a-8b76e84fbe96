<?php

namespace App\Console\Commands;

use App\Enums\SubscriptionLicenses;
use Illuminate\Console\Command;
use App\Models\Subscription; // Assuming your model is here
use App\Services\SonarQubeService;
use Illuminate\Support\Facades\Log;
use Exception;

class UpdateSonarQubeUsage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sonarqube:update-usage';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetches SonarQube usage for subscriptions via their licenses and updates quotas.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::info('SonarQube Usage Update: Scheduled task started.');
        $this->info('SonarQube Usage Update: Scheduled task started.');

        $licenseConstraints = function ($query) {
            $query->whereNotNull('sonar_url')
                ->where('sonar_url', '!=', '')
                ->whereNotNull('sonar_api_token')
                ->where('sonar_api_token', '!=', '')
                ->where('environment_status', '=', SubscriptionLicenses::ENVIRONMENT_STATUS_ACTIVE->value);
        };

        $subscriptions = Subscription::whereHas('subscriptionLicenses', $licenseConstraints)
            ->with(['subscriptionLicenses' => $licenseConstraints])
            ->get();

        if ($subscriptions->isEmpty()) {
            Log::info('SonarQube Usage Update: No subscriptions found with relevant SonarQube licenses.');
            $this->info('SonarQube Usage Update: No subscriptions found with relevant SonarQube licenses.');
            return Command::SUCCESS;
        }

        $this->info("Found {$subscriptions->count()} subscriptions to process for SonarQube usage update.");

        foreach ($subscriptions as $subscription) {
            $this->info("Processing Subscription ID: {$subscription->id}");
            Log::info("SonarQube Usage Update: Processing Subscription ID: {$subscription->id}");

            if ($subscription->subscriptionLicenses->isEmpty()) {
                Log::info("SonarQube Usage Update: Subscription ID {$subscription->id} has no licenses, skipping (should not happen due to whereHas).");
                $this->warn("Subscription ID {$subscription->id} has no licenses, skipping.");
                continue;
            }

            foreach ($subscription->subscriptionLicenses as $license) {
                $this->info("  Processing License ID: {$license->id} for Subscription ID: {$subscription->id} (URL: {$license->sonar_url})");
                Log::info("SonarQube Usage Update:   Processing License ID: {$license->id}, URL: {$license->sonar_url}");

                try {
                    if ((empty($license->sonar_url) || $license->sonar_url == "") || (empty($license->sonar_api_token) || $license->sonar_api_token == "")) {
                        Log::warning("SonarQube Usage Update: Skipping License ID {$license->id} for Subscription ID {$subscription->id} due to missing SonarQube URL or API token.");
                        $this->warn("  Skipping License ID {$license->id} (Subscription: {$subscription->id}) due to missing SonarQube URL or API token.");
                        continue;
                    }

                    $sonarService = new SonarQubeService(
                        $license->sonar_url,
                        $license->sonar_api_token
                    );

                    $usageDetails = $sonarService->getUsageDetails();

                    if (isset($usageDetails['error']) && $usageDetails['error'] !== null) {
                        $errorMessage = is_array($usageDetails['error']) ? json_encode($usageDetails['error']) : $usageDetails['error'];
                        Log::error("SonarQube Usage Update: Failed to get usage details for License ID {$license->id} (Subscription ID {$subscription->id}). Error: {$errorMessage}");
                        $this->error("  Failed for License ID {$license->id} (Subscription: {$subscription->id}): {$errorMessage}");
                        continue; // Skip to the next license
                    }

                    if (isset($usageDetails['loc_used'])) {
                        $usedQuota = (int) $usageDetails['loc_used'];
                        $assignedQuota = (int) $subscription->assigned_quota;

                        $subscription->used_quota = $usedQuota;
                        $subscription->remaining_quota = $assignedQuota - $usedQuota;

                        $subscription->save();

                        Log::info("SonarQube Usage Update: Successfully updated usage for Subscription ID {$subscription->id} based on License ID {$license->id}. Used: {$usedQuota}, Remaining: {$subscription->remaining_quota}");
                        $this->info("  Successfully updated usage for Subscription ID {$subscription->id} (via License ID {$license->id}). Used: {$usedQuota}, Remaining: {$subscription->remaining_quota}");
                    } else {
                        Log::warning("SonarQube Usage Update: 'loc_used' not found in usage details for License ID {$license->id} (Subscription ID {$subscription->id}). Raw details: " . json_encode($usageDetails));
                        $this->warn("  'loc_used' not found in usage details for License ID {$license->id} (Subscription: {$subscription->id}).");
                    }
                } catch (Exception $e) {
                    Log::error("SonarQube Usage Update: Exception while processing License ID {$license->id} (Subscription ID {$subscription->id}). Message: " . $e->getMessage());
                    $this->error("  Exception for License ID {$license->id} (Subscription: {$subscription->id}): " . $e->getMessage());
                }
            }
        }

        Log::info('SonarQube Usage Update: Scheduled task finished.');
        $this->info('SonarQube Usage Update: Scheduled task finished.');
        return Command::SUCCESS;
    }
}
