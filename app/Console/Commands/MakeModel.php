<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use function Laravel\Prompts\text;

class MakeModel extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:aj-model {name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'generate custom models';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $model = (string) str($this->argument('name') ?? text(
            label: 'What is the model name?',
            placeholder: 'BlogPost',
            required: true,
        ))
            ->studly()
            ->beforeLast('Model')
            ->trim('/')
            ->trim('\\')
            ->trim(' ')
            ->studly()
            ->replace('/', '\\');


        $stubPath = base_path('stubs/app/ResourceModel.stub');
        $modelPath = app_path("Models/{$model}.php");

        if (!file_exists($stubPath)) {
            $this->components->error("Stub file not found: {$stubPath}");
            return static::FAILURE;
        }

        $stubContent = file_get_contents($stubPath);
        $modelContent = str_replace('{{ resourceClass }}', $model, $stubContent);

        file_put_contents($modelPath, $modelContent);

        $this->components->info("Model [{$model}] created successfully at [{$modelPath}].");

        return static::SUCCESS;
    }
}
