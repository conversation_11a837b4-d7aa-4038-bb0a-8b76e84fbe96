<?php

namespace App\View\Components\Email;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Span extends Component
{
    public string $fontWeight;

    /**
     * Create a new component instance.
     */
    public function __construct($fontWeight = '500')
    {
       $this->fontWeight = $fontWeight;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.email.span');
    }
}
