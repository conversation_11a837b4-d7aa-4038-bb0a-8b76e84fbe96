<?php

namespace App\View\Components\Email;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class OtpBox extends Component
{
    public string $code;

    public string $color;

    public string $marginBottom;

    public function __construct(string $code, string $color = '#F6F6F6', string $marginBottom = '28.4px')
    {
        $this->code = $code;
        $this->color = $color;
        $this->marginBottom = $marginBottom;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.email.otp-box');
    }
}
