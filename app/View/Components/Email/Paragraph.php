<?php

namespace App\View\Components\Email;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Paragraph extends Component
{
    public string $color;

    public string $marginBottom;

    public string $fontWeight;

    public function __construct($color = '#A6A6A6', $marginBottom = '12px', $fontWeight = '400')
    {
        $this->color = $color;
        $this->marginBottom = $marginBottom;
        $this->fontWeight = $fontWeight;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.email.paragraph');
    }
}
