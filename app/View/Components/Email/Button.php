<?php

namespace App\View\Components\Email;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class <PERSON>ton extends Component
{
    public string $color;

    public string $url;

    /**
     * Create a new component instance.
     */
    public function __construct($color = '#003CA4', $url = '#')
    {
        $this->color = $color;
        $this->url = $url;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.email.button');
    }
}
