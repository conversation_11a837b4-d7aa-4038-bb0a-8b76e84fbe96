<?php

namespace App\Helpers;

use App\Models\Role;
use App\Models\User;
use Illuminate\Support\Facades\Config;

class RbacHelper
{
    /**
    * Role Guard
    */
    const GUARD_API = 'api';
    const GUARD_WEB = 'web';

    /**
    * Role Types 
    */
    const TYPE_ADMIN = 'admin';
    const TYPE_CUSTOMER = 'customer';

    /**
    * Customers Roles 
    */
    const ROLE_PRIMARY = 'primary';
    const ROLE_ADMIN = 'admin';
    const ROLE_OPERATOR = 'operator';
    const ROLE_DEVELOPER = 'developer';
    const ROLE_READ_ONLY = 'read_only';
    const ROLE_CUSTOMER = 'customer';

    /**
    * Admins Roles
    */
    const ROLE_ISOLUTION_ADMIN = 'isolution_admin';
    const ROLE_SONAR_TEAM = 'sonar_team';
    const ROLE_FINANCE_TEAM = 'finance_team';

    /**
    * Get System Rbac Type options
    */
    public static function getSystemRbacTypes(?string $value = null): array | string
    {
        $options = [
            self::TYPE_ADMIN => 'iSolution Admin',
            self::TYPE_CUSTOMER => 'Customer Console User',
        ];

        if(!empty($value))
        {
            return $options[$value];
        }

        return $options;
    }

    /**
     * List Available Roles By Type 
     */
    public static function getRolesByType(string $type = self::TYPE_ADMIN, ?string $guard = self::GUARD_WEB)
    {
        $roles = Role::where([
            ['type', '=', $type],
            ['guard_name', '=', $guard],
        ])->get();

        return $roles;
    }

    /**
     * Get a user's role.
     *
     * @param User $user
     * @param bool $withPermissions
     * @return array|string|null
     */
    public static function getUserRole($user, bool $withPermissions = false): array | string | null
    {
        if (empty($user)) {
            return null;
        }

        $role = $user->roles()->first();

        if (empty($role)) {
            return null;
        }

        if ($withPermissions) {
            return [
                'name' => $role->name,
                'permissions' => self::getRolePermissions($role),
            ];
        }

        return $role->name;
    }

    /**
     * Return Formatted Permissions 
     */
    public static function getRolePermissions($role): array | null
    {
        if (empty($role) || empty($role->permissions)) {
            return null;
        }

        return collect($role->permissions)
            ->pluck('name')
            ->map(function ($permission) {
                return explode('::', $permission);
            })
            ->filter(function ($parts) {
                return count($parts) === 2;
            })
            ->reduce(function ($carry, $parts) {
                $carry[$parts[1]][] = $parts[0];
                return $carry;
            }, []);
    }

    /**
     * Get Primary Role Permissions.
     */
    public static function getRolePermissionsArray(?array $excludeModulesPermissions = []): array
    {
        $allPermissions = config('customer-permissions');

        foreach ($excludeModulesPermissions as $module => $excludedPermissions) {
            if (isset($allPermissions[$module])) {
                // If '*' is present, remove the entire module
                if (in_array('*', $excludedPermissions, true)) {
                    unset($allPermissions[$module]);
                    continue;
                }
                $allPermissions[$module] = array_diff($allPermissions[$module], $excludedPermissions);

                if (empty($allPermissions[$module])) {
                    unset($allPermissions[$module]);
                }
            }
        }

        return $allPermissions;
    }

}
