<?php

namespace App\Helpers;

use App\Enums\DefaultValues;
use App\Traits\Timezones;

class DateTimeHelper
{
    use Timezones;

    public static function parseDateTime(?string $datetime = null, ?string $format = 'd M, Y - h:i A')
    {
        $timezone = self::getUserTimezone();

        return !empty($datetime) ? \Carbon\Carbon::parse($datetime, 'UTC')->setTimezone($timezone)->format($format) : DefaultValues::DATE_TIME->get();
    }
}
