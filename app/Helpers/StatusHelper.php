<?php

namespace App\Helpers;

use App\Enums\DefaultValues;
use App\Models\Status;

class StatusHelper
{
    const STATUS_TYPE_CUSTOMER = 'customer';
    const STATUS_TYPE_USER = 'user';

    /**
    * Get Status ID by attribute and value ("name", "active")
    */
    public static function getStatusByAttributeName(string $attr, string $value, ?string $type = self::STATUS_TYPE_CUSTOMER): int
    {
        return Status::where($attr, $value)->where('type', $type)->first()->status_id ?? DefaultValues::FOREING_KEY->get();
    }

    /**
    * Get Customer Statuses 
    */
    public static function getCustomerStatuses(?bool $pluck = false)
    {
        $statuses = Status::where('type', self::STATUS_TYPE_CUSTOMER)->get();

        if ($pluck) {
            return self::pluckStatuses($statuses, 'name', 'status_id');
        }

        return $statuses;
    }

    /**
    * Get User Statuses  
    */
    public static function getUserStatuses(?bool $pluck = false)
    {
        $statuses = Status::where('type', self::STATUS_TYPE_USER)->get();

        if ($pluck) {
            return self::pluckStatuses($statuses, 'name', 'status_id');
        }

        return $statuses;
    }

    /**
    * Pluck data 
    */
    public static function pluckStatuses(array $data, string $key, string $value): array
    {
        return collect($data)->pluck($value, $key)->toArray();
    }
}
