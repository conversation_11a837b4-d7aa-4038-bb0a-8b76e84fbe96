<?php

namespace App\Helpers;

use App\Enums\ApiSettings;
use App\Mail\CustomerUserInvitationMail;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class InvitationHelper
{
    /**
     * Send an invitation to a user, or re-invite if already exists and hasn't accepted.
     *
     * @param  string  $email
     * @param  string  $name
     * @return void
     */
    public static function sendUserInvitation(string $email, string $name): void
    {
        $user = User::where('email', $email)->first();

        if (!empty($user)) {
            if (empty($user->invitation_token) || $user?->invitation_expires_at?->isPast()) {
                $user->update([
                    'invitation_token' => Str::random(64),
                    'invitation_expires_at' => Carbon::now()->addMinutes(ApiSettings::USER_INVITATION_EXPIRY_MINUTES->getValue()),
                ]);
            }
        } else {
            $user = User::create([
                'name' => $name,
                'email' => $email,
                'password' => null,
                'invitation_token' => Str::random(64),
                'invitation_expires_at' => Carbon::now()->addHours(ApiSettings::USER_INVITATION_EXPIRY_MINUTES->getValue()),
            ]);
        }

        Mail::to($user->email)->queue(new CustomerUserInvitationMail($user));
    }
}
