<?php

namespace App\Helpers;

use App\Enums\DefaultValues;
use App\Models\Currency;

class CurrencyHelper
{
    /**
     * Get Currency Symbol By Currency Name OR Iso code 
     */
    public static function getCurrencySymbol(string $label): string
    {
        $currency = Currency::where('name', $label)
            ->orWhere('iso', $label)
            ->first() ?? DefaultValues::CURRENCY_SYMBOL->get();

        return !empty($currency->symbol) ? $currency->symbol : DefaultValues::CURRENCY_SYMBOL->get();
    }
}
