<?php

namespace App\Helpers;

use App\Enums\ApiStatus;
use App\Enums\ValidationMessages;
use App\Models\User;
use App\Services\ApiResponseService;
use App\Traits\Blamable;

class CustomerHelper
{
    use Blamable;

    const CUSTOMER_REGISTRATION_TYPE_SELF_REGISTERED = 'self_registered';
    const CUSTOMER_REGISTRATION_TYPE_INVITED_BY_OPERATOR = 'invited_by_operator';

    public static function getCompanyUsers(ApiResponseService $apiResponseService, ?string $user_id = null)
    {
        $user = self::getBlamable();
        $companyId = $user->company_id ?? null;

        if(empty($companyId)){ return self::companyNotFound($apiResponseService); }

        $companyUsers = User::where([
            ['type', '=', 'customer'],
            ['company_id', '=', $companyId],
        ]);

        if(!empty($user_id))
        {
            $companyUsers = $companyUsers->where('user_id', '=', $user_id)->first();

            if(empty($companyUsers)) { return self::userNotFound($apiResponseService); }
        } else {
            $companyUsers = $companyUsers->get();
        }

        return $companyUsers;
    }

    public static function companyNotFound(ApiResponseService $apiResponseService)
    {
        return $apiResponseService->props(ApiStatus::NOT_FOUND)
        ->withSystemMessages(ValidationMessages::USER_NOT_RELATED_TO_A_COMPANY)
        ->send();
    }

    public static function userNotFound(ApiResponseService $apiResponseService)
    {
        return $apiResponseService->props(ApiStatus::NOT_FOUND)
                ->withSystemMessages(ValidationMessages::USER_NOT_FOUND)
                ->send();
    }
}
