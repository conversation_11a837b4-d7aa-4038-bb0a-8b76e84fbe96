<?php

namespace App\Helpers;

use App\Enums\SubscriptionReceipts;
use App\Enums\Subscriptions;
use App\Http\Resources\PlanFeatureResource;
use App\Models\Company;
use App\Models\Currency;
use App\Models\Plan;
use App\Models\PlanFeature;
use App\Models\Subscription;
use App\Models\SubscriptionHistory;
use App\Models\SubscriptionReceipt;
use App\Services\GcsService;
use Exception;
use Illuminate\Support\Facades\DB;

class SubscriptionHelper
{
    protected static $subscriberType = Company::class;

    /**
     * Get Subscriptions List By Customer ID
     */
    public static function getSubscriptionsList(int $customerId, ?int $subscriptionId = null)
    {
        if (!empty($subscriptionId)) {
            return Subscription::find($subscriptionId);
        }

        return Subscription::where([
            'subscriber_type' => self::$subscriberType,
            'subscriber_id'   => $customerId
        ])->get();
    }

    /**
     * Get Subscription Plans
     */
    public static function getPlanPricePerLineOfCodes(int $codeLines): array
    {
        $plans = Plan::withoutTrashed()
            ->join('plan_features', function ($join) use ($codeLines) {
                $join->on('plans.id', '=', 'plan_features.plan_id')
                    ->whereNull('plan_features.deleted_at')
                    ->where(function ($query) use ($codeLines) {
                        $query->where('valid_from_minimum', '<=', $codeLines)
                            ->where('valid_to_maximum', '>=', $codeLines)
                            ->orWhere(function ($query) {
                                $query->whereNull('plan_features.valid_from_minimum')
                                    ->whereNull('plan_features.valid_to_maximum');
                            });
                    });
            })
            ->join('features', 'plan_features.feature_id', '=', 'features.id')
            ->select('plans.id as plan_id', 'plans.currency as currency', DB::raw('SUM(plan_features.price) as total_price'))
            ->groupBy('plan_id')
            ->get();

        return $plans->toArray();
    }

    /**
     * Get Plan Features & Addons 
     * Will return only features for now & the others by another api call 
     */
    public static function getPlanFeatures(Plan $plan)
    {
        $null = [
            'features' => [],
        ];

        if (empty($plan)) {
            return $null;
        }

        $features = $plan->planFeatures()
            ->where('parent_plan_feature_id', null)
            ->get();

        if (empty($features)) {
            return $null;
        }

        return [
            'features' => PlanFeatureResource::collection(collect($features)
                ->filter(fn($feature) => is_null($feature->valid_from_minimum) && is_null($feature->valid_to_maximum))
                ->values()
                ->all()),
        ];
    }

    /**
     * Get Plan Features & Addons 
     */
    public static function getPlanFeatureAddons(string $planId, string $planFeatureId, ?string $valid_from_min = null, ?string $valid_to_max = null): array
    {
        $null = [
            'line_of_codes' => [],
            'addons' => [],
            'extra_addons' => []
        ];

        if (empty($planFeatureId) || empty($planId)) {
            return $null;
        }

        $planFeatures = PlanFeature::where(['plan_id' => $planId, 'parent_plan_feature_id' => $planFeatureId])
            ->get();

        if (empty($planFeatures)) {
            return $null;
        }

        return [
            'line_of_codes' => PlanFeatureResource::collection(collect($planFeatures)
                ->filter(function ($addon) {
                    if (!$addon->is_addon && !$addon->is_extra_addon) {
                        return $addon->valid_from_minimum != null && $addon->valid_to_maximum != null;
                    }

                    return false;
                })
                ->sortBy('valid_to_maximum') // Order by valid_to_maximum ascending
                ->values()
                ->all()),

            'addons' => PlanFeatureResource::collection(collect($planFeatures)
                ->filter(function ($addon) use ($valid_from_min, $valid_to_max) {
                    if ($addon->is_addon && !$addon->is_extra_addon) {
                        if (!is_null($valid_from_min) && !is_null($valid_to_max)) {
                            return $addon->valid_from_minimum <= $valid_from_min && $addon->valid_to_maximum >= $valid_to_max;
                        }

                        return $addon->valid_from_minimum == null && $addon->valid_to_maximum == null;
                    }
                    return false;
                })
                ->values()
                ->all()),

            'extra_addons' =>  PlanFeatureResource::collection(collect($planFeatures)
                ->filter(function ($addon) use ($valid_from_min, $valid_to_max) {
                    if ($addon->is_addon && $addon->is_extra_addon) {
                        if (!is_null($valid_from_min) && !is_null($valid_to_max)) {
                            return $addon->valid_from_minimum <= $valid_from_min && $addon->valid_to_maximum >= $valid_to_max;
                        }

                        return $addon->valid_from_minimum == null && $addon->valid_to_maximum == null;
                    }
                    return false;
                })
                ->values()
                ->all()),   
        ];
    }

    /**
     * @throws Exception
     */
    public static function uploadReceipts(Subscription &$subscription, array $receipt_files): bool
    {
        $planModel = $subscription->plan;

        if (empty($planModel) || empty($receipt_files)) {
            return false;
        }

        foreach ($receipt_files as $file) {
            $receiptModel = SubscriptionReceipt::create([
                'subscription_id' => $subscription->id,
                'receipt_number' => uniqid(),
                'amount' => $subscription->totalPrice(asArray: false),
                'currency_code' => $subscription?->currency?->iso,
                'receipt_status' => SubscriptionReceipts::RECEIPT_STATUS_PENDING->value,
                'storage' => 'gcs',
            ]);

            $receiptName = $subscription->subscriber->public_company_id  . "_" . $subscription->public_subscription_id . "_" . $receiptModel->subscription_receipts_id;

            $gcsService = app(GcsService::class)->setFolder('receipts')->setFileName($receiptName);

            $receipt_path = $gcsService->upload($file);

            $receiptModel->update([
                'receipt_path' => $receipt_path,
            ]);

            /**
            * Update Subscription Model Status 
            */
            $subscription->update([
                'subscription_status' => Subscriptions::SUBSCRIPTION_STATUS_PENDING_PAYMENT->value,
                'payment_status' => Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_IN_REVIEW->value,
            ]);
        }

        return true;
    }

    /**
     * Payment Status Formatted 
     * Note: Not Used - But Kept For Reference
     */
    public static function paymentStatusFormatted(Subscription | SubscriptionHistory $model): string
    {
        $status = Subscriptions::from($model->payment_status);

        return match ($status) {
            Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PAID =>
            $status->label() . ' on ' . $model->last_payment_date_formatted,

            default => $status->label(),
        };
    }
}
