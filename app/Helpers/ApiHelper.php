<?php

namespace App\Helpers;

use App\Enums\ApiStatus;
use App\Enums\ValidationMessages;
use App\Services\ApiResponseService;

class ApiHelper
{
    public static function validationError(ApiResponseService $service, array $validationErrors)
    {
        return $service->props(ApiStatus::BAD_REQUEST, ValidationMessages::VALIDATION_ERROR->description())
                ->withValidationMessages($validationErrors)
                ->withSystemMessages(ValidationMessages::VALIDATION_ERROR)
                ->send();
    }
}
