<?php

namespace App\Helpers;

use App\Enums\DefaultValues;
use App\Models\BillingVatMapping;
use App\Models\Country;
use App\Models\Currency;
use App\Models\Plan;
use App\Models\PlanFeature;
use App\Models\PlatformCountry;
use App\Models\Subscription;
use App\Observers\SubscriptionObserver;
use App\Services\MoneyFormatter;
use Cknow\Money\Money;

class BillingsHelper
{

    /**
     * Address Types 
     */
    const ADDRESS_TYPE_COMPANY = 'company_address';

    const ADDRESS_TYPE_BILLING = 'billing_address';

    /**
     * Get Platform Billing Country VAT
     */
    public static function getVat(?string $customerBillingCountry = null, ?string $customerPlatformCountry = null): float
    {
        $today = now()->toDateString();

        /**
         * Both Exist 
         */
        if (!empty($customerPlatformCountry) && !empty($customerBillingCountry)) {
            $vatMapping = BillingVatMapping::where([
                ['customer_billing_country_code', '=', $customerBillingCountry],
                ['platform_billing_country_code', '=', $customerPlatformCountry],
                // ['is_default', '=', false],
            ])
                ->where(function ($query) use ($today) {
                    $query->whereNull('from_date')->orWhere('from_date', '<=', $today);
                })
                ->where(function ($query) use ($today) {
                    $query->whereNull('to_date')->orWhere('to_date', '>=', $today);
                })
                ->orderByDesc('billing_vat_mapping_id')
                ->first();

            if ($vatMapping) {
                return (float) $vatMapping->vat;
            }
        }

        /**
         * Billing Country empty 
         */
        if (empty($customerBillingCountry) && !empty($customerPlatformCountry)) {
            $vatMapping = BillingVatMapping::where([
                ['platform_billing_country_code', '=', $customerPlatformCountry],
                ['is_default', '=', true],
            ])
                ->first();

            if ($vatMapping) {
                return (float) $vatMapping->vat;
            }
        }

        /**
         * Platform Country empty 
         */
        if (!empty($customerBillingCountry) && empty($customerPlatformCountry)) {
            $vatMapping = BillingVatMapping::where([
                ['customer_billing_country_code', '=', $customerBillingCountry],
                ['is_default', '=', true],
            ])
                ->first();

            if ($vatMapping) {
                return (float) $vatMapping->vat;
            }
        }

        return DefaultValues::VAT->get();
    }

    /**
     * Currency Conversion From & To USD
     * From ( Plan Currency )
     * To ( Billing Currency )
     */
    public static function currencyConversion(float $price, string $fromISO, string $toISO): float
    {
        $fromISO = strtoupper($fromISO);
        $toISO = strtoupper($toISO);

        if ($fromISO === $toISO) {
            return $price;
        }

        $fromCurrency = Currency::where('iso', $fromISO)->first();
        $toCurrency = Currency::where('iso', $toISO)->first();

        if (!$fromCurrency || !$toCurrency || $fromCurrency->exchange_rate == 0 || $toCurrency->exchange_rate == 0) {
            return $price;
        }

        // Convert to USD then to destination currency
        $priceInUSD = $price / $fromCurrency->exchange_rate;
        $converted = $priceInUSD * $toCurrency->exchange_rate;

        return round($converted, 2); // precision-safe
    }


    /**
     * Get Currency by ISO code
     */
    public static function getCurrencyByISOCode(string $code): string
    {
        $currency = Currency::where('iso', strtoupper($code))->first();

        return $currency?->symbol ?? DefaultValues::CURRENCY->get();
    }

    /**
     * Calculates and formats the estimated price for a plan, addons, and applies VAT and currency conversions.
     */
    public static function calculateEstimatedPrice(
        $planId,
        $billingCountryId,
        $newCurrencyId = null,
        $addons = [],
        $platformCountryId = null,
        $numberOfYears = null,
        $subscriptionId = null
    ): array {

        $plan = Plan::find($planId);
        if (!$plan) {
            return [];
        }

        if ($subscriptionId && $newCurrencyId) {
            $subscriptionModel = Subscription::find($subscriptionId);
            if ($subscriptionModel) {
                SubscriptionObserver::$skipObserver = true;
                $subscriptionModel->update(['currency_id' => $newCurrencyId]);
                SubscriptionObserver::$skipObserver = false;
            }
        }

        $defaultPlanCurrency = $plan->currency(); 
        $priceInCents = PlanFeature::whereIn('plan_feature_id', $addons)->sum('price');

        if ($numberOfYears && $numberOfYears > 0) {
            $priceInCents *= $numberOfYears;
        }

        $price = new Money($priceInCents, $defaultPlanCurrency);
        $discount = new Money(0, $defaultPlanCurrency); 
        $totalDiscounted = $price->subtract($discount);

        $billingCountry = Country::find($billingCountryId);
        $platformCountry = PlatformCountry::find($platformCountryId);

        $vatPercentage = self::getVat($billingCountry?->code, $platformCountry?->code);
        $vatAmount = $totalDiscounted->multiply($vatPercentage);
        $grandTotal = $totalDiscounted->add($vatAmount);

        $newCurrencyISO = $newCurrencyId ? Currency::find($newCurrencyId)?->iso : null;

        if ($newCurrencyISO && strtoupper($newCurrencyISO) !== strtoupper($defaultPlanCurrency)) {
            $convert = function (Money $money, string $targetCurrency) use ($defaultPlanCurrency) {
                $amountInBaseUnit = $money->divide(100)->getAmount();
                $convertedValue = self::currencyConversion($amountInBaseUnit, $defaultPlanCurrency, $targetCurrency);
                return new Money($convertedValue, $targetCurrency);
            };

            $price = $convert($price, $newCurrencyISO);
            $discount = $convert($discount, $newCurrencyISO);
            $totalDiscounted = $convert($totalDiscounted, $newCurrencyISO);
            $vatAmount = $convert($vatAmount, $newCurrencyISO);
            $grandTotal = $convert($grandTotal, $newCurrencyISO);
        }

        return [
            "price" => $price,
            "discount" => $discount,
            "total_discounted" => $totalDiscounted,
            "vat_percentage" => $vatPercentage,
            "vat_amount" => $vatAmount,
            "grand_total" => $grandTotal,
        ];
    }
}
