<?php

namespace App\Helpers;

use App\Enums\DefaultValues;
use BackedEnum;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class AppHelper
{
    /**
     * Strings 
     */
    public static function matchStrings(string|BackedEnum|null $str_1 = null, string|BackedEnum|null $str_2 = null): bool
    {
        if ($str_1 instanceof BackedEnum) {
            $str_1 = $str_1->value;
        }

        if ($str_2 instanceof BackedEnum) {
            $str_2 = $str_2->value;
        }

        if (empty($str_1) || empty($str_2)) {
            return false;
        }

        return strtolower(trim($str_1)) === strtolower(trim($str_2));
    }

    public static function refactorString(string $string, string $prefix, int $length, string $paddingString, string $direction = STR_PAD_LEFT): string
    {
        return $prefix . str_pad($string, $length, $paddingString, $direction);
    }

    public static function getFormattedId(string $id, string $prefix)
    {
        return !empty($id) ? self::refactorString($id, $prefix, 4, '0') : DefaultValues::STRING->get();
    }

    public static function normalizeSlug(string $slug): string
    {
        return preg_replace('/(-\d+)+$/', '', $slug);
    }

    public static function normalizeDomain(string $domain): string
    {
        // Remove digits and any trailing hyphen left by digit removal
        $domain = preg_replace('/\d+/', '', $domain);
        $domain = preg_replace('/-+(\.)/', '$1', $domain); // Remove hyphen(s) before a dot
        $domain = preg_replace('/-+$/', '', $domain); // Remove trailing hyphens
        return $domain;
    }

    public static function normalizeStatus(?string $status = null, ?string $seperator = "_")
    {
        if (empty($status)) return null;

        return collect(explode($seperator, $status))
            ->map(fn($word) => ucfirst($word))
            ->join(' ');
    }


    /**
     * Decrypts an encrypted string using Laravel's Crypt facade. 
     */
    public static function decryptString(string|null $encryptedString): string
    {
        if (empty($encryptedString)) {
            return '';
        }

        try {
            return Crypt::decryptString($encryptedString);
        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * Stubs 
     */

    /**
     * Processes a stub file, replaces placeholders, and saves the output.
     */
    public static function processStub(string $stubPath, string $outputPath, array $replacements, string $contextLogIdentifier): void
    {
        Log::info("[{$contextLogIdentifier}] Processing stub: {$stubPath} to {$outputPath}");

        if (!File::exists($stubPath)) {
            Log::error("[{$contextLogIdentifier}] stub file not found: {$stubPath}");
            throw new \RuntimeException("stub file not found: {$stubPath}");
        }

        $stubContent = File::get($stubPath);
        $processedContent = str_replace(array_keys($replacements), array_values($replacements), $stubContent);

        $outputDir = dirname($outputPath);
        File::ensureDirectoryExists($outputDir); // Ensure the directory for generated YAML exists

        File::put($outputPath, $processedContent);
        Log::info("[{$contextLogIdentifier}] Generated file from stub: {$outputPath}");
    }

    /**
     * Return config value based on the environment.
     */
    public static function getConfigValue(string $key, string $default = ""): mixed
    {
        $env = config('app.env');
        $configKey = "{$key}.{$env}";

        return config($configKey, $default);
    }
}
