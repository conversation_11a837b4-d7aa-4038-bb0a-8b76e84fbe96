<?php

namespace App\Http\Requests;

use App\Helpers\ApiHelper;
use App\Models\User;
use App\Rules\CompanyNameRule;
use App\Services\ApiResponseService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateCustomerRequest extends FormRequest
{
    use \App\Traits\Blamable;

    /**
    * User Object 
    */
    protected User $user;

    /**
    * construct function 
    */
    public function __construct() {
        $this->user = self::getBlamable();
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user->hasPermissionTo('update::customer-profile', 'api');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'        => ['string', new CompanyNameRule()],
            'countries'   => ['required', 'array'],
            'countries.*' => ['integer', 'exists:countries,id'],
            'sectors'     => ['required', 'array'],
            'sectors.*'   => ['integer', 'exists:sectors,sector_id'],
            'industries'  => ['required', 'array'],
            'industries.*' => ['integer', 'exists:industries,industry_id'],
            'website'     => ['required', 'string', 'url'],
            'number_of_employees' => ['required', 'integer', 'min:1'],
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $response = ApiHelper::validationError(
            app(ApiResponseService::class),
            $validator->errors()->toArray()
        );

        throw new HttpResponseException($response);
    }

}
