<?php

namespace App\Http\Requests;

use App\Enums\ValidationMessages;
use App\Rules\BusinessEmailRule;
use App\Rules\PhoneNumberRule;
use Illuminate\Foundation\Http\FormRequest;

class CreateUpdateCompanyUserRequest extends FormRequest
{
    use \App\Traits\Blamable;

    /**
     * User Object 
     */
    protected \App\Models\User $user;

    /**
     * construct function 
     */
    public function __construct()
    {
        $this->user = self::getBlamable();
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // create
        // if ($this->isMethod('post')) {
        //     return $this->user()->can('create_', 'api');
        // }

        // update
        // if ($this->isMethod('put') || $this->isMethod('patch')) {
        //     return $this->user()->can('update_', 'api');
        // }

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                'unique:users',
                app(BusinessEmailRule::class)
            ],
            'name' =>  ['required', 'string'],
            'phone' => [
                'required',
                'string',
                app(PhoneNumberRule::class)
            ],
            'role'  => ['required', 'string']
        ];

        // Create 
        if ($this->isMethod('post')) {
            //$rules['name'] = ['required', 'string'];
        }

        // update
        if ($this->isMethod('put') || $this->isMethod('patch')) {
            //$rules['name'] = ['nullable', 'string'];
        }

        return $rules;
    }

    /**
     * Customize the error messages.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'email.unique' => ValidationMessages::EMAIL_UNIQUE->description(),
        ];
    }

    /**
     * Handle failed validation.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        $response = \App\Helpers\ApiHelper::validationError(
            app(\App\Services\ApiResponseService::class),
            $validator->errors()->toArray()
        );

        throw new \Illuminate\Http\Exceptions\HttpResponseException($response);
    }
}
