<?php

namespace App\Http\Controllers\Notifications\Api;

use App\Enums\ApiStatus;
use App\Helpers\ApiHelper;
use App\Http\Controllers\Controller;
use App\Services\ApiResponseService;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class NotificationsController extends Controller
{
    public function __construct(protected NotificationService $notificationService, protected ApiResponseService $apiResponseService) {}

    public function index(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'per_page' => 'sometimes|integer|min:1|max:100',
        ]);

        if ($validator->fails()) {
            return ApiHelper::validationError($this->apiResponseService, $validator->errors()->toArray());
        }

        $perPage = $validator->validated()['per_page'] ?? 15;

        $data = $this->notificationService->getNotificationsForUser(
            Auth::user(),
            $perPage
        );

        return $this->apiResponseService->props(ApiStatus::SUCCESS)
            ->withData($data)
            ->send();
    }

    public function markAsRead(int $notification_id)
    {
        $this->notificationService->markNotificationAsRead(Auth::user(), $notification_id);
        return $this->apiResponseService->props(ApiStatus::SUCCESS, 'Notification marked as read.')
            ->send();
    }

    public function markAllAsRead()
    {
        $count = $this->notificationService->markAllNotificationsAsRead(Auth::user());
        return $this->apiResponseService->props(ApiStatus::SUCCESS, "{$count} notifications marked as read.")
            ->send();
    }

    public function getSettings()
    {
        return $this->apiResponseService->props(ApiStatus::SUCCESS)
            ->withData($this->notificationService->getNotificationSettings(Auth::user()))
            ->send();
    }

    public function updateSettings(Request $request)
    {
        $validated = $request->validate([
            'settings' => 'required|array',
            'settings.*.type_id' => 'required|integer|exists:notification_types,notification_type_id',
            'settings.*.is_enabled' => 'required|boolean',
        ]);
        
        $this->notificationService->updateNotificationSettings(Auth::user(), $validated['settings']);
        return $this->apiResponseService->props(ApiStatus::SUCCESS, "Settings updated successfully.")
            ->send();
    }
}
