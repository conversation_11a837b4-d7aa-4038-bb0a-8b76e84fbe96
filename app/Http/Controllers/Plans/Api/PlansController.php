<?php

namespace App\Http\Controllers\Plans\Api;

use App\Enums\ApiStatus;
use App\Helpers\ApiHelper;
use App\Helpers\CustomerHelper;
use App\Helpers\SubscriptionHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\PlanResource;
use App\Models\Plan;
use App\Services\ApiResponseService;
use App\Traits\Blamable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PlansController extends Controller
{
    use Blamable;

    protected $apiResponse;

    public function __construct(ApiResponseService $apiResponse)
    {
        $this->apiResponse = $apiResponse;
    }

    /**
    * List Available Plans 
    */
    public function list(Request $request)
    {

        $user = CustomerHelper::getCompanyUsers($this->apiResponse, self::getBlamableId());

        if ($user instanceof \Illuminate\Http\JsonResponse) {
            return $user;
        }

        return $this->apiResponse->props(ApiStatus::SUCCESS)
        ->withData(PlanResource::collection(Plan::enabled()->orderBy('id', 'asc')->get())->toArray($request))
        ->send();
    }

    /**
    * Get Plan Feature Addons By Plan & Feature Id 
    */
    public function getPlanFeatureAddonsById(Request $request)
    {

        $user = CustomerHelper::getCompanyUsers($this->apiResponse, self::getBlamableId());

        if ($user instanceof \Illuminate\Http\JsonResponse) {
            return $user;
        }

        $validator = Validator::make($request->all(), [
            'plan_id' => 'integer|required|exists:plans,id',
            'feature_id' => 'integer|required|exists:plan_features,plan_feature_id',
            'valild_from_min' => 'integer',
            'valild_to_max' => 'integer',
        ]);

        if ($validator->fails()) {
            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $planFeatureAddons = SubscriptionHelper::getPlanFeatureAddons($request->plan_id, $request->feature_id, $request->valid_from_min, $request->valid_to_max);

        return $this->apiResponse->props(ApiStatus::SUCCESS)
        ->withData($planFeatureAddons)
        ->send();
    }
}
