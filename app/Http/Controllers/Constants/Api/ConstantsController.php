<?php

namespace App\Http\Controllers\Constants\Api;

use App\Enums\ApiStatus;
use App\Enums\Subscriptions;
use App\Helpers\ApiHelper;
use App\Helpers\CustomerHelper;
use App\Helpers\RbacHelper;
use App\Helpers\StatusHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\CityResource;
use App\Http\Resources\CompanyUserResource;
use App\Http\Resources\CountryResource;
use App\Http\Resources\CurrencyResource;
use App\Http\Resources\IndustryResource;
use App\Http\Resources\PlatformBankResource;
use App\Http\Resources\PlatformCountryResource;
use App\Http\Resources\RoleResource;
use App\Http\Resources\SectorResource;
use App\Http\Resources\StatusResource;
use App\Models\City;
use App\Models\Country;
use App\Models\Currency;
use App\Models\Industry;
use App\Models\PlatformBanks;
use App\Models\PlatformCountry;
use App\Models\Sector;
use App\Models\Status;
use App\Services\ApiResponseService;
use App\Traits\Locations;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ConstantsController extends Controller
{
    protected $apiResponse;

    public function __construct(ApiResponseService $apiResponse)
    {
        $this->apiResponse = $apiResponse;
    }

    public function list(Request $request)
    {
        $out = [
            'product' => config('settings.default_product_name'),
            'statuses' => StatusResource::collection(StatusHelper::getUserStatuses()),
            'industries' => IndustryResource::collection(Industry::all()),
            'sectors' => SectorResource::collection(Sector::all()),
            'roles' => RoleResource::collection(RbacHelper::getRolesByType(RbacHelper::TYPE_CUSTOMER, RbacHelper::GUARD_API)),
            'platform_countries' => PlatformCountryResource::collection(PlatformCountry::all()),
            'subscriptions_steps' => collect(array_keys(Subscriptions::getSubscriptionProgressSteps()))->values()->all(),
        ];

        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->withData($out)
            ->send();
    }

    public function countries(Request $request)
    {

        $out = [
            'countries' => CountryResource::collection(Country::sorted()->get()),
        ];

        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->withData($out)
            ->send();
    }

    public function currencies(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'country_id' => 'sometimes|nullable|integer|exists:countries,id',
        ]);

        if ($validator->fails()) {
            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $currenciesCollection = collect(); 

        if (empty($request->country_id)) {
            $preferredCurrenciesIso = ['USD', 'EUR', 'SAR', 'QAR', 'AED'];
            $currenciesCollection = Currency::query()
                ->whereIn('iso', $preferredCurrenciesIso)
                ->get()
                ->sortBy(function ($currency) use ($preferredCurrenciesIso) {
                    return array_search($currency->iso, $preferredCurrenciesIso);
                });
        } else {
            $countryModel = Country::find($request->country_id);
            $targetIsos = []; 

            if ($countryModel && !empty($countryModel->currency)) {
                $countryCurrencyIso = $countryModel->currency;

                switch ($countryCurrencyIso) {
                    case 'QAR': 
                        $targetIsos = ['QAR', 'USD'];
                        break;
                    case 'AED': 
                        $targetIsos = ['AED', 'USD'];
                        break;
                    case 'SAR': 
                        $targetIsos = ['SAR', 'USD'];
                        break;
                    default:
                        $targetIsos = ['USD'];
                        break;
                }
            } else {
                $targetIsos = ['USD'];
            }

            if (!empty($targetIsos)) {
                $currenciesCollection = Currency::query()
                    ->whereIn('iso', $targetIsos)
                    ->get();

                if ($currenciesCollection->isNotEmpty()) {
                    $currenciesCollection = $currenciesCollection->sortBy(function ($currency) use ($targetIsos) {
                        $order = array_search($currency->iso, $targetIsos);
                        return $order === false ? count($targetIsos) : $order;
                    });
                }
            }

        }

        $out = [
            'currencies' => CurrencyResource::collection($currenciesCollection->values()), 
        ];

        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->withData($out)
            ->send();
    }

    public function cities(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'country_id' => 'integer|exists:countries,id',
        ]);

        if ($validator->fails()) {
            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $cities = City::query()
            ->when($request->filled('country_id'), fn($q) => $q->where('country_id', $request->country_id))
            ->where('lang', 'en')
            ->get();

        $out = [
            'cities' => CityResource::collection($cities)
        ];

        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->withData($out)
            ->send();
    }

    public function banks(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'billing_country_id' => 'integer|exists:countries,id',
        ]);

        if ($validator->fails()) {
            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $platformBanks = PlatformBanks::query()
            ->when($request->filled('billing_country_id'), fn($q) => $q->where('country_code', Country::find($request->billing_country_id)->code))
            ->get();

        $out = [
            'platform_banks' => PlatformBankResource::collection($platformBanks)
        ];

        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->withData($out)
            ->send();
    }
}
