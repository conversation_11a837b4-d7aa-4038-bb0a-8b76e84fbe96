<?php

namespace App\Http\Controllers\Auth\Api;

use App\Enums\ApiSettings;
use App\Enums\ApiStatus;
use App\Enums\AuthEvents;
use App\Enums\ValidationMessages;
use App\Helpers\ApiHelper;
use App\Helpers\AppHelper;
use App\Helpers\StatusHelper;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use App\Mail\OtpVerificationMail;
use App\Models\Company;
use App\Models\Status;
use App\Services\ApiResponseService;
use App\Services\LogService;
use App\Services\OtpService;

class OtpController extends Controller
{
    protected $apiResponse;

    protected $logService;

    protected $otpService;

    public function __construct(ApiResponseService $apiResponse, LogService $logService, OtpService $otpService)
    {
        $this->apiResponse = $apiResponse;
        $this->logService = $logService;
        $this->otpService = $otpService;
    }

    public function renew(Request $request)
    {
        $logService = $this->logService->withRequest($request);

        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email|max:255',
            'action' => 'required|string',
        ]);

        if ($validator->fails()) {
            $logService
                ->props(AuthEvents::OTP_RENEW, AuthEvents::OTP_RENEW->description(),  ValidationMessages::VALIDATION_ERROR->description(), $validator->errors()->toArray())
                ->withFailure()
                ->log();

            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $user = User::where('email', $request->email)->first();

        if (empty($user)) {
            $logService
                ->props(AuthEvents::OTP_RENEW, AuthEvents::OTP_RENEW->description(),  ValidationMessages::USER_NOT_FOUND->description())
                ->withFailure()
                ->log();

            return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::USER_NOT_FOUND->description())
                ->send();
        }

        $otp = $this->otpService->withUser($user, ApiSettings::OTP_EXPIRY_MINUTES->getValue())->generate(ApiSettings::OTP_DEFAULT_RANGE->getValue());

        Mail::to($request->email)->queue(new OtpVerificationMail($otp, $user, $request->action));

        $this->logService
            ->withRequest($request)
            ->props(AuthEvents::OTP_RENEW, AuthEvents::OTP_RENEW->description())
            ->log();

        return $this->apiResponse->props(ApiStatus::SUCCESS, ValidationMessages::OTP_RENEWED->description())
            ->send();
    }

    public function verify(Request $request)
    {
        $logService = $this->logService->withRequest($request);

        $validator = Validator::make($request->all(), [
            'otp' => 'required|string|max:6',
            'email' => 'required|string|email|max:255',
        ]);

        if ($validator->fails()) {
            $logService
                ->props(AuthEvents::OTP_VALIDATE, AuthEvents::OTP_VALIDATE->description(),  ValidationMessages::VALIDATION_ERROR->description())
                ->withFailure()
                ->log();

            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $user = User::where('email', $request->email)->first();

        // user not found
        if (empty($user)) {
            $logService
                ->props(AuthEvents::OTP_VALIDATE, AuthEvents::OTP_VALIDATE->description(),  ValidationMessages::USER_NOT_FOUND->description())
                ->withFailure()
                ->log();

            return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::USER_NOT_FOUND->description())
                ->send();
        }

        $otpService = $this->otpService->withUser($user);

        // locked
        if ($otpService->isLocked()) {
            $logService
                ->props(AuthEvents::OTP_VALIDATE, AuthEvents::OTP_VALIDATE->description(),  ValidationMessages::OTP_LOCKED->description())
                ->withFailure()
                ->log();

            return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::INVALID_OTP->description())
                ->withSystemMessages(ValidationMessages::OTP_LOCKED)
                ->send();
        }

        // invalid
        if (!$otpService->isValid($request->otp)) {
            if (!empty($user->otp_attempts) && $user->otp_attempts >= ApiSettings::OTP_LOCKING_FAILED_ATTEMPTS->getValue()) {
                $otpService->lock(ApiSettings::OTP_LOCKING_MINUTES->getValue());

                $logService
                    ->props(AuthEvents::OTP_VALIDATE, AuthEvents::OTP_VALIDATE->description(),  ValidationMessages::OTP_LOCKED->description())
                    ->withFailure()
                    ->log();

                return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::INVALID_OTP->description())
                    ->withSystemMessages(ValidationMessages::OTP_LOCKED)
                    ->send();
            }

            $logService
                ->props(AuthEvents::OTP_VALIDATE, AuthEvents::OTP_VALIDATE->description(),  ValidationMessages::INVALID_OTP->description())
                ->withFailure()
                ->log();

            return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::INVALID_OTP->description())
                ->send();
        }

        // expired
        if ($user->otp_expires_at->isPast()) {
            $logService
                ->props(AuthEvents::OTP_VALIDATE, AuthEvents::OTP_VALIDATE->description(),  ValidationMessages::EXPIRED_OTP->description())
                ->withFailure()
                ->log();

            return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::EXPIRED_OTP->description())
                ->send();
        }

        $otpService->reset();

        if (empty($user->email_verified_at)) {
            $user->email_verified_at = now();
            $user->save();
        }

        $logService
            ->props(AuthEvents::OTP_VALIDATE, AuthEvents::OTP_VALIDATE->description())
            ->log();

        return $this->apiResponse->props(ApiStatus::SUCCESS, ValidationMessages::OTP_VERIFIED->description())
            ->send();
    }
}
