<?php

namespace App\Http\Controllers\Auth\Api;

use App\Enums\ApiStatus;
use App\Enums\AuthEvents;
use App\Enums\ValidationMessages;
use App\Http\Controllers\Controller;
use App\Services\ApiResponseService;
use App\Services\LogService;
use App\Traits\Blamable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Tymon\JWTAuth\Facades\JWTAuth;

class LogoutController extends Controller
{
    use Blamable;

    protected $apiResponse;

    protected $logService;

    public function __construct(ApiResponseService $apiResponse, LogService $logService)
    {
        $this->apiResponse = $apiResponse;
        $this->logService = $logService;
    }

    public function logout(Request $request)
    {
        $this->logService
        ->withRequest($request)
        ->props(AuthEvents::LOGOUT, AuthEvents::LOGOUT->description())
        ->log();
        
        Auth::logout();
        JWTAuth::invalidate(JWTAuth::getToken());

        return $this->apiResponse->props(ApiStatus::SUCCESS)
        ->send();
    }
}
