<?php

namespace App\Http\Controllers\Auth\Api;

use App\Enums\ApiSettings;
use App\Enums\ApiStatus;
use App\Enums\AuthEvents;
use App\Enums\ValidationMessages;
use App\Helpers\ApiHelper;
use App\Helpers\CustomerHelper;
use App\Helpers\RbacHelper;
use App\Helpers\StatusHelper;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use App\Mail\OtpVerificationMail;
use App\Models\Company;
use App\Models\Log;
use App\Services\ApiResponseService;
use App\Services\LogService;
use App\Services\NotificationService;
use App\Services\OtpService;
use App\Services\RbacService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class RegistrationController extends Controller
{
    protected $apiResponse;

    protected $logService;

    protected $notificationService;

    protected $otpService;

    protected $rbacService;

    public function __construct(ApiResponseService $apiResponse, LogService $logService, NotificationService $notificationService, OtpService $otpService, RbacService $rbacService)
    {
        $this->apiResponse = $apiResponse;
        $this->logService = $logService;
        $this->notificationService = $notificationService;
        $this->otpService = $otpService;
        $this->rbacService = $rbacService;
    }

    public function register_(Request $request)
    {
        $ip = $request->ip();

        // check for blocked ips
        if (Cache::has("blocked:$ip")) {
            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::REGISTER, AuthEvents::REGISTER->description(),  ValidationMessages::MANY_FAILED_REGISTRATION_ATTEMPTS->description())
                ->withFailure()
                ->log();

            return $this->apiResponse->props(ApiStatus::TOO_MANY_REQUESTS, ValidationMessages::MANY_FAILED_REGISTRATION_ATTEMPTS->description())
                ->send();
        }

        $failedAttempts = Log::where([
            ['ip_address', '=', $ip],
            ['status', '=', 'failure'],
            ['event', '=', 'register'],
            ['created_at', '>=', Carbon::now()->subMinutes(10)]
        ])->count();

        /**
         * BLOCKED IP 
         */
        if ($failedAttempts > ApiSettings::USER_IP_LOCKING_FAILED_ATTEMPTS->getValue()) {
            Cache::put("blocked:$ip", true, now()->addMinutes(ApiSettings::USER_IP_LOCKING_MINUTES->getValue()));

            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::REGISTER, AuthEvents::REGISTER->description(),  ValidationMessages::MANY_FAILED_REGISTRATION_ATTEMPTS->description())
                ->withFailure()
                ->log();

            return $this->apiResponse->props(ApiStatus::TOO_MANY_REQUESTS, ValidationMessages::MANY_FAILED_REGISTRATION_ATTEMPTS->description())
                ->send();
        }

        $user = User::where('email', $request->email)->first();
        $userExist = !empty($user) && !empty($user->email_verified_at);
        $shouldCompleteRegistration = !empty($user) && empty($user->email_verified_at);
        $havingPendingInvitation = !empty($user) && !empty($user->invitation_token) && !$user->invitation_expires_at->isPast();

        // Having Pending Invitation
        if ($havingPendingInvitation) {
            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::REGISTER, AuthEvents::REGISTER->description(),  ValidationMessages::USER_HAS_PENDING_INVITATION->description())
                ->withFailure()
                ->log();

            return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::USER_HAS_PENDING_INVITATION->description())
                ->send();
        }

        // request validation
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|min:3|max:20|regex:/^[a-zA-Z\s]+$/',
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                'regex:/^[a-zA-Z0-9._%+-]+@(?!(gmail\.com|yahoo\.com|hotmail\.com|outlook\.com|aol\.com|icloud\.com|live\.com|protonmail\.com|mail\.com|zoho\.com)$)[a-zA-Z][a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/',
                function ($attribute, $value, $fail) use ($userExist) {
                    if ($userExist) {
                        $fail(ValidationMessages::EMAIL_UNIQUE->description());
                    }
                }
                // 'unique:users',
            ],
            'terms_accepted' => 'required|int'
        ], [
            'name.regex' => ValidationMessages::NAME_FORMAT->description(),
            'email.regex' => ValidationMessages::EMAIL_FORMAT->description(),
            'email.unique' => ValidationMessages::EMAIL_UNIQUE->description(),
            'terms_accepted' => ValidationMessages::TERMS_ACCEPTED->description()
        ]);

        if ($validator->fails()) {
            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::REGISTER, AuthEvents::REGISTER->description(),  ValidationMessages::VALIDATION_ERROR->description(), $validator->errors()->toArray())
                ->withFailure()
                ->log();

            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $incompleteCustomerStatusId = StatusHelper::getStatusByAttributeName('name', 'incomplete');
        $incompleteUserStatusId = StatusHelper::getStatusByAttributeName('name', 'incomplete', StatusHelper::STATUS_TYPE_USER);

        if (empty($user)) {
            $company = Company::create([
                'status_id' => $incompleteCustomerStatusId,
                'registration_type' => CustomerHelper::CUSTOMER_REGISTRATION_TYPE_SELF_REGISTERED
            ]);

            $user = User::create([
                'company_id' => $company->company_id,
                'status_id'  => $incompleteUserStatusId,
                'email' => $request->email,
                'name' => $request->name,
                'accepted' => $request->terms_accepted,
                'is_primary_customer' => true
            ]);

            $this->rbacService->withUser($user)->withRole(RbacHelper::ROLE_PRIMARY)->assign();
        }

        $otp = $this->otpService->withUser($user, ApiSettings::OTP_EXPIRY_MINUTES->getValue())->generate(ApiSettings::OTP_DEFAULT_RANGE->getValue());

        Mail::to($request->email)->queue(new OtpVerificationMail($otp, $user));

        // Create notification for OTP sent
        $this->notificationService->createNotification(
            $user,
            'account_otp_sent',
            'otp_verification_code',
            [
                'user_name' => $user->name,
                'otp_code' => $otp,
                'action' => 'registration',
                'expiry_minutes' => ApiSettings::OTP_EXPIRY_MINUTES->getValue()
            ]
        );

        $this->logService
            ->withRequest($request)
            ->props(AuthEvents::REGISTER, AuthEvents::REGISTER->description())
            ->log();

        if ($shouldCompleteRegistration) {
            return $this->apiResponse->props(ApiStatus::SUCCESS, ValidationMessages::COMPLETE_REGISTRATION->description())
                ->send();
        }

        return $this->apiResponse->props(ApiStatus::SUCCESS, ValidationMessages::REGISTERED_SUCCESSFULLY->description())
            ->withData([
                'user_id' => $user->user_id,
            ])
            ->send();
    }

    public function register(Request $request)
    {
        $ip = $request->ip();

        // check for blocked ips
        if (Cache::has("blocked:$ip")) {
            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::REGISTER, AuthEvents::REGISTER->description(),  ValidationMessages::MANY_FAILED_REGISTRATION_ATTEMPTS->description())
                ->withFailure()
                ->log();

            return $this->apiResponse->props(ApiStatus::TOO_MANY_REQUESTS, ValidationMessages::MANY_FAILED_REGISTRATION_ATTEMPTS->description())
                ->send();
        }

        $failedAttempts = Log::where([
            ['ip_address', '=', $ip],
            ['status', '=', 'failure'],
            ['event', '=', 'register'],
            ['created_at', '>=', Carbon::now()->subMinutes(10)]
        ])->count();

        /**
        * BLOCKED IP
        */
        if ($failedAttempts > ApiSettings::USER_IP_LOCKING_FAILED_ATTEMPTS->getValue()) {
            Cache::put("blocked:$ip", true, now()->addMinutes(ApiSettings::USER_IP_LOCKING_MINUTES->getValue()));

            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::REGISTER, AuthEvents::REGISTER->description(),  ValidationMessages::MANY_FAILED_REGISTRATION_ATTEMPTS->description())
                ->withFailure()
                ->log();

            return $this->apiResponse->props(ApiStatus::TOO_MANY_REQUESTS, ValidationMessages::MANY_FAILED_REGISTRATION_ATTEMPTS->description())
                ->send();
        }

        $userWithNewEmail = User::where('email', $request->email)->first();
        $userExist = !empty($userWithNewEmail) && !empty($userWithNewEmail->email_verified_at);

        // All validation rules are now grouped here.
        $validator = Validator::make($request->all(), [
            'user_id' => 'sometimes|integer|exists:users,user_id',
            'name' => 'required|string|min:3|max:20|regex:/^[a-zA-Z\s]+$/',
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                'regex:/^[a-zA-Z0-9._%+-]+@(?!(gmail\.com|yahoo\.com|hotmail\.com|outlook\.com|aol\.com|icloud\.com|live\.com|protonmail\.com|mail\.com|zoho\.com)$)[a-zA-Z][a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/',
                function ($attribute, $value, $fail) use ($userExist) {
                    if ($userExist) {
                        $fail(ValidationMessages::EMAIL_UNIQUE->description());
                    }
                }
            ],
            'terms_accepted' => 'required|int'
        ], [
            'name.regex' => ValidationMessages::NAME_FORMAT->description(),
            'email.regex' => ValidationMessages::EMAIL_FORMAT->description(),
            'email.unique' => ValidationMessages::EMAIL_UNIQUE->description(),
            'terms_accepted' => ValidationMessages::TERMS_ACCEPTED->description()
        ]);
        
        // If validation fails, we stop immediately, before any users are soft-deleted.
        if ($validator->fails()) {
            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::REGISTER, AuthEvents::REGISTER->description(),  ValidationMessages::VALIDATION_ERROR->description(), $validator->errors()->toArray())
                ->withFailure()
                ->log();

            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }
        
        $companyIdToReuse = null;
        if ($request->filled('user_id')) {
            $userToSoftDelete = User::find($request->user_id);
            if ($userToSoftDelete) {
                $companyIdToReuse = $userToSoftDelete->company_id;
                $userToSoftDelete->delete();
            }
        }
        
        $user = User::where('email', $request->email)->first();
        $shouldCompleteRegistration = !empty($user) && empty($user->email_verified_at);
        $havingPendingInvitation = !empty($user) && !empty($user->invitation_token) && !$user->invitation_expires_at->isPast();

        if ($havingPendingInvitation) {
            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::REGISTER, AuthEvents::REGISTER->description(),  ValidationMessages::USER_HAS_PENDING_INVITATION->description())
                ->withFailure()
                ->log();

            return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::USER_HAS_PENDING_INVITATION->description())
                ->send();
        }

        $incompleteCustomerStatusId = StatusHelper::getStatusByAttributeName('name', 'incomplete');
        $incompleteUserStatusId = StatusHelper::getStatusByAttributeName('name', 'incomplete', StatusHelper::STATUS_TYPE_USER);

        if (empty($user)) {
            if (is_null($companyIdToReuse)) {
                $company = Company::create([
                    'status_id' => $incompleteCustomerStatusId,
                    'registration_type' => CustomerHelper::CUSTOMER_REGISTRATION_TYPE_SELF_REGISTERED
                ]);
                $companyIdToReuse = $company->company_id;
            }

            $user = User::create([
                'company_id' => $companyIdToReuse,
                'status_id'  => $incompleteUserStatusId,
                'email' => $request->email,
                'name' => $request->name,
                'accepted' => $request->terms_accepted,
                'is_primary_customer' => true
            ]);

            $this->rbacService->withUser($user)->withRole(RbacHelper::ROLE_PRIMARY)->assign();
        }

        $otp = $this->otpService->withUser($user, ApiSettings::OTP_EXPIRY_MINUTES->getValue())->generate(ApiSettings::OTP_DEFAULT_RANGE->getValue());

        Mail::to($request->email)->queue(new OtpVerificationMail($otp, $user));

        // Create notification for OTP sent
        $this->notificationService->createNotification(
            $user,
            'account_otp_sent',
            'otp_verification_code',
            [
                'user_name' => $user->name,
                'otp_code' => $otp,
                'action' => 'registration',
                'expiry_minutes' => ApiSettings::OTP_EXPIRY_MINUTES->getValue()
            ]
        );

        $this->logService
            ->withRequest($request)
            ->props(AuthEvents::REGISTER, AuthEvents::REGISTER->description())
            ->log();

        if ($shouldCompleteRegistration) {
            return $this->apiResponse->props(ApiStatus::SUCCESS, ValidationMessages::COMPLETE_REGISTRATION->description())
                ->send();
        }

        return $this->apiResponse->props(ApiStatus::SUCCESS, ValidationMessages::REGISTERED_SUCCESSFULLY->description())
            ->withData([
                'user_id' => $user->user_id,
            ])
            ->send();
    }
}
