<?php

namespace App\Http\Controllers\Auth\Api;

use App\Enums\ApiSettings;
use App\Enums\ApiStatus;
use App\Enums\AuthEvents;
use App\Enums\ValidationMessages;
use App\Helpers\ApiHelper;
use App\Helpers\AppHelper;
use App\Helpers\StatusHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\CompanyUserResource;
use App\Http\Resources\UserResource;
use App\Mail\AccountLockedMail;
use App\Models\User;
use App\Services\ApiResponseService;
use App\Services\LogService;
use App\Traits\Blamable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Tymon\JWTAuth\Facades\JWTAuth;

class LoginController extends Controller
{
    use Blamable;

    protected $apiResponse;

    protected $logService;

    public function __construct(ApiResponseService $apiResponse, LogService $logService)
    {
        $this->apiResponse = $apiResponse;
        $this->logService = $logService;
    }

    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email|max:255',
            'password' => [
                'required',
                'string',
            ],
            'remember_me' => 'required|boolean'
        ]);

        if ($validator->fails()) {
            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::LOGIN, AuthEvents::LOGIN->description(),  ValidationMessages::VALIDATION_ERROR->description(), $validator->errors()->toArray())
                ->withFailure()
                ->log();

            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $user = User::withTrashed()->where('email', $request->email)->first();

        // user not found
        if (empty($user)) {
            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::LOGIN, AuthEvents::LOGIN->description(), ValidationMessages::USER_NOT_FOUND->description())
                ->withFailure()
                ->log();

            return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::EMAIL_NOT_REGISTERED->description())
                ->send();
        }

        // account suspended | deleted
        $userStatus = $user->status;
        $company = $user->company;
        $userProfileCompleted = $user->isProfileCompleted();

        if (!empty($userStatus)) {
            if (strtolower($user->status->name) == 'suspended') {
                $this->logService
                    ->withRequest($request)
                    ->props(AuthEvents::LOGIN, AuthEvents::LOGIN->description(), ValidationMessages::ACCOUNT_SUSPENDED->description())
                    ->withFailure()
                    ->log();

                return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::ACCOUNT_SUSPENDED->description())
                    ->send();
            }

            if (strtolower($user->status->name) == 'terminated' || !empty($user->deleted_at)) {
                $this->logService
                    ->withRequest($request)
                    ->props(AuthEvents::LOGIN, AuthEvents::LOGIN->description(), ValidationMessages::ACCOUNT_DELETED->description())
                    ->withFailure()
                    ->log();

                return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::ACCOUNT_DELETED->description())
                    ->send();
            }
        }

        // email is not verified
        if (empty($user->email_verified_at)) {
            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::LOGIN, AuthEvents::LOGIN->description(), ValidationMessages::UNVEIFIED_EMAIL->description())
                ->withFailure()
                ->log();

            return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::UNVEIFIED_EMAIL->description())
                ->send();
        }

        // account locked
        if ($user->locked && $user->locked_until && !$user->locked_until->isPast()) {
            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::LOGIN, AuthEvents::LOGIN->description(), ValidationMessages::ACCOUNT_LOCKED->description())
                ->withFailure()
                ->log();

            return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::ACCOUNT_LOCKED->description())
                ->send();
        }

        // should we unlock the user
        if ($user->locked && $user->locked_until && $user->locked_until->isPast()) {
            $user->locked = 0;
            $user->login_attempts = 0;
            $user->locked_until = null;
            $user->saveQuietly();

            $this->logService
                ->withRequest($request)
                ->props(ValidationMessages::ACCOUNT_UNLOCKED, ValidationMessages::ACCOUNT_UNLOCKED->description())
                ->withWarning()
                ->log();
        }

        // succesful login attempt
        if (Auth::attempt(['email' => $request->email, 'password' => $request->password], $request->remember_me)) {
            $user = self::getBlamable();
            $user->accepted = true;
            $user->last_activity_at = now();
            $user->locked = 0;
            $user->locked_until = null;
            $user->login_attempts = 0;

            /**
             * primary + incomplete profile => Incomplete
             * primary OR non Primary + Complete => Active
             */
            if ($user->isPrimary()) {
                if (!$userProfileCompleted) {
                    $user->status_id = StatusHelper::getStatusByAttributeName('name', 'incomplete', StatusHelper::STATUS_TYPE_USER);
                } else {
                    $user->status_id = StatusHelper::getStatusByAttributeName('name', 'active', StatusHelper::STATUS_TYPE_USER);
                }
            } else {
                if (($company->isProfileCompleted() && $userProfileCompleted) && AppHelper::matchStrings($user->status->name, "pending")) {
                    $user->status_id = StatusHelper::getStatusByAttributeName('name', 'active', StatusHelper::STATUS_TYPE_USER);
                }
            }

            $user->saveQuietly();

            $company->last_activity_at = now();

            /**
             * Primary User logged in to the platform but didn't complete his profile information 
             * Company Status Change
             */
            if ($user->isPrimary() && $company->status->name === 'pending' && !$userProfileCompleted) {
                $company->status_id = StatusHelper::getStatusByAttributeName('name', 'incomplete');
            }

            $company->saveQuietly();

            $token = JWTAuth::fromUser($user);

            $user->refresh();

            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::LOGIN, AuthEvents::LOGIN->description())
                ->log();

            return $this->apiResponse->props(ApiStatus::SUCCESS, ValidationMessages::LOGGED_IN_SUCCESSFULLY->description())
                ->withData([
                    'user' => new CompanyUserResource($user),
                    'token' => $token,
                ])
                ->send();
        }

        /**
         * Increment Login Attempts 
         */
        $user->login_attempts = $user->login_attempts + 1;
        $user->saveQuietly();

        // locking the user
        if ($user->login_attempts > ApiSettings::USER_ACCOUNT_LOCKING_FAILED_ATTEMPTS->getValue()) {
            $user->locked = true;
            $user->locked_until = now()->addMinutes(ApiSettings::USER_ACCOUNT_LOCKING_MINUTES->getValue());
            $user->saveQuietly();

            Mail::to($request->email)->queue(new AccountLockedMail($user));

            $this->logService
                ->withRequest($request)
                ->props(ValidationMessages::ACCOUNT_LOCKED, ValidationMessages::ACCOUNT_LOCKED->description(), ValidationMessages::TOO_MANY_ATTEMPTS->description())
                ->withFailure()
                ->log();

            return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::ACCOUNT_LOCKED->description())
                ->send();
        }

        // login failed
        $this->logService
            ->withRequest($request)
            ->props(AuthEvents::LOGIN, AuthEvents::LOGIN->description(),  ValidationMessages::INVALID_CREDENTIALS->description())
            ->withFailure()
            ->log();

        return $this->apiResponse->props(ApiStatus::UNAUTHORIZED, ValidationMessages::INVALID_CREDENTIALS->description())
            ->withSystemMessages(ValidationMessages::INVALID_CREDENTIALS)
            ->send();
    }
}
