<?php

namespace App\Http\Controllers\Auth\Api;

use App\Enums\ApiStatus;
use App\Enums\AuthEvents;
use App\Enums\ValidationMessages;
use App\Helpers\ApiHelper;
use App\Helpers\AppHelper;
use App\Helpers\CustomerHelper;
use App\Helpers\SubscriptionHelper;
use App\Http\Controllers\Controller;
use App\Mail\RoleChangedMail;
use App\Models\SubscriptionLicense;
use App\Models\User;
use App\Services\ApiResponseService;
use App\Services\LogService;
use App\Services\OtpService;
use App\Services\SonarQubeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class DeveloperController extends Controller
{
    protected $otpService;

    protected $apiResponse;

    protected $logService;

    public function __construct(ApiResponseService $apiResponse, LogService $logService, OtpService $otpService)
    {
        $this->apiResponse = $apiResponse;
        $this->logService = $logService;
        $this->otpService = $otpService;
    }

    public function unlockAccount(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email|max:255',
        ]);

        if ($validator->fails()) {
            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::LOGIN, AuthEvents::LOGIN->description(),  ValidationMessages::VALIDATION_ERROR, $validator->errors()->toArray())
                ->withFailure()
                ->log();

            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $user = User::withTrashed()->where('email', $request->email)->first();

        if (empty($user)) {
            return CustomerHelper::userNotFound($this->apiResponse);
        }

        $otpService = $this->otpService->withUser($user);

        if ($otpService->isLocked()) {
            $otpService->unlock();
        }

        if ($user->locked) {
            $user->locked = false;
            $user->locked_until = null;
            $user->login_attempts = 0;
            $user->deleted_at = null;
            $user->saveQuietly();
        }

        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->send();
    }

    public function deleteAccount(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email|max:255',
            'password' => [
                'required',
                'string',
                'min:8',
            ],
        ]);

        if ($validator->fails()) {
            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::LOGIN, AuthEvents::LOGIN->description(),  ValidationMessages::VALIDATION_ERROR, $validator->errors()->toArray())
                ->withFailure()
                ->log();

            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $user = User::withTrashed()->where('email', $request->email)->first();

        if (empty($user)) {
            return CustomerHelper::userNotFound($this->apiResponse);
        }

        $user->forceDelete();

        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->send();
    }

    public function getPlans(Request $request)
    {
        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->withData(SubscriptionHelper::getPlanPricePerLineOfCodes(10000))
            ->send();
    }

    public function testMail(Request $request)
    {
        $user = User::where('email', $request->email)->first();

        Mail::to($request->email)->queue(new RoleChangedMail($user, 'Read Only', 'Admin', 'System Admin'));

        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->send();
    }

    public function getServerID(Request $request)
    {
        // 1. Validate the incoming request
        $validator = Validator::make($request->all(), [
            'url' => 'required|string|min:10|max:500',
            'token' => 'required|string|min:10|max:500',
        ]);

        if ($validator->fails()) {
            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::LOGIN, AuthEvents::LOGIN->description(),  ValidationMessages::VALIDATION_ERROR, $validator->errors()->toArray())
                ->withFailure()
                ->log();

            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }


        try {
            $sonarService = new SonarQubeService(
                $request->url,
                $request->token
            );
            $serverId = $sonarService->getServerId();
            print_r($serverId);
            exit;
        } catch (\Exception $e) {
            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::LOGIN, 'Failed to retrieve Server ID', $e->getMessage())
                ->withFailure()
                ->log();

            return $this->apiResponse->props(ApiStatus::BAD_REQUEST, 'Failed to retrieve Server ID')
                ->withData(['error' => $e->getMessage()])
                ->send();
        }

        return $this->apiResponse->props(ApiStatus::SUCCESS, 'Server ID retrieved successfully')
            ->withData([
                'server_id' => $serverId,
            ])
            ->send();
    }
}
