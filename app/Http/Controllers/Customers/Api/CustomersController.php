<?php

namespace App\Http\Controllers\Customers\Api;

use App\Enums\ApiStatus;
use App\Enums\DefaultValues;
use App\Enums\ResponseMessages;
use App\Enums\ValidationMessages;
use App\Helpers\ApiHelper;
use App\Helpers\AppHelper;
use App\Helpers\CustomerHelper;
use App\Helpers\InvitationHelper;
use App\Helpers\StatusHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\UpdateCustomerRequest;
use App\Http\Resources\CompanyUserResource;
use App\Mail\RoleChangedMail;
use App\Mail\UserReactivateMail;
use App\Models\CompanyCountry;
use App\Models\CompanyIndustry;
use App\Models\CompanySector;
use App\Models\User;
use App\Rules\BusinessEmailRule;
use App\Rules\PhoneNumberRule;
use App\Services\ApiResponseService;
use App\Services\RbacService;
use App\Traits\Blamable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class CustomersController extends Controller
{
    use Blamable;

    protected $apiResponse;
    protected $rbacService;

    public function __construct(ApiResponseService $apiResponse, RbacService $rbacService)
    {
        $this->apiResponse = $apiResponse;
        $this->rbacService = $rbacService;
    }

    /**
     * Update Customer 
     * Only Primary User Can Update Customer Infos
     */
    public function updateCustomer(UpdateCustomerRequest $request)
    {
        $user = self::getBlamable();

        if (empty($user)) {
            return CustomerHelper::userNotFound($this->apiResponse);
        }

        $customer = $user->company;

        if (empty($customer)) {
            return CustomerHelper::companyNotFound($this->apiResponse);
        }

        if (!empty($request->name)) {
            $customer->update([
                'name'  => $request->name,
            ]);
        }

        if (!empty($request->website)) {
            $customer->update([
                'website'  => $request->website,
            ]);
        }

        if (!empty($request->number_of_employees)) {
            $customer->update([
                'number_of_employees'  => $request->number_of_employees,
            ]);
        }

        $customer->syncRelation(CompanyCountry::class, 'country_id', $request->countries);
        $customer->syncRelation(CompanySector::class, 'sector_id', $request->sectors);
        $customer->syncRelation(CompanyIndustry::class, 'industry_id', $request->industries);

        /**
         * Active: customer profile & primary user profile information is complete 
         */
        if ($customer->isProfileCompleted() && !AppHelper::matchStrings($customer->status->name, "active") && ($user->isProfileCompleted() && $user->isPrimary())) {
            $customer->status_id = StatusHelper::getStatusByAttributeName('name', 'active');
            $customer->save();

            /**
             * Update Primary user status  
             */
            if (!AppHelper::matchStrings($user->status->name, 'active')) {
                $user->status_id = StatusHelper::getStatusByAttributeName('name', 'active', StatusHelper::STATUS_TYPE_USER);
                $user->save();
            }
        }

        return $this->apiResponse->props(ApiStatus::SUCCESS, ResponseMessages::CUSTOMER_UPDATED->description())
            ->withData((new CompanyUserResource($user->fresh()))->toArray($request))
            ->send();
    }

    /**
     * Customer Users 
     */
    public function CustomerUsers(Request $request)
    {
        $companyUsers = CustomerHelper::getCompanyUsers($this->apiResponse);

        if ($companyUsers instanceof \Illuminate\Http\JsonResponse) {
            return $companyUsers;
        }

        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->withData(CompanyUserResource::collection($companyUsers)->toArray($request))
            ->send();
    }

    /**
     * Single Customer User 
     */
    public function CustomerUser($id, Request $request)
    {
        $user = CustomerHelper::getCompanyUsers($this->apiResponse, user_id: $id);

        if ($user instanceof \Illuminate\Http\JsonResponse) {
            return $user;
        }

        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->withData((new CompanyUserResource($user))->toArray($request))
            ->send();
    }

    /**
     * Add Customer User 
     */
    public function addCustomerUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => [
                'bail',
                'required',
                'string',
                'email',
                'max:255',
                function ($attribute, $value, $fail) {

                    $trashedUser = \App\Models\User::withTrashed()->where('email', $value)->first();

                    if ($trashedUser && $trashedUser->trashed()) {
                        $fail(ValidationMessages::EMAIL_UNIQUE_INACTIVE->description());
                    } elseif (\App\Models\User::where('email', $value)->exists()) {
                        $fail(ValidationMessages::EMAIL_UNIQUE->description());
                    }
                },
                app(BusinessEmailRule::class)
            ],
            'name' =>  ['bail', 'required', 'string'],
            'phone' => [
                'bail',
                'required',
                'string',
                app(PhoneNumberRule::class)
            ],
            'role'  => ['bail', 'required', 'string'],
        ], [
            'email.required' => ValidationMessages::EMAIL_REQUIRED->description(),
            'email.email' => ValidationMessages::EMAIL_FORMAT->description(),
        ]);

        if ($validator->fails()) {
            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $user = Auth::user();

        if (empty($user)) {
            return CustomerHelper::userNotFound($this->apiResponse);
        }

        $companyId = $user->company_id ?? null;

        if (empty($companyId)) {
            return CustomerHelper::companyNotFound($this->apiResponse);
        }

        /**
         * User Will be pending until he logs in  
         */
        $pendingStatusId = StatusHelper::getStatusByAttributeName('name', 'pending', StatusHelper::STATUS_TYPE_USER);

        $customerUser = User::create([
            'company_id' => $companyId,
            'status_id'  => $pendingStatusId,
            'email' => $request->email,
            'name'  => $request->name,
            'phone_number'  => $request->phone,
            'created_by' => $user->user_id,
            'accepted' => true
        ]);

        $this->rbacService->withUser($customerUser)->withRole($request->role)->assign();

        InvitationHelper::sendUserInvitation($request->email, $request->name);

        return $this->apiResponse->props(ApiStatus::SUCCESS, ResponseMessages::USER_CREATED->description())
            ->withData((new CompanyUserResource($customerUser))->toArray($request))
            ->send();
    }

    /**
     * Update Customer User 
     */
    public function updateCustomerUser($id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => [
                'bail',
                'required',
                'string',
                'email',
                'max:255',
                app(BusinessEmailRule::class)
            ],
            'name' =>  ['bail', 'required', 'string'],
            'phone' => [
                'bail',
                'required',
                'string',
                app(PhoneNumberRule::class)
            ],
            'role'  => ['bail', 'required', 'string']
        ], [
            'email.required' => ValidationMessages::EMAIL_REQUIRED->description(),
            'email.email' => ValidationMessages::EMAIL_FORMAT->description(),
        ]);

        if ($validator->fails()) {
            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $user = CustomerHelper::getCompanyUsers($this->apiResponse, user_id: $id);

        if ($user instanceof \Illuminate\Http\JsonResponse) {
            return $user;
        }

        $customer = $user->company;
        $userRole = $user->roles()->first();
        $attributeHasChanged = false;

        /**
         * Check If Email Is Already Exist For A OR Non Trashed User 
         */
        if (!empty($request->email) && !AppHelper::matchStrings($request->email, $user->email)) {
            $trashedUser = \App\Models\User::withTrashed()
                ->where('email', $request->email)
                ->where('user_id', '!=', $user->user_id)
                ->first();

            if ($trashedUser && $trashedUser->trashed()) {
                return ApiHelper::validationError($this->apiResponse, [
                    'email' => [ValidationMessages::EMAIL_UNIQUE_INACTIVE->description()],
                ]);
            } elseif ($trashedUser) {
                return ApiHelper::validationError($this->apiResponse, [
                    'email' => [ValidationMessages::EMAIL_UNIQUE->description()],
                ]);
            }

            $user->email = $request->email;
            $attributeHasChanged = true;
        }

        /**
         * Check If Name Has Changed 
         */
        if (!empty($request->name) && !AppHelper::matchStrings($request->name, $user->name)) {
            $user->name = $request->name;
            $attributeHasChanged = true;
        }

        /**
         * Check If Phone Number Has Changed 
         */
        if (!empty($request->phone) && !AppHelper::matchStrings($request->phone, $user->phone_number)) {
            $user->phone_number =  $request->phone;
            $attributeHasChanged = true;
        }

        /**
         * Check If Role Has Changed 
         */
        if (!empty($request->role) && !AppHelper::matchStrings($request->role, $userRole)) {
            $rbacService = $this->rbacService->withUser($user)->withRole($request->role);

            if ($rbacService->roleHasChanged()) {
                $modifiedBy = self::getBlamable()->name ?? DefaultValues::SYSTEM_USER_ID->name();

                $rbacService->assign();

                $attributeHasChanged = true;

                Mail::to($user->email)->queue(new RoleChangedMail($user, $rbacService->getCurrentRole(), $request->role, $modifiedBy));
            }
        }

        /**
         * Customer Active Status : customer profile or primary user profile information is complete 
         */
        if (($user->isProfileCompleted() && $user->isPrimary()) && !AppHelper::matchStrings($user->status->name, "active")) {
            $user->status_id = StatusHelper::getStatusByAttributeName('name', 'active', StatusHelper::STATUS_TYPE_USER);
            $attributeHasChanged = true;

            if ($customer->isProfileCompleted() && !AppHelper::matchStrings($customer->status->name, "active")) {
                $customer->status_id = StatusHelper::getStatusByAttributeName('name', 'active');
                $customer->save();
            }
        }

        if ($attributeHasChanged) {
            $user->save();
        }

        return $this->apiResponse->props(ApiStatus::SUCCESS, ResponseMessages::USER_UPDATED->description())
            ->withData((new CompanyUserResource($user->fresh()))->toArray($request))
            ->send();
    }

    /**
     * Deactivate Customer User 
     */
    public function deactivateCustomerUser($id, Request $request)
    {
        $user = CustomerHelper::getCompanyUsers($this->apiResponse, user_id: $id);

        if ($user instanceof \Illuminate\Http\JsonResponse) {
            return $user;
        }

        $blamable = self::getBlamable();
        $customer = $blamable->company;
        $closedStatusId = StatusHelper::getStatusByAttributeName('name', 'closed');

        $suspendedStatusId = StatusHelper::getStatusByAttributeName('name', 'suspended', StatusHelper::STATUS_TYPE_USER);
        $deactivatedStatusId = StatusHelper::getStatusByAttributeName('name', 'deactivated', StatusHelper::STATUS_TYPE_USER);

        /**
         * Primary User Case 
         */
        if ($blamable->isPrimary()) {
            $customer->update([
                'status_id' => $closedStatusId,
            ]);

            // Update all customer users' status to suspended
            $customer->users()->update([
                'status_id' => $deactivatedStatusId,
            ]);
        } else {
            $user->update([
                'status_id' => $suspendedStatusId,
            ]);
        }

        return $this->apiResponse->props(ApiStatus::SUCCESS, ResponseMessages::USER_SUSPENDED->description())
            ->send();
    }

    /**
     * Activate Customer User 
     */
    public function activateCustomerUser($id, Request $request)
    {
        $user = CustomerHelper::getCompanyUsers($this->apiResponse, user_id: $id);

        if ($user instanceof \Illuminate\Http\JsonResponse) {
            return $user;
        }

        $blamable = self::getBlamable();
        $activeStatusId = StatusHelper::getStatusByAttributeName('name', 'active', StatusHelper::STATUS_TYPE_USER);

        $user->update([
            'status_id' => $activeStatusId,
            'updated_by' => $blamable->user_id
        ]);

        Mail::to($user->email)->queue(new UserReactivateMail($user));

        return $this->apiResponse->props(ApiStatus::SUCCESS, ResponseMessages::USER_REACTIVATED->description())
            ->send();
    }

    /**
     * Resend Customer User Invitation 
     */
    public function resendCustomerUserInvitation($id, Request $request)
    {
        $user = CustomerHelper::getCompanyUsers($this->apiResponse, user_id: $id);

        if ($user instanceof \Illuminate\Http\JsonResponse) {
            return $user;
        }

        InvitationHelper::sendUserInvitation($user->email, $user->name);

        return $this->apiResponse->props(ApiStatus::SUCCESS, ResponseMessages::INVITATION_SENT->description())
            ->send();
    }

    /**
     * Change Customer User Status 
     */
    public function changeCustomerUserStatus($id, Request $request)
    {
        $user = CustomerHelper::getCompanyUsers($this->apiResponse, user_id: $id);

        if ($user instanceof \Illuminate\Http\JsonResponse) {
            return $user;
        }

        $validator = Validator::make($request->all(), [
            'status' => [
                'bail',
                'required',
                'string',
                'exists:statuses,name,type,' . StatusHelper::STATUS_TYPE_USER
            ],
        ]);

        if ($validator->fails()) {
            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $statusId = StatusHelper::getStatusByAttributeName('name', $request->status, StatusHelper::STATUS_TYPE_USER);

        if (empty($statusId)) {
            return $this->apiResponse->props(ApiStatus::NOT_FOUND)
                ->withSystemMessages(ValidationMessages::STATUS_NOT_FOUND)
                ->send();
        }

        $user->update([
            'status_id' => $statusId,
        ]);

        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->withData((new CompanyUserResource($user))->toArray($request))
            ->send();
    }
}
