<?php

namespace App\Http\Controllers\Socialite\Api;

use App\Enums\ApiStatus;
use App\Helpers\StatusHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\CompanyUserResource;
use App\Models\User;
use App\Services\ApiResponseService;
use Laravel\Socialite\Facades\Socialite;
use Tymon\JWTAuth\Facades\JWTAuth;
use Illuminate\Support\Str;

class GoogleController extends Controller
{
    protected $apiResponse;

    public function __construct(ApiResponseService $apiResponse)
    {
        $this->apiResponse = $apiResponse;
    }

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function redirectToGoogle()
    {
        $url = Socialite::driver('google')->stateless()->redirect()->getTargetUrl();

        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->withData(['url' => $url])
            ->send();
    }

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function handleGoogleCallback()
    {
        $googleUser = Socialite::driver('google')->stateless()->user();

        $user = User::where(['email', $googleUser->getEmail()]);

        /**
        * Login The User 
        */
        if (!empty($user)) {
            $user->update([
                'last_activity_at' => now()
            ]);

            $token = JWTAuth::fromUser($user);

            return $this->apiResponse->props(ApiStatus::SUCCESS)
                ->withData([
                    'token' => $token,
                    'user' => new CompanyUserResource($user),
                ])
                ->send();
        }

        $user = User::create(
            [
                'email' => $googleUser->getEmail(),
                'google_id' => $googleUser->getId(),
                'name' => $googleUser->getName(),
                'email_verified_at' => now(),
                'password' => bcrypt(Str::random(40)),
                'last_activity_at' => now(),
                'accepted' => true,
                'status_id' => StatusHelper::getStatusByAttributeName('name', '')
            ]
        );

        $token = JWTAuth::fromUser($user);

        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->withData([
                'token' => $token,
                'user' => new CompanyUserResource($user),
            ])
            ->send();
    }
}
