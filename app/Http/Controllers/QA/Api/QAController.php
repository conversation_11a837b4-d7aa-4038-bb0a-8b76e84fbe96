<?php

namespace App\Http\Controllers\QA\Api;

use App\Enums\ApiStatus;
use App\Helpers\ApiHelper;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\ApiResponseService;
use App\Traits\Blamable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class QAController extends Controller
{
    use Blamable;

    protected $apiResponse;

    public function __construct(ApiResponseService $apiResponse)
    {
        $this->apiResponse = $apiResponse;
    }

    /**
    * Get User OTP Code
    * /qa/otp
    */
    public function getUserOtpCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email|max:255',
        ]);

        if ($validator->fails()) {
            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $user = User::where('email', $request->email)->first();

        return $this->apiResponse->props(ApiStatus::SUCCESS)
        ->withData(['otp' => optional($user)->otp])
        ->send();
    }

    /**
    * Get User OTP Code
    * /qa/invitation-link
    */
    public function getUserInvitationLink(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email|max:255',
        ]);

        if ($validator->fails()) {
            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $user = User::where('email', $request->email)->first();

        return $this->apiResponse->props(ApiStatus::SUCCESS)
        ->withData(['link' => optional($user)->invitationLink()])
        ->send();
    }
}
