<?php

namespace App\Http\Controllers\Settings\Api;

use App\Enums\ApiSettings;
use App\Enums\ApiStatus;
use App\Http\Controllers\Controller;
use App\Services\ApiResponseService;
use Illuminate\Http\Request;

class SettingsController extends Controller
{
    protected $apiResponse;

    public function __construct(ApiResponseService $apiResponse)
    {
        $this->apiResponse = $apiResponse;
    }

    public function list(Request $request)
    {
        return $this->apiResponse->props(ApiStatus::SUCCESS)
        ->withData(ApiSettings::list())
        ->send();
    }
}
