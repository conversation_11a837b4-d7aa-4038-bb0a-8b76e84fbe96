<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Filament\Facades\Filament;
use Illuminate\Validation\ValidationException;

class AdminAuth
{
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();

        if (!$user || $user->type !== 'admin') {
            Auth::logout(); 

            throw ValidationException::withMessages([
                'data.email' => 'These credentials do not match our records.',
            ]);
        }

        return $next($request);
    }
}
