<?php

namespace App\Http\Resources;

use App\Helpers\AppHelper;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Crypt;

class SubscriptionLicenseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->subscription_license_id,
            'license_key' => $this->license_key,
            'license_type' => $this->license_type,
            'server_id' => $this->server_id,
            'environment' => $this->environment,
            'environment_data' => [
                'url' => $this->sonar_url,
                'username' => $this->sonar_username,
                'password' => AppHelper::decryptString($this->sonar_password), 
            ],
            'history' => SubscriptionLicenseHistoryResource::collection($this->history),
        ];
    }
}
