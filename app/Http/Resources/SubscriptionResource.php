<?php

namespace App\Http\Resources;

use App\Enums\Subscriptions;
use App\Helpers\SubscriptionHelper;
use App\Services\NumberFormatter;
use App\Traits\Blamable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubscriptionResource extends JsonResource
{
    use Blamable;

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return 
            [
                'id' => $this?->id,
                'public_subscription_id' => $this->public_subscription_id,
                'step' => $this->subscriptionLastProgressName(),
                'product' => config('settings.default_product_name'),
                'subscription_model' => $this->subscriptionModelName(),
                'subscription_plan_name' => $this->plan->name,
                'subscription_plan' => $this->plan->slug,
                'plan' => $this->plan->billing_cycle,
                'billing_country_id' => (int) $this->billing_country_id,
                'platform_country_id' => (int) $this->country_id,
                'addons' => $this->addons(),
                'status' => $this->subscription_status,
                'status_label' => Subscriptions::getLabel($this->subscription_status),
                'payment_status' => SubscriptionHelper::paymentStatusFormatted($this->resource),
                'auto_renew' => $this->isRenewableFormatted(),
                'includes_disaster_recovery' => $this->hasDisasterRecovery(),
                'includes_security_addon' => $this->hasSecurityAddon(),
                'includes_premium_support' => $this->hasPremiumSupport(),
                ...NumberFormatter::amount((float) $this->assigned_quota)->asArray()->parameterName('line_of_codes')->abbreviate()->format(),
                'line_of_codes_formatted_label' => NumberFormatter::amount((float) $this->assigned_quota)->abbreviate()->useFullWords()->format(),
                ...NumberFormatter::amount((float) $this->used_quota)->asArray()->parameterName('used_quota')->abbreviate()->format(),
                ...NumberFormatter::amount((float) $this->remaining_quota)->asArray()->parameterName('remaining_quota')->abbreviate()->format(),
                'used_quota_percentage' => NumberFormatter::amount((float) $this->used_quota, '%', $this->assigned_quota)->format(),
                'start_date' => $this->starts_at_formatted_date,
                'end_date' => $this->ends_at_formatted_date,
                'number_of_years' => $this->number_of_years,
                'remaining_days' => $this->remainingDays(),
                'created_by' => self::getBlamableNameById($this->created_by),
                'created_on' => $this->created_at_formatted,
                'last_updated_by' => self::getBlamableNameById($this->updated_by),
                'last_updated_on' => $this->updated_at_formatted,
                'sales_order' => $this->erp_sales_order_id,
                ...$this->totalPrice(),
                'estimated_price' => $this->estimatedPrice(),
                'histories' => SubscriptionHistoryResource::collection($this->subscriptionHistories),
                'licenses' => SubscriptionLicenseResource::collection($this->subscriptionLicenses),
                'payments' => SubscriptionPaymentResource::collection($this->subscriptionPayments),
                'receipts' => SubscriptionReceiptResource::collection($this->subscriptionReceipts),
                'billing_address' => (new CompanyAddressResource($this->billingAddress()))
            ];
    }
}
