<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PlatformBankResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->platform_bank_id,
            'receipient_name' => config('settings.default_payment_receipient_name'),
            'bank_name' => $this->bank_name,
            'account_number' => $this->account_number,
            'swift_code' => $this->swift_code,
            'bank_address' => $this->bank_address,
            'iban' => $this->iban,
        ];
    }
}
