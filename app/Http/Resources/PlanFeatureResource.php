<?php

namespace App\Http\Resources;

use App\Helpers\AppHelper;
use App\Helpers\BillingsHelper;
use App\Services\MoneyFormatter;
use App\Services\NumberFormatter;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PlanFeatureResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->plan_feature_id,
            'name' => $this->feature?->name,
            'slug' => AppHelper::normalizeSlug($this->feature?->slug),
            ...NumberFormatter::amount((float) $this->valid_from_minimum)->parameterName('valid_from_minimum')->abbreviate()->useFullWords()->asArray()->format(),
            ...NumberFormatter::amount((float) $this->valid_to_maximum)->parameterName('valid_to_maximum')->abbreviate()->useFullWords()->asArray()->format(),
            'currency' => $this?->plan?->currency(),
            ...MoneyFormatter::price($this->price * 100, $this?->plan?->currency())->asArray()->abbreviate()->format(),
        ];
    }
}
