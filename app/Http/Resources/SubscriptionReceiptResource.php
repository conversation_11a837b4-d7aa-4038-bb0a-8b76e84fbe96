<?php

namespace App\Http\Resources;

use App\Traits\Blamable;
use App\Traits\Timezones;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubscriptionReceiptResource extends JsonResource
{
    use Blamable;

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->subscription_receipts_id,
            'receipt_number' => $this->receipt_number,
            'amount' => $this->amount,
            'currency_code' => $this->currency_code,
            'receipt_status' => $this->receipt_status,
            'approved_by' => !empty($this->approved_by) ? self::getBlamableNameById($this->approved_by) : null,
            'rejected_by' => !empty($this->rejected_by) ? self::getBlamableNameById($this->rejected_by) : null,
            'rejection_reason' => $this->rejection_reason,
            'paid_at' => $this->paid_at_formatted,
        ];
    }
}
