<?php

namespace App\Http\Resources;

use App\Enums\DefaultValues;
use App\Helpers\AppHelper;
use App\Traits\Blamable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyResource extends JsonResource
{
    use Blamable;

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'customer_id' => $this->company_id,
            'public_customer_id' => $this->public_company_id,
            'customer_name' => $this->name,
            'status' => new StatusResource($this->status),
            'countries' => !empty($this->countries) ? implode(', ', $this->countries->pluck('name')->toArray()) : DefaultValues::ARRAY->get(),
            'sectors' => !empty($this->sectors) ? implode(', ', $this->sectors->pluck('name')->toArray()) : DefaultValues::ARRAY->get(),
            'industry' => !empty($this->industries) ? implode(', ', $this->industries->pluck('name')->toArray()) : DefaultValues::ARRAY->get(),
            'website' => $this->website,
            'number_of_employees' => $this->number_of_employees,
            'last_updated_by' => self::getBlamableNameById($this->updated_by),
            'last_updated_on' => $this->updated_at_formatted,
            'registration_type' => $this->registration_type,
            'is_profile_complete' => $this->resource->isProfileCompleted(),
            'has_subscription' => $this->resource->hasSubscription(),
            'has_team_users' => $this->resource->hasNonPrimaryUsers(),
        ];
    }
}
