<?php

namespace App\Http\Resources;

use App\Helpers\AppHelper;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubscriptionPaymentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->subscription_payments_id,
            'payment_type'  => AppHelper::normalizeStatus($this->payment_type),
            'payment_gateway' => $this->payment_gateway,
        ];
    }
}
