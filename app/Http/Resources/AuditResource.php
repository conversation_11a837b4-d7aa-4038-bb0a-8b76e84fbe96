<?php

namespace App\Http\Resources;

use App\Enums\DefaultValues;
use App\Helpers\AppHelper;
use App\Helpers\DateTimeHelper;
use App\Traits\Blamable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AuditResource extends JsonResource
{
    use Blamable;
    
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'activity_id' => AppHelper::getFormattedId($this->id, DefaultValues::ACTIVITY_PREFIX->get()),
            'action' => $this->event,
            'user' => self::getBlamableNameById($this->user_id),
            'time_stamp' => DateTimeHelper::parseDateTime($this->created_at)
        ];
    }
}
