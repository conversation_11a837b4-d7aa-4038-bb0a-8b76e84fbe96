<?php

namespace App\Http\Resources;

use App\Enums\DefaultValues;
use App\Enums\Subscriptions;
use App\Helpers\AppHelper;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubscriptionLicenseHistoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'action_id' => AppHelper::getFormattedId((string) $this->subscription_license_history_id, DefaultValues::SUBSCRIPTION_LICENSE_HISTORY_PREFIX->get()),
            'event_type' => Subscriptions::getLabel($this->history_event),
            'server_type' => Subscriptions::getLabel($this->environment),
            'created_by' => self::getBlamableNameById($this->created_by),
            'timestamp' => $this->updated_at_formatted,
        ];
    }
}
