<?php

namespace App\Http\Resources;

use App\Enums\DefaultValues;
use App\Enums\Subscriptions;
use App\Helpers\AppHelper;
use App\Helpers\SubscriptionHelper;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubscriptionHistoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'subscription_history_id' => AppHelper::getFormattedId((string) $this->subscription_history_id, DefaultValues::SUBSCRIPTION_HISTORY_PREFIX->get()),
            'event_type' => $this->history_event,
            'event_type_label' => Subscriptions::getLabel($this->history_event),
            'created_by' => self::getBlamableNameById($this->created_by),
            'subscription_status' => $this->subscription_status,
            'subscription_status_label' => Subscriptions::getLabel($this->subscription_status),
            'payment_status' => SubscriptionHelper::paymentStatusFormatted($this->resource),
            'rejection_reason_description' => $this->rejection_reason_description,
            'billing_id' => $this->billing_id,
            'timestamp' => $this->updated_at_formatted,
        ];
    }
}
