<?php

namespace App\Http\Resources;

use App\Helpers\AppHelper;
use App\Helpers\RbacHelper;
use App\Models\Log;
use App\Traits\Blamable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyUserResource extends JsonResource
{
    use Blamable;

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'name' => $this->name,
            'user_id' => $this->user_id,
            'role' => RbacHelper::getUserRole($this, true),
            'last_login' => $this->last_activity_at_formatted,
            'created_by' =>  self::getBlamableNameById($this->created_by),
            'created_on' => $this->created_at_formatted,
            'last_updated_by' => self::getBlamableNameById($this->updated_by),
            'last_updated_on' => $this->updated_at_formatted,
            'status' => new StatusResource($this->status),
            'is_profile_complete' => $this->resource->isProfileCompleted(),
            'extra_attributes' => [
                'public_user_id' => $this->public_user_id,
                'email' => $this->email,
                'phone_number' => $this->phone_number,
                'customer' => new CompanyResource($this->company),
                'logs_activities' => [
                    'activities' => AuditResource::collection($this->audits()->latest()->take(20)->get()),
                    'logs' => LogsResource::collection($this->logs()->latest()->take(20)->get()),
                ],
            ]
        ];
    }
}
