<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyAddressResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        if(empty($this->company_address_id))
        {
            return [];
        }
        
        return [
            'id' => $this->company_address_id,
            'platform_bank_id' => $this->platform_bank_id,
            'company_registration_number' => $this->commercial_registration_number,
            'country' => $this?->country?->name,
            'state' => $this->state,
            'city' => $this->city,
            'zip' => $this->zip,
            'address_1' => $this->address_1,
            'tax_id' => $this->tax_id,
            'currency_id' => $this->currency?->id,
            'currency' => $this->currency_code,
        ];
    }
}
