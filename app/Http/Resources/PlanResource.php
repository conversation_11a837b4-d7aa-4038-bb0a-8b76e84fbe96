<?php

namespace App\Http\Resources;

use App\Helpers\SubscriptionHelper;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PlanResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'badge' => $this->badge,
            'billing_cycle' => $this->billing_cycle,
            ...SubscriptionHelper::getPlanFeatures($this->resource)
        ];
    }
}
