<?php

namespace App\Models;

use App\Enums\DefaultValues;
use App\Enums\Subscriptions;
use App\Helpers\AppHelper;
use App\Scopes\SortingScope;
use App\Traits\Blamable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use TomatoPHP\FilamentSubscriptions\Models\Plan as BaseModel;

class Plan extends BaseModel
{
    use HasFactory, Blamable;

    public function getFillable()
    {
        return array_merge(parent::getFillable(), [
            'badge',
            'billing_cycle',
            'created_at',
            'updated_at',
            'deleted_at',
            'created_by',
            'updated_by',
            'deleted_by'
        ]);
    }

    public function getCasts()
    {
        return array_merge(parent::getCasts(), [
            //
        ]);
    }

    public function getFormattedPriceAttribute()
    {
        return number_format($this->price, 2) . ' SAR';
    }

    public function getBillingCycleTextAttribute()
    {
        return ucfirst($this->billing_cycle);
    }

    public static function enabled(): \Illuminate\Database\Eloquent\Builder
    {
        return self::where('is_active', true);
    }

    public function features(): HasMany
    {
        return $this->hasMany(Feature::class);
    }

    public function planFeatures()
    {
        return $this->hasMany(PlanFeature::class);
    }  

    public function currency()
    {
        return $this->currency ?? DefaultValues::CURRENCY->get();
    }

    protected static function booted()
    {
        static::addGlobalScope(new SortingScope());
    }


    /**
    * Is Free Trial 
    */
    public function isFreeTrial(): bool
    {
        if(empty($this->slug)) { return false; }

        return AppHelper::matchStrings($this->slug, Subscriptions::TRIAL_VERSION->value);
    }
}
