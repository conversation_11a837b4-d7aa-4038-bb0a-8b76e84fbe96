<?php

namespace App\Models;

use App\Scopes\SortingScope;
use App\Traits\Blamable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Contracts\Auditable;

class PlanFeature extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, Blamable;

    /**
     * The primary key column name.
     *
     * @var string
     */
    protected $primaryKey = 'plan_feature_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'plan_id',
        'feature_id',
        'parent_plan_feature_id',
        'valid_from_minimum',
        'valid_to_maximum',
        'is_addon',
        'is_extra_addon',
        'price',
        'sort_order',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by',
        'deleted_at',
        'deleted_by'
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
            //'password' => 'hashed',
            //'countries' => 'array',
        ];
    }

    public function addons()
    {
        return $this->hasMany(PlanFeature::class, 'parent_plan_feature_id', 'plan_feature_id')->where('is_addon', true)->where('parent_plan_feature_id', '!=', null);
    }

    public function parent()
    {
        return $this->belongsTo(PlanFeature::class, 'parent_plan_feature_id', 'plan_feature_id');
    }

    /**
     * Attributes to include in the Audit.
     *
     * @var array
     */
    // protected $auditInclude = [
    //     'title',
    //     'content',
    // ];

    protected static function booted()
    {
        static::addGlobalScope(new SortingScope());

        static::creating(function ($model) {
            self::setCreatedUpdatedBy($model);
        });

        static::saving(function ($model) {
            self::setUpdatedBy($model);
        });

        static::deleting(function ($model) {
            self::setDeletedBy($model);
        });

        return parent::booted();
    }

    public function feature(): BelongsTo
    {
        return $this->belongsTo(Feature::class);
    }

    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    public function isLocBundle(): bool
    {
        return (!$this->is_addon && !empty($this->parent_plan_feature_id));
    }
}
