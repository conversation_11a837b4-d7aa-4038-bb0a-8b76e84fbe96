<?php

namespace App\Models;

use App\Scopes\SortingScope;
use App\Traits\Blamable;
use App\Traits\Timezones;
use Laravelcm\Subscriptions\Models\SubscriptionUsage as BaseModel;

class SubscriptionUsage extends BaseModel
{
    use Blamable, Timezones;

    public function getFillable()
    {
        return [
            ...parent::getFillable(),
            'plan_feature_id'
        ];
    }

    public function getCasts()
    {
        return array_merge(parent::getCasts(), [
            //
        ]);
    }

    public function planFeature()
    {
        return $this->belongsTo(PlanFeature::class, 'plan_feature_id');
    }

    protected static function booted()
    {
        static::addGlobalScope(new SortingScope());

        static::creating(function ($model) {
            self::setCreatedUpdatedBy($model);
        });
        
        static::saving(function ($model) {
            self::setUpdatedBy($model);
        });

        static::deleting(function ($model) {
            self::setDeletedBy($model);
        });

        return parent::booted();
    }
}
