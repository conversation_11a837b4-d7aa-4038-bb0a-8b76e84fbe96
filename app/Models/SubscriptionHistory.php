<?php

namespace App\Models;

use App\Scopes\SortingScope;
use App\Traits\Blamable;
use App\Traits\Timezones;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\Translatable\HasTranslations;

class SubscriptionHistory extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, Blamable, Timezones, HasTranslations;

    protected $table = 'subscription_history';

    protected $primaryKey = 'subscription_history_id';

    protected static $parentModel = Subscription::class;

    public array $translatable = ['description'];

    public function getFillable()
    {
        return array_merge((new static::$parentModel)->getFillable(), [
            'subscription_id',
            'history_event',
            'rejection_reason_description',
            'rejection_reason'
        ]);
    }

    public function getCasts()
    {
        return (new static::$parentModel)->getCasts();
    }

    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    public function subscriber(): MorphTo
    {
        return $this->morphTo('subscriber', 'subscriber_type', 'subscriber_id', 'company_id');
    }

    public function billingCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'billing_country_id', 'id');
    }

    public static function mapParentColumns(array $fillable): array
    {
        $rules = [
            'id' => 'subscription_id',
        ];

       return collect($fillable)->mapWithKeys(function ($value, $key) use ($rules) {
            return [$rules[$key] ?? $key => $value];
        })->toArray();
    }

    
    protected static function booted()
    {
        static::addGlobalScope(new SortingScope());

        static::creating(function ($model) {
            self::setCreatedUpdatedBy($model);
        });
        
        static::saving(function ($model) {
            self::setUpdatedBy($model);
        });

        static::deleting(function ($model) {
            self::setDeletedBy($model);
        });

        return parent::booted();
    }
}
