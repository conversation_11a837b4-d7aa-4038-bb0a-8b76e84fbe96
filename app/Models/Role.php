<?php

namespace App\Models;

use App\Helpers\RbacHelper;
use App\Scopes\SortingScope;
use App\Traits\Blamable;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\Permission\Models\Role as ModelsRole;

class Role extends ModelsRole implements Auditable
{
    use \OwenIt\Auditing\Auditable, Blamable;

    /**
     * The primary key column name.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
    */
    protected $fillable = [
        'type',
        'name',
        'guard_name',
        'exposed_to_customer',
        'sort_order',
        'created_at',
        'updated_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
            //'password' => 'hashed',
            //'countries' => 'array',
        ];
    }

    /**
     * Attributes to include in the Audit.
     *
     * @var array
     */
    // protected $auditInclude = [
    //     'title',
    //     'content',
    // ];

    protected static function booted()
    {
        static::addGlobalScope(new SortingScope());

        static::creating(function ($model) {
            self::setCreatedUpdatedBy($model);
        });
        
        static::saving(function ($model) {
            self::setUpdatedBy($model);
        });

        static::deleting(function ($model) {
            self::setDeletedBy($model);
        });

        return parent::booted();
    }
}