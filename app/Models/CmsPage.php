<?php

namespace App\Models;

use App\Scopes\SortingScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use SolutionForest\FilamentCms\Models\CmsPage as BaseModel;

class CmsPage extends BaseModel
{
    use HasFactory;

    public const HOME_SLUG = 'home';

    protected static function booted()
    {
        static::addGlobalScope(new SortingScope());

        static::creating(function ($model) {
            self::setCreatedUpdatedBy($model);
        });

        static::saving(function ($model) {
            self::setUpdatedBy($model);
        });

        static::deleting(function ($model) {
            self::setDeletedBy($model);
        });

        return parent::booted();
    }
}