<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;
use App\Models\Company;
use App\Scopes\SortingScope;
use App\Traits\Blamable;

class Country extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, Blamable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'code',
        'phone',
        'lat',
        'lang',
        'sort_order',
        'created_at',
        'updated_at',
        'deleted_at',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the companies associated with the country.
     */
    public function companies()
    {
        return $this->belongsToMany(Company::class, 'company_countries', 'country_id', 'company_id')
            ->withTimestamps();
    }

    /**
    * Country Cities 
    */
    public function cities()
    {
        return $this->hasMany(City::class);
    }

    /**
     * Get Sorted Countries 
     */
    public function scopeSorted($query)
    {
        return $query->orderByRaw('CASE WHEN sort_order > 0 THEN 0 ELSE 1 END')
            ->orderBy('sort_order', 'asc')
            ->orderBy('name', 'asc');
    }

    protected static function booted()
    {
        static::addGlobalScope(new SortingScope());

        static::creating(function ($model) {
            self::setCreatedUpdatedBy($model);
        });

        static::saving(function ($model) {
            self::setUpdatedBy($model);
        });

        static::deleting(function ($model) {
            self::setDeletedBy($model);
        });

        return parent::booted();
    }
}
