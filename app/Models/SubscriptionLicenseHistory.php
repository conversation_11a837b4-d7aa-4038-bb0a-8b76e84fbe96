<?php

namespace App\Models;

use App\Scopes\SortingScope;
use App\Traits\Blamable;
use App\Traits\Timezones;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Contracts\Auditable;

class SubscriptionLicenseHistory extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, Blamable, Timezones;

    protected $table = 'subscription_license_history';

    protected $primaryKey = 'subscription_license_history_id';

    protected $fillable = [
        'subscription_license_id',
        'subscription_id',
        'company_id',
        'license_key',
        'server_id',
        'license_type',
        'environment',
        'sonar_url',
        'sonar_username',
        'sonar_password',
        'sonar_api_token',
        'environment_status',
        'history_event',
        'change_reason',
        'request_date',
        'issue_date',
        'first_use_date',
        'created_by',
    ];

    protected $casts = [
        'request_date' => 'datetime',
        'issue_date' => 'datetime',
        'first_use_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function subscriptionLicense(): BelongsTo
    {
        return $this->belongsTo(SubscriptionLicense::class, 'subscription_license_id', 'subscription_license_id');
    }

    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class, 'subscription_id', 'id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'company_id');
    }

    protected static function booted()
    {
        static::addGlobalScope(new SortingScope());

        static::creating(function ($model) {
            self::setCreatedUpdatedBy($model);
        });

        static::saving(function ($model) {
            self::setUpdatedBy($model);
        });

        static::deleting(function ($model) {
            self::setDeletedBy($model);
        });

        return parent::booted();
    }
}
