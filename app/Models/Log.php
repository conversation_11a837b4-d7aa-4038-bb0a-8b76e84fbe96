<?php

namespace App\Models;

use App\Scopes\SortingScope;
use App\Traits\Blamable;
use App\Traits\Timezones;
use Illuminate\Database\Eloquent\Concerns\HasTimestamps;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Log extends Model
{
    use HasFactory, HasTimestamps, Blamable, SoftDeletes, Timezones;

    protected $table = 'logs';

    /**
     * The primary key column name.
     *
     * @var string
     */
    protected $primaryKey = 'log_id';

    protected $fillable = [
        'level',
        'event',
        'description',
        'reason',
        'context',
        'status',
        'ip_address',
        'user_id',
        'email',
        'device_info',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by',
        'deleted_at',
        'deleted_by'
    ];

    protected $casts = [
        'context' => 'array',
        'device_info' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    protected static function booted()
    {
        static::addGlobalScope(new SortingScope());

        static::creating(function ($model) {
            self::setCreatedUpdatedBy($model);
        });

        static::saving(function ($model) {
            self::setUpdatedBy($model);
        });

        static::deleting(function ($model) {
            self::setDeletedBy($model);
        });

        return parent::booted();
    }
}
