<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\DefaultValues;
use App\Enums\SubscriptionLicenses;
use App\Enums\Subscriptions;
use App\Helpers\AppHelper;
use App\Helpers\BillingsHelper;
use App\Scopes\SortingScope;
use App\Services\GcsService;
use App\Services\MoneyFormatter;
use App\Traits\Blamable;
use App\Traits\Timezones;
use Carbon\Carbon;
use Cknow\Money\Money;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Arr;
use TomatoPHP\FilamentSubscriptions\Models\Subscription as BaseModel;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class Subscription extends BaseModel
{
    use Blamable, Timezones, HasSlug;

    public const DEFAULT_NAME = 'sonar';
    public const SUBSCRIPTION_TYPE_SAAS = 'saas';
    public const SUBSCRIPTION_TYPE_ON_PREM = 'on-prem';

    public static function getSubscriptionTypes(): array
    {
        return [
            self::SUBSCRIPTION_TYPE_SAAS => 'SaaS',
            self::SUBSCRIPTION_TYPE_ON_PREM => 'On Prem',
        ];
    }

    public function getFillable()
    {
        return [
            ...parent::getFillable(),
            'public_subscription_id',
            'name',
            'slug',
            'country_id',
            'billing_country_id',
            'currency_id',
            'suspension_reason_id',
            'license_type',
            'plan_type',
            'timezone',
            'last_payment_date',
            'subscription_status',
            'payment_status',
            'payment_review_status',
            'payment_rejection_comment',
            'erp_sales_order_id',
            'payment_amount',
            'payment_type',
            'auto_renew',
            'includes_disaster_recovery',
            'assigned_quota',
            'used_quota',
            'remaining_quota',
            'number_of_years',
            'created_at',
            'updated_at',
            'deleted_at',
            'created_by',
            'updated_by',
            'deleted_by'
        ];
    }

    public function getCasts()
    {
        return [
            ...parent::getCasts(),
            'last_payment_date' => 'datetime',
        ];
    }

    /**
     * The attributes that are translatable.
     *
     * @var array
     */
    public $translatable = [
        'name',
        'description',
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug');
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }

    public function suspensionReason(): BelongsTo
    {
        return $this->belongsTo(SuspensionReason::class, 'suspension_reason_id', 'suspension_reasons_id');
    }

    public function subscriber(): MorphTo
    {
        return $this->morphTo('subscriber', 'subscriber_type', 'subscriber_id', 'company_id');
    }

    public function subscriptionHistories(): HasMany
    {
        return $this->hasMany(SubscriptionHistory::class, 'subscription_id');
    }

    public function subscriptionPayments(): HasMany
    {
        return $this->hasMany(SubscriptionPayment::class, 'subscription_id');
    }

    /**
     * Get the latest payment for this subscription
     */
    public function subscriptionLatestPayment(): HasOne
    {
        return $this->hasOne(SubscriptionPayment::class, 'subscription_id')
            ->latest('created_at');
    }

    public function subscriptionReceipts(): HasMany
    {
        return $this->hasMany(SubscriptionReceipt::class, 'subscription_id');
    }

    public function subscriptionFeatures(): HasMany
    {
        return $this->hasMany(SubscriptionFeature::class, 'subscription_id');
    }

    public function subscriptionLicenses(): HasMany
    {
        return $this->hasMany(SubscriptionLicense::class, 'subscription_id');
    }

    public function subscriptionProgress(): HasMany
    {
        return $this->hasMany(SubscriptionProgress::class, 'subscription_id');
    }

    public function subscriptionLastProgressName()
    {
        return optional($this->subscriptionProgress()->latest()->first())->step_name;
    }

    /**
     * IS Subscription Renewable 
     */
    public function isRenewableFormatted(): string
    {
        $isRenewable = $this->auto_renew ? "On" : "Off";
        // $endsAt = !empty($this->ends_at) ? " - $this->ends_at_formatted_date" : "";

        return "{$isRenewable}";
    }

    /**
     * Subscription has disaster recovery 
     */
    public function hasDisasterRecovery(): string
    {
        if ($this->subscriptionHasFeaturePrefix('include-disaster-recovery')) {
            return "Included";
        }

        return "Not Included";
    }

    /**
     * Subscription has security add-on
     */
    public function hasSecurityAddon(): string
    {
        if ($this->subscriptionHasFeaturePrefix('security-add-on')) {
            return "Included";
        }

        return "Not Included";
    }

    /**
     * Subscription has premium support 
     */
    public function hasPremiumSupport(): string
    {
        if ($this->subscriptionHasFeaturePrefix('premium-support')) {
            return "Premium (unlimited Tickets) | 24/7 Support";
        }

        return "Standard (50 Tickets)";
    }

    /**
     * Get Subscription Model Name 
     */
    function subscriptionModelName(): string
    {
        if ($this->subscriptionHasFeaturePrefix('saas')) {
            return "Saas";
        }
        if ($this->subscriptionHasFeaturePrefix('on-prem')) {
            return "On Prem";
        }

        return "";
    }

    /**
     * Is Saas 
     */
    public function isSaas(): bool
    {
        return $this->subscriptionHasFeaturePrefix('saas');
    }

    /**
     * Subscription Has a Feature 
     */
    function subscriptionHasFeature(string $featureSlug): bool
    {
        return $this->subscriptionFeatures()
            ->where('value', true)
            ->whereHas('feature', function ($query) use ($featureSlug) {
                $query->where('slug', $featureSlug)
                    ->whereNull('deleted_at');
            })
            ->exists();
    }

    /**
     * Subscription Has a Feature Prefix 
     */
    public function subscriptionHasFeaturePrefix(string $prefix): bool
    {
        return $this->subscriptionFeatures()
            ->whereHas('feature', function ($query) use ($prefix) {
                $query->where('slug', 'like', $prefix . '-%')
                    ->orWhere('slug', $prefix);
            })
            ->where('value', true)
            ->exists();
    }

    /**
     * Get All Active Feature Names of the Subscription
     */
    function subscriptionFeatureNames(): array
    {
        return $this->subscriptionFeatures()
            ->where('value', true)
            ->whereHas('feature', function ($query) {
                $query->whereNull('deleted_at');
            })
            ->with('feature')
            ->get()
            ->mapWithKeys(function ($item) {
                $normalizedSlug = AppHelper::normalizeSlug($item->feature->slug);

                return [$normalizedSlug => $item->feature->name];
            })
            ->filter()
            ->all();
    }

    /**
     * Get Subscription Estimated Price  
     */
    public function estimatedPrice()
    {
        $addonIds = $this->usage()->pluck('plan_feature_id')->all();

        $priceData = BillingsHelper::calculateEstimatedPrice(
            planId: $this->plan_id,
            billingCountryId: $this->billing_country_id,
            newCurrencyId: $this->currency_id,
            addons: $addonIds,
            platformCountryId: $this->country_id,
            numberOfYears: $this->number_of_years,
            subscriptionId: $this->id
        );

        $moneyFormatter = app(MoneyFormatter::class);

        $out = [
            "total_before_discount" => $moneyFormatter->price($priceData['price']->getAmount(), $priceData['price']->getCurrency()->getCode())->format(),
            "discount_amount" => $moneyFormatter->price($priceData['discount']->getAmount(), $priceData['discount']->getCurrency()->getCode())->format(),
            "total_after_discount" => $moneyFormatter->price($priceData['total_discounted']->getAmount(), $priceData['total_discounted']->getCurrency()->getCode())->format(),
            "vat_percentage" => (int)($priceData['vat_percentage'] * 100) . '%',
            "vat_amount" => $moneyFormatter->price($priceData['vat_amount']->getAmount(), $priceData['vat_amount']->getCurrency()->getCode())->format(),
            "grand_total" => $moneyFormatter->price($priceData['grand_total']->getAmount(), $priceData['grand_total']->getCurrency()->getCode())->format(),
        ];

        return $out;
    }

    /**
     * Get Subscription amount 
     */
    public function totalPrice(bool $asArray = true)
    {
        $plan = $this->plan;
        if (empty($plan)) {
            if ($asArray) {
                $defaultCurrencyCode = DefaultValues::CURRENCY->get();
                return MoneyFormatter::price(0, $defaultCurrencyCode)->abbreviate()->asArray()->format();
            }
            return 0.00;
        }

        $addonIds = $this->usage()->pluck('plan_feature_id')->all();

        $priceData = BillingsHelper::calculateEstimatedPrice(
            planId: $this->plan_id,
            billingCountryId: $this->billing_country_id,
            newCurrencyId: $this->currency_id,
            addons: $addonIds,
            platformCountryId: $this->country_id,
            numberOfYears: $this->number_of_years
        );

        if (empty($priceData)) {
            return $asArray ? [] : 0.00;
        }

        $grand_total = $priceData['grand_total'];

        if (!$asArray) {
            return $grand_total->getAmount();
        }

        return MoneyFormatter::price((float) $grand_total->getAmount(), $grand_total->getCurrency()->getCode())
            ->abbreviate()
            ->asArray()
            ->format();
    }

    /**
     * Get Subscription Remaining Days 
     */
    public function remainingDays(): string
    {
        $out = "0 Days Remaining";

        $endDate = $this->trial_ends_at ?? $this->ends_at;

        if (!$endDate) {
            return $out;
        }

        $remainingDays = (int) Carbon::now()->diffInDays(Carbon::parse($endDate), false);

        if ($remainingDays < 0) {
            return $out;
        }

        $out = $remainingDays . " Days Remaining";

        return $out;
    }

    public function number_of_years()
    {
        if (!empty($this->number_of_years) && is_numeric($this->number_of_years) && $this->number_of_years > 0) {
            return (int)$this->number_of_years;
        }

        return DefaultValues::SUBSCRIPTION_LIFETIME_YEARS->get();
    }

    /**
     * Get subscription addons 
     */
    public function addons()
    {
        return collect($this->usage)->mapWithKeys(function ($item) {
            $normalizedSlug = AppHelper::normalizeSlug($item->feature->slug);
            return [$normalizedSlug => $item->plan_feature_id];
        });
    }

    /**
     * Billing address 
     */
    public function billingAddress()
    {
        return $this->subscriber?->addresses()->where('address_type', BillingsHelper::ADDRESS_TYPE_BILLING)->first() ?? null;
    }

    /**
     * Has Active Processes 
     */
    public function hasPendingProcesses(): bool
    {
        return $this->subscriptionLicenses()
            ->where('environment_status', SubscriptionLicenses::ENVIRONMENT_STATUS_PENDING->value)
            ->exists() ?? false;
    }

    /**
     * Get Active Process 
     */
    public function getActiveEnvironment()
    {
        return $this->subscriptionLicenses()
            ->whereNotNull('environment_status')
            ->first();
    }

    /**
     * Create Subscription History 
     */
    public function createHistory(Subscriptions $eventType): void
    {
        $modelData = SubscriptionHistory::mapParentColumns($this->toArray());

        SubscriptionHistory::updateOrCreate(
            [
                'subscription_id' => $this->id,
                'history_event' => $eventType->value,
                'subscription_status' => $this->subscription_status,
                'payment_status' => $this->payment_status,
            ],
            $modelData
        );
    }

    protected static function booted()
    {
        static::addGlobalScope(new SortingScope());

        static::creating(function ($model) {
            self::setCreatedUpdatedBy($model);
        });

        static::created(function ($model) {
            $model->public_subscription_id = AppHelper::getFormattedId((string) $model->id, DefaultValues::SUBSCRIPTION_PREFIX->get());
            $model->saveQuietly();
        });

        static::saving(function ($model) {
            self::setUpdatedBy($model);
        });

        static::deleting(function ($model) {
            self::setDeletedBy($model);
        });

        return parent::booted();
    }
}
