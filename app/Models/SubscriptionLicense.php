<?php

namespace App\Models;

use App\Scopes\SortingScope;
use App\Traits\Blamable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class SubscriptionLicense extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, Blamable, SoftDeletes;

    /**
     * The primary key column name.
     *
     * @var string
     */
    protected $primaryKey = 'subscription_license_id';

    /**
     * The table associated with the model.
     *
     * @var string|null
     */
    protected $table = 'subscription_licenses';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
    */
    protected $fillable = [
        'subscription_id',
        'company_id',
        'license_key',
        'license_type',
        'server_id',
        'server_type',
        'environment',
        'sonar_url',
        'sonar_username',
        'sonar_password',
        'sonar_api_token',
        'environment_status',
        'request_date',
        'issue_date',
        'issued_by',
        'first_use_date',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by',
        'deleted_at',
        'deleted_by'
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
            'request_date' => 'datetime',
            'issue_date' => 'datetime',
            'first_use_date' => 'datetime',
        ];
    }

    /**
     * Attributes to include in the Audit.
     *
     * @var array
     */
    // protected $auditInclude = [
    //     'title',
    //     'content',
    // ];

    public function subscription()
    {
        return $this->belongsTo(Subscription::class, 'subscription_id', 'id');
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'company_id');
    }

    public function history()
    {
        return $this->hasMany(SubscriptionLicenseHistory::class, 'subscription_license_id', 'subscription_license_id');
    }

    protected static function booted()
    {
        static::addGlobalScope(new SortingScope());

        static::creating(function ($model) {
            self::setCreatedUpdatedBy($model);
        });
        
        static::saving(function ($model) {
            self::setUpdatedBy($model);
        });

        static::deleting(function ($model) {
            self::setDeletedBy($model);
        });

        return parent::booted();
    }
}