<?php

namespace App\Providers;

use App\Console\Commands\MakeFilamentResource;
use App\Policies\AuditRelationManagerPolicy;
use Illuminate\Support\Facades\Vite;
use Illuminate\Support\ServiceProvider;
use SolutionForest\FilamentCms\Filament\RelationManagers\AuditRelationManager;

class AuthServiceProvider extends ServiceProvider
{

    protected $policies = [
        AuditRelationManager::class => AuditRelationManagerPolicy::class,
    ];
    
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
       //
    }
}
