<?php

namespace App\Jobs;

use App\Helpers\AppHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Process;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Client\RequestException;
use Google\ApiCore\ApiException;

// Secret Manager
use Google\Cloud\SecretManager\V1beta2\Client\SecretManagerServiceClient;
use Google\Cloud\SecretManager\V1beta2\GetSecretRequest as SecretManagerGetSecretRequest;
use Google\Cloud\SecretManager\V1beta2\CreateSecretRequest as SecretManagerCreateSecretRequest;
use Google\Cloud\SecretManager\V1beta2\AddSecretVersionRequest;
use Google\Cloud\SecretManager\V1beta2\Secret;
use Google\Cloud\SecretManager\V1beta2\SecretPayload;
use Google\Cloud\SecretManager\V1beta2\Replication;
use Google\Cloud\SecretManager\V1beta2\Replication\Automatic;

// Compute Engine (V1)
use Google\Cloud\Compute\V1\AddressesClient;
use Google\Cloud\Compute\V1\Address;

// Cloud SQL Admin API Clients (V1)
use Google\Cloud\Sql\V1\Client\SqlDatabasesServiceClient;
use Google\Cloud\Sql\V1\Client\SqlUsersServiceClient;
use Google\Cloud\Sql\V1\Database;
use Google\Cloud\Sql\V1\User;
use Google\Cloud\Sql\V1\SqlDatabasesInsertRequest;
use Google\Cloud\Sql\V1\SqlDatabasesGetRequest;
use Google\Cloud\Sql\V1\SqlUsersInsertRequest;
use Google\Cloud\Sql\V1\SqlUsersListRequest;

class ProvisionClientResourcesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected string $clientNameIdentifier;
    protected string $ipAddressName; // Name of the static IP resource
    protected string $dbName;
    protected string $dbUserName;

    public $tries = 3;
    public $timeout = 600;

    public function __construct(string $clientNameIdentifier, ?string $ipAddressName = null)
    {
        $this->clientNameIdentifier = $clientNameIdentifier;
        $slugifiedClientName = Str::slug($clientNameIdentifier);

        $this->ipAddressName = strtolower($ipAddressName ?: 'static-ip-' . $slugifiedClientName . '-' . Str::random(4));

        $baseName = strtolower(Str::slug($clientNameIdentifier, '_'));
        if (strlen($baseName) > 50) {
            $baseName = substr($baseName, 0, 50);
        }
        $this->dbName = $baseName . '_db';
        $this->dbUserName = $baseName . '_user';
    }

    public function handle(): void
    {
        $projectId = config('services.google-deployment.project_id');
        $credentialsPath = config('services.google-deployment.credentials');
        $computeRegion = config('services.google-deployment.region');
        $sqlInstanceId = config('services.google-deployment.sql_instance_id');

        $godaddyApiKey = config('services.godaddy.api_key');
        $godaddyApiSecret = config('services.godaddy.api_secret');
        $godaddyBaseDomain = config('services.godaddy.base_domain');
        $godaddyUseOte = config('services.godaddy.use_ote');
        $godaddyApiBaseUrl = $godaddyUseOte ? config('services.godaddy.ote_base_url') : config('services.godaddy.api_base_url');
        $godaddyDefaultTtl = config('services.godaddy.default_ttl', 600);

        $argoStubPath = base_path('stubs/argocd/argocd-application.stub');
        $kubeConfigPath = storage_path('app/private/kube/kubeconfig.yaml');
        $argoGeneratedDir = storage_path('app/argocd-generated/' . Str::slug($this->clientNameIdentifier));
        $argoGeneratedYamlPath = $argoGeneratedDir . '/' . Str::slug($this->clientNameIdentifier) . '-argocd-app.yaml';

        if (!$projectId || !$credentialsPath || !$computeRegion) {
            Log::error("[{$this->clientNameIdentifier}] Google Cloud Project ID, Credentials Path, or Compute Region is not configured.");
            $this->fail('Google Cloud configuration missing (Project, Credentials, Region).');
            return;
        }
        if (!$sqlInstanceId) {
            Log::error("[{$this->clientNameIdentifier}] Google Cloud SQL Instance ID (sql_instance_id) is not configured.");
            $this->fail('Google Cloud SQL Instance ID configuration missing.');
            return;
        }
        if (!$godaddyApiKey || !$godaddyApiSecret || !$godaddyBaseDomain || !$godaddyApiBaseUrl) {
            Log::error("[{$this->clientNameIdentifier}] GoDaddy API Key, Secret, Base Domain or API Base URL is not configured.");
            $this->fail('GoDaddy API configuration missing.');
            return;
        }

        // --- TEMPORARY: Call the listGoDaddyDomains method for debugging ---
        Log::info("[{$this->clientNameIdentifier}] Attempting to list GoDaddy domains for diagnostic purposes...");
        $availableDomains = $this->listGoDaddyDomains($godaddyApiBaseUrl, $godaddyApiKey, $godaddyApiSecret);

        if (empty($availableDomains)) {
            Log::warning("[{$this->clientNameIdentifier}] No domains were retrieved from GoDaddy or an error occurred. Check previous logs for details from listGoDaddyDomains method.");
        } else {
            Log::info("[{$this->clientNameIdentifier}] ----- Found GoDaddy Domains -----");
            foreach ($availableDomains as $index => $domain) {
                Log::info("[{$this->clientNameIdentifier}] Domain #" . ($index + 1) . ": " . $domain);
            }
            Log::info("[{$this->clientNameIdentifier}] -------------------------------");

            if (in_array($godaddyBaseDomain, $availableDomains)) {
                Log::info("[{$this->clientNameIdentifier}] VERIFIED: The configured base domain '{$godaddyBaseDomain}' IS PRESENT in the list of domains from GoDaddy.");
            } else {
                Log::error("[{$this->clientNameIdentifier}] ERROR: The configured base domain '{$godaddyBaseDomain}' IS NOT FOUND in the list of domains from GoDaddy: " . implode(', ', $availableDomains));
                // $this->fail("Configured base domain '{$godaddyBaseDomain}' not found in GoDaddy account domains list.");
                // return;
            }
        }

        exit;
        // --- END TEMPORARY DIAGNOSTIC CALL ---

        $authConfig = ['keyFilePath' => $credentialsPath];
        $secretManagerClient = null;
        $addressesClient = null;
        $sqlDatabasesClient = null;
        $sqlUsersClient = null;
        $clientUuid = null;
        $actualStaticIpAddress = null;
        $clientSlug = Str::slug($this->clientNameIdentifier);

        try {
            $clientUuid = (string) Str::uuid();
            Log::info("[{$this->clientNameIdentifier}] Generated Client UUID: {$clientUuid}");
            $uuidSecretId = strtoupper($clientSlug . '_WEB_SYSTEMPASSCODE_KEY');

            $secretManagerClient = new SecretManagerServiceClient($authConfig);
            $this->ensureSecretAndAddVersion($secretManagerClient, $projectId, $uuidSecretId, $clientUuid);
            Log::info("[{$this->clientNameIdentifier}] Client UUID stored in Secret Manager: {$uuidSecretId}");

            $addressesClient = new AddressesClient($authConfig);
            $actualStaticIpAddress = $this->ensureStaticIpAddress($addressesClient, $projectId, $computeRegion, $this->ipAddressName);
            Log::info("[{$this->clientNameIdentifier}] Ensured Static IP Address Resource '{$this->ipAddressName}' with IP: {$actualStaticIpAddress}");

            $subdomainName = $clientSlug;
            $fullDnsName = $subdomainName . '.' . $godaddyBaseDomain;
            Log::info("[{$this->clientNameIdentifier}] Ensuring GoDaddy DNS A record for {$fullDnsName} -> {$actualStaticIpAddress} using PATCH method.");
            $this->ensureGoDaddyDnsRecord(
                $godaddyApiBaseUrl,
                $godaddyApiKey,
                $godaddyApiSecret,
                $godaddyBaseDomain,
                $subdomainName,
                $actualStaticIpAddress,
                $godaddyDefaultTtl
            );
            Log::info("[{$this->clientNameIdentifier}] Successfully ensured GoDaddy DNS A record for {$fullDnsName} using PATCH method.");

            $dbPassword = Str::random(32);
            $dbPasswordSecretId = strtoupper($clientSlug . '_JDBC_SECRETPASSWORD_KEY');
            $this->ensureSecretAndAddVersion($secretManagerClient, $projectId, $dbPasswordSecretId, $dbPassword);
            Log::info("[{$this->clientNameIdentifier}] Database credentials (password only) stored in Secret Manager: {$dbPasswordSecretId}");

            $sqlDatabasesClient = new SqlDatabasesServiceClient($authConfig);
            $sqlUsersClient = new SqlUsersServiceClient($authConfig);

            Log::info("[{$this->clientNameIdentifier}] Provisioning Database '{$this->dbName}' and User '{$this->dbUserName}' in SQL instance '{$sqlInstanceId}'.");
            $this->ensureDatabaseAndUser(
                $sqlDatabasesClient,
                $sqlUsersClient,
                $projectId,
                $sqlInstanceId,
                $this->dbName,
                $this->dbUserName,
                $dbPassword
            );

            Log::info(
                "[{$this->clientNameIdentifier}] Successfully provisioned Google Cloud and DNS resources. " .
                    "UUID Secret: {$uuidSecretId}, IP Address Name: {$this->ipAddressName} (Actual IP: {$actualStaticIpAddress}), " .
                    "DNS Record: {$fullDnsName}, " .
                    "DB Credentials Secret: {$dbPasswordSecretId} (for DB '{$this->dbName}', User '{$this->dbUserName}' in instance '{$sqlInstanceId}')"
            );

            Log::info("[{$this->clientNameIdentifier}] Starting ArgoCD application deployment steps.");
            if (!File::exists($kubeConfigPath)) {
                Log::error("[{$this->clientNameIdentifier}] Kubeconfig file not found: {$kubeConfigPath}");
                throw new \RuntimeException("Kubeconfig file not found: {$kubeConfigPath}");
            }

            $domainForArgo = $fullDnsName;
            $replacements = [
                '{{client_name}}'         => $this->clientNameIdentifier,
                '{{db_name}}'             => $this->dbName,
                '{{db_username}}'         => $this->dbUserName,
                '{{domain}}'              => $domainForArgo,
                '{{static_ip_address}}'   => $actualStaticIpAddress,
            ];

            AppHelper::processStub($argoStubPath, $argoGeneratedYamlPath, $replacements, $this->clientNameIdentifier);

            $command = "kubectl --kubeconfig={$kubeConfigPath} apply -f {$argoGeneratedYamlPath}";
            Log::info("[{$this->clientNameIdentifier}] Applying ArgoCD application using command: {$command}");
            $process = Process::run($command);

            if ($process->successful()) {
                Log::info("[{$this->clientNameIdentifier}] Successfully applied ArgoCD application. Output: " . $process->output());
            } else {
                $errorOutput = $process->errorOutput();
                $stdOutput = $process->output();
                Log::error("[{$this->clientNameIdentifier}] Failed to apply ArgoCD application. Error Output: {$errorOutput}. Standard Output: {$stdOutput}");
                throw new \RuntimeException("kubectl apply failed for {$this->clientNameIdentifier}. Error: {$errorOutput} | Output: {$stdOutput}");
            }

            Log::info("[{$this->clientNameIdentifier}] Successfully provisioned all client resources, including ArgoCD application deployment.");
        } catch (\Exception $e) {
            Log::error("[{$this->clientNameIdentifier}] Critical failure during client resource provisioning: " . $e->getMessage(), ['exception' => $e]);
            $this->fail($e);
            return;
        } finally {
            if ($secretManagerClient) $secretManagerClient->close();
            if ($addressesClient) $addressesClient->close();
            if ($sqlDatabasesClient) $sqlDatabasesClient->close();
            if ($sqlUsersClient) $sqlUsersClient->close();
        }
    }

    protected function ensureSecretAndAddVersion(SecretManagerServiceClient $client, string $projectId, string $secretId, string $payloadValue): void
    {
        $formattedSecretName = SecretManagerServiceClient::secretName($projectId, $secretId);
        $formattedProjectName = SecretManagerServiceClient::projectName($projectId);

        try {
            Log::info("[{$this->clientNameIdentifier}] Checking for secret: {$formattedSecretName}");
            $getRequest = (new SecretManagerGetSecretRequest())->setName($formattedSecretName);
            $client->getSecret($getRequest);
            Log::info("[{$this->clientNameIdentifier}] Secret {$secretId} already exists.");
        } catch (ApiException $e) {
            if ($e->getStatus() === 'NOT_FOUND') {
                Log::info("[{$this->clientNameIdentifier}] Secret {$secretId} not found. Creating...");
                $secret = (new Secret())
                    ->setReplication((new Replication())->setAutomatic(new Automatic()));
                $createSecretRequest = (new SecretManagerCreateSecretRequest())
                    ->setParent($formattedProjectName)
                    ->setSecretId($secretId)
                    ->setSecret($secret);
                $client->createSecret($createSecretRequest);
                Log::info("[{$this->clientNameIdentifier}] Secret {$secretId} created.");
            } else {
                Log::error("[{$this->clientNameIdentifier}] Error checking/creating secret {$secretId}: " . $e->getMessage(), ['exception' => $e]);
                throw $e;
            }
        }

        $payload = (new SecretPayload())->setData($payloadValue);
        $addVersionRequest = (new AddSecretVersionRequest())
            ->setParent($formattedSecretName)
            ->setPayload($payload);
        $version = $client->addSecretVersion($addVersionRequest);
        Log::info("[{$this->clientNameIdentifier}] Added new version to secret {$secretId}. Version: {$version->getName()}");
    }

    protected function ensureStaticIpAddress(AddressesClient $client, string $projectId, string $region, string $ipAddressName): string
    {
        try {
            Log::info("[{$this->clientNameIdentifier}] Checking for static IP Address resource: {$ipAddressName} in region {$region}");
            $address = $client->get($ipAddressName, $projectId, $region);
            $actualIp = $address->getAddress();
            if (empty($actualIp)) {
                Log::warning("[{$this->clientNameIdentifier}] Static IP Address resource {$ipAddressName} exists but has no IP assigned yet.");
            } else {
                Log::info("[{$this->clientNameIdentifier}] Static IP Address resource {$ipAddressName} already exists in region {$region} with IP: {$actualIp}");
                return $actualIp;
            }
        } catch (ApiException $e) {
            $isNotFound = ($e->getCode() === 404 || $e->getCode() === 5 || strtoupper($e->getStatus()) === 'NOT_FOUND');
            if ($isNotFound) {
                Log::info("[{$this->clientNameIdentifier}] Static IP Address resource {$ipAddressName} not found in region {$region}. Creating...");
            } else {
                Log::error("[{$this->clientNameIdentifier}] Error checking for static IP resource {$ipAddressName}: " . $e->getMessage(), ['exception' => $e]);
                throw $e;
            }
        }

        $addressResource = (new Address())
            ->setName($ipAddressName)
            ->setAddressType('EXTERNAL');

        try {
            Log::info("[{$this->clientNameIdentifier}] Requesting creation of static IP Address resource {$ipAddressName} in region {$region}.");
            $operation = $client->insert($addressResource, $projectId, $region);
            Log::info("[{$this->clientNameIdentifier}] Static IP Address {$ipAddressName} creation initiated. Operation: {$operation->getName()}. Polling for IP assignment...");

            $attempts = 0;
            $maxAttempts = 24;
            $pollInterval = 5;

            while ($attempts < $maxAttempts) {
                sleep($pollInterval);
                try {
                    $address = $client->get($ipAddressName, $projectId, $region);
                    $actualIp = $address->getAddress();
                    if (!empty($actualIp)) {
                        Log::info("[{$this->clientNameIdentifier}] Static IP Address {$ipAddressName} created and IP assigned: {$actualIp} (Attempt " . ($attempts + 1) . ")");
                        return $actualIp;
                    }
                    Log::info("[{$this->clientNameIdentifier}] Polling for IP address {$ipAddressName}, attempt " . ($attempts + 1) . ". IP not yet assigned.");
                } catch (ApiException $eGet) {
                    Log::warning("[{$this->clientNameIdentifier}] Polling attempt " . ($attempts + 1) . " for IP {$ipAddressName} encountered an issue: " . $eGet->getMessage() . ". Continuing to poll.");
                    if ($eGet->getCode() !== 404 && strtoupper($eGet->getStatus()) !== 'NOT_FOUND' && $attempts > $maxAttempts / 2) {
                        Log::error("[{$this->clientNameIdentifier}] Persistent error while polling for IP {$ipAddressName}: " . $eGet->getMessage());
                    }
                }
                $attempts++;
            }
            Log::error("[{$this->clientNameIdentifier}] Failed to retrieve IP address for {$ipAddressName} after creation and polling.");
            throw new \RuntimeException("Failed to retrieve IP address for {$ipAddressName} after creation.");
        } catch (ApiException $e) {
            if (str_contains(strtoupper($e->getMessage()), 'ALREADY_EXISTS') || $e->getStatus() === 'ALREADY_EXISTS' || $e->getCode() === 6) {
                Log::info("[{$this->clientNameIdentifier}] Static IP Address {$ipAddressName} already exists (confirmed during creation attempt). Fetching its IP...");
                sleep(5);
                $existingAddress = $client->get($ipAddressName, $projectId, $region);
                $actualIp = $existingAddress->getAddress();
                if (empty($actualIp)) {
                    Log::error("[{$this->clientNameIdentifier}] Static IP Address {$ipAddressName} exists but failed to retrieve its IP value even after re-fetch.");
                    throw new \RuntimeException("Static IP Address {$ipAddressName} exists but IP value could not be retrieved.");
                }
                Log::info("[{$this->clientNameIdentifier}] Fetched existing Static IP Address {$ipAddressName} with IP: {$actualIp}");
                return $actualIp;
            } else {
                Log::error("[{$this->clientNameIdentifier}] Failed to create or ensure static IP {$ipAddressName}: " . $e->getMessage(), ['exception' => $e]);
                throw $e;
            }
        }
    }

    protected function ensureDatabaseAndUser(
        SqlDatabasesServiceClient $databasesClient,
        SqlUsersServiceClient $usersClient,
        string $projectId,
        string $instanceId,
        string $dbName,
        string $dbUserName,
        string $dbPassword
    ): void {
        try {
            Log::info("[{$this->clientNameIdentifier}] Checking if database '{$dbName}' exists in instance '{$instanceId}'.");
            $getDbRequest = (new SqlDatabasesGetRequest())
                ->setProject($projectId)
                ->setInstance($instanceId)
                ->setDatabase($dbName);
            $databasesClient->get($getDbRequest);
            Log::info("[{$this->clientNameIdentifier}] Database '{$dbName}' already exists in instance '{$instanceId}'.");
        } catch (ApiException $e) {
            if ($e->getStatus() === 'NOT_FOUND' || $e->getCode() === 5 || $e->getCode() === 404) {
                Log::info("[{$this->clientNameIdentifier}] Database '{$dbName}' not found in instance '{$instanceId}'. Creating...");
                $databaseResource = (new Database())
                    ->setName($dbName)
                    ->setCharset('UTF8')
                    ->setCollation('en_US.UTF8');
                $insertDbRequest = (new SqlDatabasesInsertRequest())
                    ->setProject($projectId)
                    ->setInstance($instanceId)
                    ->setBody($databaseResource);
                $operation = $databasesClient->insert($insertDbRequest);
                Log::info("[{$this->clientNameIdentifier}] Database '{$dbName}' creation requested. Operation: {$operation->getName()}. Waiting briefly...");
                sleep(20);
                Log::info("[{$this->clientNameIdentifier}] Database '{$dbName}' creation likely completed or in progress in '{$instanceId}'.");
            } else {
                Log::error("[{$this->clientNameIdentifier}] Unexpected error while checking for database '{$dbName}' in '{$instanceId}': " . $e->getMessage(), ['exception' => $e]);
                throw $e;
            }
        }

        $userExists = false;
        $existingUserHost = '%';

        try {
            Log::info("[{$this->clientNameIdentifier}] Listing users for instance '{$instanceId}' to check for '{$dbUserName}'.");
            $listUsersRequest = (new SqlUsersListRequest())
                ->setProject($projectId)
                ->setInstance($instanceId);
            $response = $usersClient->list($listUsersRequest);
            foreach ($response->getItems() as $user) {
                $currentHost = $user->getHost() ?: '%';
                if ($user->getName() === $dbUserName && $currentHost === '%') {
                    $userExists = true;
                    break;
                }
            }

            if ($userExists) {
                Log::info("[{$this->clientNameIdentifier}] User '{$dbUserName}' (host: '{$existingUserHost}') already exists for instance '{$instanceId}'. Updating password.");
                $userResourceWithNewPassword = (new User())
                    ->setName($dbUserName)
                    ->setHost($existingUserHost)
                    ->setPassword($dbPassword);
                $updateUserRequest = (new \Google\Cloud\Sql\V1\SqlUsersUpdateRequest())
                    ->setProject($projectId)
                    ->setInstance($instanceId)
                    ->setName($dbUserName)
                    ->setHost($existingUserHost)
                    ->setBody($userResourceWithNewPassword);
                $operation = $usersClient->update($updateUserRequest);
                Log::info("[{$this->clientNameIdentifier}] User '{$dbUserName}' password update requested. Operation: {$operation->getName()}. Waiting briefly...");
                sleep(10);
                Log::info("[{$this->clientNameIdentifier}] User '{$dbUserName}' password update likely completed for instance '{$instanceId}'.");
            } else {
                Log::info("[{$this->clientNameIdentifier}] User '{$dbUserName}' not found for instance '{$instanceId}'. Creating...");
                $userResource = (new User())
                    ->setName($dbUserName)
                    ->setPassword($dbPassword)
                    ->setHost('%');
                $insertUserRequest = (new SqlUsersInsertRequest())
                    ->setProject($projectId)
                    ->setInstance($instanceId)
                    ->setBody($userResource);
                $operation = $usersClient->insert($insertUserRequest);
                Log::info("[{$this->clientNameIdentifier}] User '{$dbUserName}' creation requested. Operation: {$operation->getName()}. Waiting briefly...");
                sleep(20);
                Log::info("[{$this->clientNameIdentifier}] User '{$dbUserName}' creation likely completed or in progress for instance '{$instanceId}'.");
            }
        } catch (ApiException $e) {
            Log::error("[{$this->clientNameIdentifier}] Error during user check/creation/update for '{$dbUserName}' in instance '{$instanceId}': " . $e->getMessage(), ['exception' => $e]);
            throw $e;
        }
        Log::info("[{$this->clientNameIdentifier}] Successfully ensured database '{$dbName}' and user '{$dbUserName}' (with updated password if existing) for instance '{$instanceId}'.");
    }

    /**
     * Ensures a DNS A record exists in GoDaddy using PATCH.
     * This method attempts to add or update a single A record.
     */
    protected function ensureGoDaddyDnsRecord(
        string $apiBaseUrl,
        string $apiKey,
        string $apiSecret,
        string $baseDomain,    // e.g., "jawda.ismena.com"
        string $subdomainName, // e.g., "client-slug"
        string $ipAddress,
        int $ttl = 600
    ): void {
        $fullDnsName = $subdomainName . '.' . $baseDomain;
        // API URL for PATCH - {type} and {name} are removed from the path
        $apiUrl = "{$apiBaseUrl}/v1/domains/{$baseDomain}/records";
        $authHeader = "sso-key {$apiKey}:{$apiSecret}";

        // Payload for PATCH: an array of record objects.
        // Each object must specify its type, name, data, and ttl.
        $payload = [[
            'type' => 'A',
            'name' => $subdomainName, // The subdomain part, e.g., "client-slug"
            'data' => $ipAddress,
            'ttl' => $ttl
        ]];

        Log::info("[{$this->clientNameIdentifier}] Attempting to add/update GoDaddy DNS A record for {$fullDnsName} -> {$ipAddress} using PATCH.");
        Log::debug("[{$this->clientNameIdentifier}] GoDaddy API PATCH Request Details:", [
            'url' => $apiUrl,
            'method' => 'PATCH',
            'payload' => $payload,
            'authorization_header_used' => !empty($authHeader)
        ]);

        try {
            // Use Http::patch now
            $response = Http::withHeaders([
                'Authorization' => $authHeader,
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ])->patch($apiUrl, $payload);

            // According to GoDaddy docs, PATCH /v1/domains/{domain}/records can return:
            // 200 OK on success
            // 202 Accepted if the request is deferred
            // Other error codes for failures.
            // We will consider 200 and 202 as success for sending the request.
            if ($response->successful() || $response->status() == 202) {
                Log::info("[{$this->clientNameIdentifier}] Successfully sent PATCH request to GoDaddy for {$fullDnsName}. Status: {$response->status()}");
            } else {
                // Throw an exception if the status is not 200 or 202.
                $response->throw();
            }

            // Verification Step: This part remains the same, as we still want to verify
            // the specific record (A, $subdomainName) exists and has the correct IP.
            // The GET endpoint for a specific record is still valid.
            $verifyApiUrl = "{$apiBaseUrl}/v1/domains/{$baseDomain}/records/A/{$subdomainName}";
            Log::info("[{$this->clientNameIdentifier}] Verifying GoDaddy DNS A record for {$fullDnsName} via GET {$verifyApiUrl}");

            // It might take a moment for the change to be queryable, especially if the PATCH was 202 Accepted.
            // A small delay might be beneficial before verification if issues arise.
            // For now, we proceed directly. Consider adding a short sleep if verification fails intermittently after 202.
            if ($response->status() == 202) {
                Log::info("[{$this->clientNameIdentifier}] PATCH request was accepted (202), adding a small delay before verification.");
                sleep(10); // Wait 10 seconds if request was deferred
            }


            $verifyResponse = Http::withHeaders([
                'Authorization' => $authHeader,
                'Accept' => 'application/json',
            ])->get($verifyApiUrl);

            $verifyResponse->throw(); // Throw for 4xx/5xx errors on verification

            $records = $verifyResponse->json();
            $foundAndVerified = false;
            if (is_array($records)) {
                foreach ($records as $record) {
                    if (
                        isset($record['type']) && strtoupper($record['type']) === 'A' && // Ensure case-insensitivity for type
                        isset($record['name']) && $record['name'] === $subdomainName &&
                        isset($record['data']) && $record['data'] === $ipAddress
                    ) {
                        $foundAndVerified = true;
                        break;
                    }
                }
            }

            if ($foundAndVerified) {
                Log::info("[{$this->clientNameIdentifier}] Successfully verified GoDaddy DNS A record for {$fullDnsName} points to {$ipAddress}.");
            } else {
                Log::error("[{$this->clientNameIdentifier}] Failed to verify GoDaddy DNS A record for {$fullDnsName} after PATCH. IP mismatch or record not found as expected.", [
                    'verification_url' => $verifyApiUrl,
                    'expected_ip' => $ipAddress,
                    'api_response_for_verification' => $records
                ]);
                throw new \RuntimeException("Failed to verify GoDaddy DNS A record for {$fullDnsName} points to {$ipAddress} after PATCH. API Response: " . json_encode($records));
            }
        } catch (RequestException $e) {
            $statusCode = $e->response ? $e->response->status() : 'N/A';
            $responseBody = $e->response ? $e->response->body() : 'N/A';
            Log::error("[{$this->clientNameIdentifier}] GoDaddy API PATCH request failed for {$fullDnsName}. Status: {$statusCode}. Error: " . $e->getMessage(), [
                'url' => $apiUrl,
                'payload_sent' => $payload,
                'response_body' => $responseBody,
                'exception' => $e->getTraceAsString()
            ]);
            throw new \RuntimeException("GoDaddy API PATCH request failed for {$fullDnsName}: " . $e->getMessage(), 0, $e);
        } catch (\Exception $e) {
            Log::error("[{$this->clientNameIdentifier}] An unexpected error occurred during GoDaddy DNS PATCH management for {$fullDnsName}: " . $e->getMessage(), [
                'url' => $apiUrl,
                'payload_sent' => $payload,
                'exception' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Lists domains available in the GoDaddy account.
     */
    protected function listGoDaddyDomains(
        string $apiBaseUrl,
        string $apiKey,
        string $apiSecret
    ): array {
        $apiUrl = "{$apiBaseUrl}/v1/domains";
        $authHeader = "sso-key {$apiKey}:{$apiSecret}";
        $domainNames = [];
        $logIdentifier = "[GoDaddyDomainList]";

        Log::info("{$logIdentifier} Attempting to list GoDaddy domains from {$apiBaseUrl}.");

        try {
            $response = Http::withHeaders([
                'Authorization' => $authHeader,
                'Accept' => 'application/json',
            ])->get($apiUrl);

            if ($response->status() == 401 || $response->status() == 403) {
                Log::error("{$logIdentifier} GoDaddy API authentication/authorization error (Status: {$response->status()}). Check API Key/Secret and permissions.", [
                    'response_body' => $response->body()
                ]);
                return [];
            }
            $response->throw();
            $domainsData = $response->json();

            if (is_array($domainsData)) {
                foreach ($domainsData as $domainInfo) {
                    if (isset($domainInfo['domain'])) {
                        $domainNames[] = $domainInfo['domain'];
                    }
                }
                if (empty($domainNames) && !empty($domainsData)) {
                     Log::info("{$logIdentifier} GoDaddy API returned data, but no domain names were extracted. Check response structure.", ['api_response' => $domainsData]);
                } else {
                    Log::info("{$logIdentifier} Successfully retrieved GoDaddy domains. Count: " . count($domainNames) . ". Domains: " . implode(', ', $domainNames));
                }
            } else {
                Log::warning("{$logIdentifier} GoDaddy list domains API did not return an array as expected.", ['api_response' => $domainsData]);
            }
            return $domainNames;

        } catch (RequestException $e) {
            $statusCode = $e->response ? $e->response->status() : 'N/A';
            $responseBody = $e->response ? $e->response->body() : 'N/A';
            Log::error("{$logIdentifier} GoDaddy API request to list domains failed. Status: {$statusCode}. Error: " . $e->getMessage(), [
                'response_body' => $responseBody,
                'exception_trace' => $e->getTraceAsString()
            ]);
            return [];
        } catch (\Exception $e) {
            Log::error("{$logIdentifier} An unexpected error occurred while listing GoDaddy domains: " . $e->getMessage(), ['exception_trace' => $e->getTraceAsString()]);
            return [];
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::critical("[{$this->clientNameIdentifier}] Job ProvisionClientResourcesJob FAILED: " . $exception->getMessage(), [
            'clientIdentifier' => $this->clientNameIdentifier,
            'ipAddressName' => $this->ipAddressName ?? 'N/A',
            'dbName' => $this->dbName ?? 'N/A',
            'dbUserName' => $this->dbUserName ?? 'N/A',
            'exception_class' => get_class($exception),
            'exception_trace' => $exception->getTraceAsString()
        ]);
    }
}
