<?php

namespace App\Jobs;

use App\Helpers\AppHelper;
use App\Services\CloudflareService; // Added
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Process;
use Google\ApiCore\ApiException;

// Secret Manager
use Google\Cloud\SecretManager\V1beta2\Client\SecretManagerServiceClient;
use Google\Cloud\SecretManager\V1beta2\GetSecretRequest as SecretManagerGetSecretRequest;
use Google\Cloud\SecretManager\V1beta2\CreateSecretRequest as SecretManagerCreateSecretRequest;
use Google\Cloud\SecretManager\V1beta2\AddSecretVersionRequest;
use Google\Cloud\SecretManager\V1beta2\Secret;
use Google\Cloud\SecretManager\V1beta2\SecretPayload;
use Google\Cloud\SecretManager\V1beta2\Replication;
use Google\Cloud\SecretManager\V1beta2\Replication\Automatic;

// Compute Engine (V1)
use Google\Cloud\Compute\V1\GlobalAddressesClient;
use Google\Cloud\Compute\V1\Address;

// Cloud SQL Admin API Clients (V1)
use Google\Cloud\Sql\V1\Client\SqlDatabasesServiceClient;
use Google\Cloud\Sql\V1\Client\SqlUsersServiceClient;
use Google\Cloud\Sql\V1\Database;
use Google\Cloud\Sql\V1\User;
use Google\Cloud\Sql\V1\SqlDatabasesInsertRequest;
use Google\Cloud\Sql\V1\SqlDatabasesGetRequest;
use Google\Cloud\Sql\V1\SqlUsersInsertRequest;
use Google\Cloud\Sql\V1\SqlUsersListRequest;
use Google\Cloud\Sql\V1\SqlUsersUpdateRequest;


class ProvisionClientResourcesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected string $clientNameIdentifier;

    protected string $ipAddressName; 

    protected string $dbName;

    protected string $dbUserName;

    protected bool $shouldNormalizeDomain;

    public $tries = 3;
    public $timeout = 600;

    public function __construct(string $clientNameIdentifier, bool $shouldNormalizeDomain = false, ?string $ipAddressName = null)
    {
        $this->clientNameIdentifier = Str::slug($clientNameIdentifier);

        Log::info("Initializing ProvisionClientResourcesJob for client: {$this->clientNameIdentifier}");

        $this->ipAddressName = strtolower($ipAddressName ?: 'static-ip-' . $this->clientNameIdentifier);

        $this->dbName = $this->clientNameIdentifier . '_db';
        $this->dbUserName = $this->clientNameIdentifier . '_user';
        $this->shouldNormalizeDomain = $shouldNormalizeDomain;
    }

    public function handle(CloudflareService $cloudflareService): void 
    {
        /**
        * configurations 
        */
        $projectId = config('services.google-deployment.project_id');
        $credentialsPath = config('services.google-deployment.credentials');
        $computeRegion = config('services.google-deployment.region');
        $sqlInstanceId = config('services.google-deployment.sql_instance_id');

        $cloudflareApiToken = config('services.cloudflare.api_token');
        $cloudflareZoneId = config('services.cloudflare.zone_id');
        $appBaseDomain = config('services.cloudflare.base_domain');
        $cloudflareDnsTtl = config('services.cloudflare.default_ttl', 1); 
        $cloudflareProxied = config('services.cloudflare.proxied_by_default', false);

        $argoStubPath = base_path('stubs/argocd/argocd-application.stub');
        $kubeConfigPath = storage_path('app/private/kube/kubeconfig.yaml');
        $argoGeneratedDir = storage_path('app/argocd-generated/' . $this->clientNameIdentifier);
        $argoGeneratedYamlPath = $argoGeneratedDir . '/' . $this->clientNameIdentifier . '-argocd-app.yaml';

        /**
        * Empty fields check 
        */
        if (!$projectId || !$credentialsPath || !$computeRegion) {
            Log::error("[{$this->clientNameIdentifier}] Google Cloud Project ID, Credentials Path, or Compute Region is not configured.");
            $this->fail('Google Cloud configuration missing (Project, Credentials, Region).');
            return;
        }
        if (!$sqlInstanceId) {
            Log::error("[{$this->clientNameIdentifier}] Google Cloud SQL Instance ID (sql_instance_id) is not configured.");
            $this->fail('Google Cloud SQL Instance ID configuration missing.');
            return;
        }

        if (!$cloudflareApiToken || !$cloudflareZoneId) {
            Log::error("[{$this->clientNameIdentifier}] Cloudflare API Token or Zone ID is not configured.");
            $this->fail('Cloudflare API configuration missing (API Token or Zone ID).');
            return;
        }
        if (!$appBaseDomain) {
            Log::error("[{$this->clientNameIdentifier}] Application Base Domain (services.cloudflare.base_domain) is not configured.");
            $this->fail('Application Base Domain configuration missing.');
            return;
        }

        /**
        * Variables 
        */
        $authConfig = ['keyFilePath' => $credentialsPath];
        $secretManagerClient = null;
        $addressesClient = null;
        $sqlDatabasesClient = null;
        $sqlUsersClient = null;
        $clientUuid = null;
        $actualStaticIpAddress = null;
        $clientSlug = $this->clientNameIdentifier;

        /**
        * Actions 
        */
        try {
            $clientUuid = (string) Str::uuid();
            Log::info("[{$this->clientNameIdentifier}] Generated Client UUID: {$clientUuid}");
            $uuidSecretId = strtoupper($clientSlug . '_WEB_SYSTEMPASSCODE_KEY');

            $secretManagerClient = new SecretManagerServiceClient($authConfig);
            $this->ensureSecretAndAddVersion($secretManagerClient, $projectId, $uuidSecretId, $clientUuid);
            Log::info("[{$this->clientNameIdentifier}] Client UUID stored in Secret Manager: {$uuidSecretId}");

            $addressesClient = new GlobalAddressesClient($authConfig);
            $actualStaticIpAddress = $this->ensureStaticIpAddress($addressesClient, $projectId, $this->ipAddressName);
            Log::info("[{$this->clientNameIdentifier}] Ensured Static IP Address Resource '{$this->ipAddressName}' with IP: {$actualStaticIpAddress}");

            // DNS record creation using Cloudflare
            $subdomainName = $clientSlug;
            $fullDnsName = $subdomainName . '.' . $appBaseDomain; 

            Log::info("[{$this->clientNameIdentifier}] Attempting to add Cloudflare DNS A record for {$fullDnsName} -> {$actualStaticIpAddress}");

            try {
                $dnsRecordResponse = $cloudflareService->addDNSRecord(
                    'A',
                    $fullDnsName,
                    $actualStaticIpAddress,
                    $cloudflareDnsTtl,    
                    $cloudflareProxied,
                    $this->shouldNormalizeDomain    
                );

                if (isset($dnsRecordResponse['success']) && $dnsRecordResponse['success'] === true) {
                    Log::info("[{$this->clientNameIdentifier}] Successfully added Cloudflare DNS A record for {$fullDnsName}. Response: " . json_encode($dnsRecordResponse['result'] ?? []));
                } else {
                    $errors = isset($dnsRecordResponse['errors']) ? json_encode($dnsRecordResponse['errors']) : 'Unknown error structure.';
                    $alreadyExistsError = false;
                    if (isset($dnsRecordResponse['errors']) && is_array($dnsRecordResponse['errors'])) {
                        foreach ($dnsRecordResponse['errors'] as $error) {
                            if (isset($error['code']) && ($error['code'] === 81057 || $error['code'] === 81058)) {
                                $alreadyExistsError = true;
                                break;
                            }
                        }
                    }

                    if ($alreadyExistsError) {
                        Log::warning("[{$this->clientNameIdentifier}] Cloudflare DNS A record for {$fullDnsName} could not be added because an identical or conflicting record already exists. Assuming it's correctly pointing. Errors: {$errors}");
                    } else {
                        Log::error("[{$this->clientNameIdentifier}] Failed to add Cloudflare DNS A record for {$fullDnsName}. Errors: {$errors}", ['api_response' => $dnsRecordResponse]);
                        throw new \RuntimeException("Failed to add Cloudflare DNS A record for {$fullDnsName}. Errors: " . $errors);
                    }
                }
            } catch (\Exception $e) { 
                Log::error("[{$this->clientNameIdentifier}] Error during Cloudflare DNS record creation for {$fullDnsName}: " . $e->getMessage(), ['exception' => $e]);
                throw new \RuntimeException("Error creating Cloudflare DNS record for {$fullDnsName}: " . $e->getMessage(), 0, $e);
            }


            $dbPassword = Str::random(32);
            $dbPasswordSecretId = strtoupper($clientSlug . '_JDBC_SECRETPASSWORD_KEY');
            $this->ensureSecretAndAddVersion($secretManagerClient, $projectId, $dbPasswordSecretId, $dbPassword);
            Log::info("[{$this->clientNameIdentifier}] Database credentials (password only) stored in Secret Manager: {$dbPasswordSecretId}");

            $sqlDatabasesClient = new SqlDatabasesServiceClient($authConfig);
            $sqlUsersClient = new SqlUsersServiceClient($authConfig);

            Log::info("[{$this->clientNameIdentifier}] Provisioning Database '{$this->dbName}' and User '{$this->dbUserName}' in SQL instance '{$sqlInstanceId}'.");
            $this->ensureDatabaseAndUser(
                $sqlDatabasesClient,
                $sqlUsersClient,
                $projectId,
                $sqlInstanceId,
                $this->dbName,
                $this->dbUserName,
                $dbPassword
            );

            Log::info(
                "[{$this->clientNameIdentifier}] Successfully provisioned Google Cloud and DNS resources. " .
                    "UUID Secret: {$uuidSecretId}, IP Address Name: {$this->ipAddressName} (Actual IP: {$actualStaticIpAddress}), " .
                    "DNS Record (Cloudflare): {$fullDnsName}, " .
                    "DB Credentials Secret: {$dbPasswordSecretId} (for DB '{$this->dbName}', User '{$this->dbUserName}' in instance '{$sqlInstanceId}')"
            );

            Log::info("[{$this->clientNameIdentifier}] Starting ArgoCD application deployment steps.");
            if (!File::exists($kubeConfigPath)) {
                Log::error("[{$this->clientNameIdentifier}] Kubeconfig file not found: {$kubeConfigPath}");
                throw new \RuntimeException("Kubeconfig file not found: {$kubeConfigPath}");
            }

            // Remove "-{id}" suffix from clientSlug for Argo domain
            $domainForArgo = $this->shouldNormalizeDomain ? AppHelper::normalizeDomain($clientSlug . '.' . $appBaseDomain) : $clientSlug . '.' . $appBaseDomain;

            $replacements = [
                '{{client_name}}'         => $this->clientNameIdentifier,
                '{{db_name}}'             => $this->dbName,
                '{{db_username}}'         => $this->dbUserName,
                '{{domain}}'              => $domainForArgo,
                '{{static_ip_address}}'   => $this->ipAddressName,
            ];

            AppHelper::processStub($argoStubPath, $argoGeneratedYamlPath, $replacements, $this->clientNameIdentifier);

            $command = "kubectl --kubeconfig={$kubeConfigPath} apply -f {$argoGeneratedYamlPath}";
            Log::info("[{$this->clientNameIdentifier}] Applying ArgoCD application using command: {$command}");
            $process = Process::run($command);

            if ($process->successful()) {
                Log::info("[{$this->clientNameIdentifier}] Successfully applied ArgoCD application. Output: " . $process->output());
            } else {
                $errorOutput = $process->errorOutput();
                $stdOutput = $process->output();
                Log::error("[{$this->clientNameIdentifier}] Failed to apply ArgoCD application. Error Output: {$errorOutput}. Standard Output: {$stdOutput}");
                throw new \RuntimeException("kubectl apply failed for {$this->clientNameIdentifier}. Error: {$errorOutput} | Output: {$stdOutput}");
            }

            Log::info("[{$this->clientNameIdentifier}] Successfully provisioned all client resources, including ArgoCD application deployment.");
        } catch (\Exception $e) {
            Log::error("[{$this->clientNameIdentifier}] Critical failure during client resource provisioning: " . $e->getMessage(), ['exception' => $e]);
            $this->fail($e);
            return;
        } finally {
            if ($secretManagerClient) $secretManagerClient->close();
            if ($addressesClient) $addressesClient->close();
            if ($sqlDatabasesClient) $sqlDatabasesClient->close();
            if ($sqlUsersClient) $sqlUsersClient->close();
        }
    }

    protected function ensureSecretAndAddVersion(SecretManagerServiceClient $client, string $projectId, string $secretId, string $payloadValue): void
    {
        $formattedSecretName = SecretManagerServiceClient::secretName($projectId, $secretId);
        $formattedProjectName = SecretManagerServiceClient::projectName($projectId);

        try {
            Log::info("[{$this->clientNameIdentifier}] Checking for secret: {$formattedSecretName}");
            $getRequest = (new SecretManagerGetSecretRequest())->setName($formattedSecretName);
            $client->getSecret($getRequest);
            Log::info("[{$this->clientNameIdentifier}] Secret {$secretId} already exists.");
        } catch (ApiException $e) {
            if ($e->getStatus() === 'NOT_FOUND') {
                Log::info("[{$this->clientNameIdentifier}] Secret {$secretId} not found. Creating...");
                $secret = (new Secret())
                    ->setReplication((new Replication())->setAutomatic(new Automatic()));
                $createSecretRequest = (new SecretManagerCreateSecretRequest())
                    ->setParent($formattedProjectName)
                    ->setSecretId($secretId)
                    ->setSecret($secret);
                $client->createSecret($createSecretRequest);
                Log::info("[{$this->clientNameIdentifier}] Secret {$secretId} created.");
            } else {
                Log::error("[{$this->clientNameIdentifier}] Error checking/creating secret {$secretId}: " . $e->getMessage(), ['exception' => $e]);
                throw $e;
            }
        }

        $payload = (new SecretPayload())->setData($payloadValue);
        $addVersionRequest = (new AddSecretVersionRequest())
            ->setParent($formattedSecretName)
            ->setPayload($payload);
        $version = $client->addSecretVersion($addVersionRequest);
        Log::info("[{$this->clientNameIdentifier}] Added new version to secret {$secretId}. Version: {$version->getName()}");
    }

    protected function ensureStaticIpAddress(GlobalAddressesClient $client, string $projectId, string $ipAddressName): string
    {
        try {
            Log::info("[{$this->clientNameIdentifier}] Checking for GLOBAL static IP Address resource: {$ipAddressName}");
            $address = $client->get($ipAddressName, $projectId);
            $actualIp = $address->getAddress();

            if (empty($actualIp)) {
                Log::warning("[{$this->clientNameIdentifier}] Global Static IP Address resource {$ipAddressName} exists but has no IP assigned yet. Will attempt to ensure.");
            } else {
                Log::info("[{$this->clientNameIdentifier}] Global Static IP Address resource {$ipAddressName} already exists with IP: {$actualIp}");
                return $actualIp;
            }
        } catch (ApiException $e) {
            $isNotFound = ($e->getCode() === 404 || $e->getCode() === 5 || strtoupper($e->getStatus()) === 'NOT_FOUND');
            if ($isNotFound) {
                Log::info("[{$this->clientNameIdentifier}] Global Static IP Address resource {$ipAddressName} not found. Creating...");
            } else {
                Log::error("[{$this->clientNameIdentifier}] Error checking for global static IP resource {$ipAddressName}: " . $e->getMessage(), ['exception' => $e]);
                throw $e;
            }
        }

        $addressResource = (new Address())
            ->setName($ipAddressName)
            ->setAddressType('EXTERNAL'); 

        try {
            Log::info("[{$this->clientNameIdentifier}] Requesting creation of GLOBAL static IP Address resource {$ipAddressName}.");
            $operation = $client->insert($addressResource, $projectId);
            Log::info("[{$this->clientNameIdentifier}] Global Static IP Address {$ipAddressName} creation initiated. Operation: {$operation->getName()}. Polling for IP assignment...");

            $attempts = 0;
            $maxAttempts = 30;
            $pollInterval = 6; 

            while ($attempts < $maxAttempts) {
                sleep($pollInterval);
                try {
                    $address = $client->get($ipAddressName, $projectId);
                    $actualIp = $address->getAddress();
                    if (!empty($actualIp)) {
                        Log::info("[{$this->clientNameIdentifier}] Global Static IP Address {$ipAddressName} created and IP assigned: {$actualIp} (Attempt " . ($attempts + 1) . ")");
                        return $actualIp;
                    }
                    Log::info("[{$this->clientNameIdentifier}] Polling for GLOBAL IP address {$ipAddressName}, attempt " . ($attempts + 1) . ". IP not yet assigned.");
                } catch (ApiException $eGet) {
                    Log::warning("[{$this->clientNameIdentifier}] Polling attempt " . ($attempts + 1) . " for GLOBAL IP {$ipAddressName} encountered an issue: " . $eGet->getMessage() . ". Continuing to poll.");
                    if ($eGet->getCode() !== 404 && strtoupper($eGet->getStatus()) !== 'NOT_FOUND' && $attempts > $maxAttempts / 2) {
                        Log::error("[{$this->clientNameIdentifier}] Persistent error while polling for GLOBAL IP {$ipAddressName}: " . $eGet->getMessage());
                    }
                }
                $attempts++;
            }
            Log::error("[{$this->clientNameIdentifier}] Failed to retrieve IP address for GLOBAL {$ipAddressName} after creation and polling.");
            throw new \RuntimeException("Failed to retrieve IP address for GLOBAL {$ipAddressName} after creation.");
        } catch (ApiException $e) {
            if (str_contains(strtoupper($e->getMessage()), 'ALREADY_EXISTS') || $e->getStatus() === 'ALREADY_EXISTS' || $e->getCode() === 6) {
                Log::info("[{$this->clientNameIdentifier}] Global Static IP Address {$ipAddressName} already exists (confirmed during creation attempt). Fetching its IP...");
                sleep($pollInterval);
                $existingAddress = $client->get($ipAddressName, $projectId);
                $actualIp = $existingAddress->getAddress();
                if (empty($actualIp)) {
                    Log::error("[{$this->clientNameIdentifier}] Global Static IP Address {$ipAddressName} exists but failed to retrieve its IP value even after re-fetch.");
                    throw new \RuntimeException("Global Static IP Address {$ipAddressName} exists but IP value could not be retrieved.");
                }
                Log::info("[{$this->clientNameIdentifier}] Fetched existing Global Static IP Address {$ipAddressName} with IP: {$actualIp}");
                return $actualIp;
            } else {
                Log::error("[{$this->clientNameIdentifier}] Failed to create or ensure global static IP {$ipAddressName}: " . $e->getMessage(), ['exception' => $e]);
                throw $e;
            }
        }
    }

    protected function ensureDatabaseAndUser(
        SqlDatabasesServiceClient $databasesClient,
        SqlUsersServiceClient $usersClient,
        string $projectId,
        string $instanceId,
        string $dbName,
        string $dbUserName,
        string $dbPassword
    ): void {
        try {
            Log::info("[{$this->clientNameIdentifier}] Checking if database '{$dbName}' exists in instance '{$instanceId}'.");
            $getDbRequest = (new SqlDatabasesGetRequest())
                ->setProject($projectId)
                ->setInstance($instanceId)
                ->setDatabase($dbName);
            $databasesClient->get($getDbRequest);
            Log::info("[{$this->clientNameIdentifier}] Database '{$dbName}' already exists in instance '{$instanceId}'.");
        } catch (ApiException $e) {
            if ($e->getStatus() === 'NOT_FOUND' || $e->getCode() === 5 || $e->getCode() === 404) {
                Log::info("[{$this->clientNameIdentifier}] Database '{$dbName}' not found in instance '{$instanceId}'. Creating...");
                $databaseResource = (new Database())
                    ->setName($dbName)
                    ->setCharset('UTF8')
                    ->setCollation('en_US.UTF8');
                $insertDbRequest = (new SqlDatabasesInsertRequest())
                    ->setProject($projectId)
                    ->setInstance($instanceId)
                    ->setBody($databaseResource);
                $operation = $databasesClient->insert($insertDbRequest);
                Log::info("[{$this->clientNameIdentifier}] Database '{$dbName}' creation requested. Operation: {$operation->getName()}. Waiting briefly...");
                sleep(20); 
                Log::info("[{$this->clientNameIdentifier}] Database '{$dbName}' creation likely completed or in progress in '{$instanceId}'.");
            } else {
                Log::error("[{$this->clientNameIdentifier}] Unexpected error while checking for database '{$dbName}' in '{$instanceId}': " . $e->getMessage(), ['exception' => $e]);
                throw $e;
            }
        }

        $userExists = false;
        $existingUserHost = '%';

        try {
            Log::info("[{$this->clientNameIdentifier}] Listing users for instance '{$instanceId}' to check for '{$dbUserName}'.");
            $listUsersRequest = (new SqlUsersListRequest())
                ->setProject($projectId)
                ->setInstance($instanceId);
            $response = $usersClient->list($listUsersRequest);
            foreach ($response->getItems() as $user) {
                $currentHost = $user->getHost() ?: '%'; 
                if ($user->getName() === $dbUserName && $currentHost === '%') { 
                    $userExists = true;
                    break;
                }
            }

            if ($userExists) {
                Log::info("[{$this->clientNameIdentifier}] User '{$dbUserName}' (host: '{$existingUserHost}') already exists for instance '{$instanceId}'. Updating password.");
                $userResourceWithNewPassword = (new User())
                    ->setName($dbUserName)
                    ->setHost($existingUserHost)
                    ->setPassword($dbPassword); 

                $updateUserRequest = (new SqlUsersUpdateRequest())
                    ->setProject($projectId)
                    ->setInstance($instanceId)
                    ->setName($dbUserName)
                    ->setHost($existingUserHost) 
                    ->setBody($userResourceWithNewPassword);
                $operation = $usersClient->update($updateUserRequest);
                Log::info("[{$this->clientNameIdentifier}] User '{$dbUserName}' password update requested. Operation: {$operation->getName()}. Waiting briefly...");
                sleep(10);
                Log::info("[{$this->clientNameIdentifier}] User '{$dbUserName}' password update likely completed for instance '{$instanceId}'.");
            } else {
                Log::info("[{$this->clientNameIdentifier}] User '{$dbUserName}' not found for instance '{$instanceId}'. Creating...");
                $userResource = (new User())
                    ->setName($dbUserName)
                    ->setPassword($dbPassword)
                    ->setHost('%'); 
                $insertUserRequest = (new SqlUsersInsertRequest())
                    ->setProject($projectId)
                    ->setInstance($instanceId)
                    ->setBody($userResource);
                $operation = $usersClient->insert($insertUserRequest);
                Log::info("[{$this->clientNameIdentifier}] User '{$dbUserName}' creation requested. Operation: {$operation->getName()}. Waiting briefly...");
                sleep(20);
                Log::info("[{$this->clientNameIdentifier}] User '{$dbUserName}' creation likely completed or in progress for instance '{$instanceId}'.");
            }
        } catch (ApiException $e) {
            Log::error("[{$this->clientNameIdentifier}] Error during user check/creation/update for '{$dbUserName}' in instance '{$instanceId}': " . $e->getMessage(), ['exception' => $e]);
            throw $e;
        }
        Log::info("[{$this->clientNameIdentifier}] Successfully ensured database '{$dbName}' and user '{$dbUserName}' (with updated password if existing) for instance '{$instanceId}'.");
    }

    public function failed(\Throwable $exception): void
    {
        Log::critical("[{$this->clientNameIdentifier}] Job ProvisionClientResourcesJob FAILED: " . $exception->getMessage(), [
            'clientIdentifier' => $this->clientNameIdentifier,
            'ipAddressName' => $this->ipAddressName ?? 'N/A',
            'dbName' => $this->dbName ?? 'N/A',
            'dbUserName' => $this->dbUserName ?? 'N/A',
            'exception_class' => get_class($exception),
            'exception_trace' => $exception->getTraceAsString()
        ]);
    }
}
