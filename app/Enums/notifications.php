<?php

namespace App\Enums;

use App\Traits\EnumLabels;

enum Notifications: string
{
    use EnumLabels;

    // NOTIFICATION CHANNELS
    case NOTIFICATION_CHANNEL_MAIL = 'mail';
    case NOTIFICATION_CHANNEL_DATABASE = 'database';
    case NOTIFICATION_CHANNEL_SMS = 'sms';
    case NOTIFICATION_CHANNEL_WHATSAPP = 'whatsapp';
    case NOTIFICATION_CHANNEL_CUSTOMER_PORTAL = 'customer_portal';

    // NOTIFICATION EVENT TYPES
    case NOTIFICATION_EVENT_ACCOUNT_CREATED = 'account_created';
    case NOTIFICATION_EVENT_ACCOUNT_LOCKED = 'account_locked';
    case NOTIFICATION_EVENT_ACCOUNT_REACTIVATED = 'account_reactivated';
    case NOTIFICATION_EVENT_ACCOUNT_ROLE_CHANGED = 'account_role_changed';
    case NOTIFICATION_EVENT_ACCOUNT_PASSWORD_RESET = 'account_password_reset';
    case NOTIFICATION_EVENT_ACCOUNT_INVITATION_SENT = 'account_invitation_sent';
    case NOTIFICATION_EVENT_ACCOUNT_OTP_SENT = 'account_otp_sent';
    case NOTIFICATION_EVENT_ASSIGNED_LICENSE = 'assigned_license';
    case NOTIFICATION_EVENT_PROVIDED_LICENSE = 'provided_license';
    case NOTIFICATION_EVENT_RECEIPT_APPROVED = 'receipt_approved';
    case NOTIFICATION_EVENT_RECEIPT_REJECTED = 'receipt_rejected';
    case NOTIFICATION_EVENT_SUBSCRIPTION_ACTIVATED = 'susbcription_activated';

    /**
     * Get the label for the enum case.
     *
     * @return string
     */
    public function label(): string
    {
        return match ($this) {

            // NOTIFICATION CHANNELS
            self::NOTIFICATION_CHANNEL_MAIL => 'Mail',
            self::NOTIFICATION_CHANNEL_DATABASE => 'Database',
            self::NOTIFICATION_CHANNEL_SMS => 'SMS',
            self::NOTIFICATION_CHANNEL_WHATSAPP => 'WhatsApp',
            self::NOTIFICATION_CHANNEL_CUSTOMER_PORTAL => 'Customer Portal',

            // NOTIFICATION EVENT TYPES
            self::NOTIFICATION_EVENT_ACCOUNT_CREATED => 'Account Created',
            self::NOTIFICATION_EVENT_ACCOUNT_LOCKED => 'Account Locked',
            self::NOTIFICATION_EVENT_ACCOUNT_REACTIVATED => 'Account Reactivated',
            self::NOTIFICATION_EVENT_ACCOUNT_ROLE_CHANGED => 'Account Role Changed',
            self::NOTIFICATION_EVENT_ACCOUNT_PASSWORD_RESET => 'Account Password Reset',
            self::NOTIFICATION_EVENT_ACCOUNT_INVITATION_SENT => 'Account Invitation Sent',
            self::NOTIFICATION_EVENT_ACCOUNT_OTP_SENT => 'Account OTP Sent',
            self::NOTIFICATION_EVENT_ASSIGNED_LICENSE => 'Assigned License',
            self::NOTIFICATION_EVENT_PROVIDED_LICENSE => 'Provided License',
            self::NOTIFICATION_EVENT_RECEIPT_APPROVED => 'Receipt Approved',
            self::NOTIFICATION_EVENT_RECEIPT_REJECTED => 'Receipt Rejected',
            self::NOTIFICATION_EVENT_SUBSCRIPTION_ACTIVATED => 'Subscription Activated',

            default => $this->value,
        };
    }

    /**
    * Get notification channel options. 
    */
    public static function getNotificationChannelOptions(): array
    {
        return [
            self::NOTIFICATION_CHANNEL_MAIL->value => self::NOTIFICATION_CHANNEL_MAIL->label(),
            self::NOTIFICATION_CHANNEL_DATABASE->value => self::NOTIFICATION_CHANNEL_DATABASE->label(),
            self::NOTIFICATION_CHANNEL_SMS->value => self::NOTIFICATION_CHANNEL_SMS->label(),
            self::NOTIFICATION_CHANNEL_WHATSAPP->value => self::NOTIFICATION_CHANNEL_WHATSAPP->label(),
            self::NOTIFICATION_CHANNEL_CUSTOMER_PORTAL->value => self::NOTIFICATION_CHANNEL_CUSTOMER_PORTAL->label(),
        ];
    }

    /**
     * Get notification event options.
     */
    public static function getNotificationEventOptions(): array
    {
        return [
            self::NOTIFICATION_EVENT_ACCOUNT_CREATED->value => self::NOTIFICATION_EVENT_ACCOUNT_CREATED->label(),
            self::NOTIFICATION_EVENT_ACCOUNT_LOCKED->value => self::NOTIFICATION_EVENT_ACCOUNT_LOCKED->label(),
            self::NOTIFICATION_EVENT_ACCOUNT_REACTIVATED->value => self::NOTIFICATION_EVENT_ACCOUNT_REACTIVATED->label(),
            self::NOTIFICATION_EVENT_ACCOUNT_ROLE_CHANGED->value => self::NOTIFICATION_EVENT_ACCOUNT_ROLE_CHANGED->label(),
            self::NOTIFICATION_EVENT_ACCOUNT_PASSWORD_RESET->value => self::NOTIFICATION_EVENT_ACCOUNT_PASSWORD_RESET->label(),
            self::NOTIFICATION_EVENT_ACCOUNT_INVITATION_SENT->value => self::NOTIFICATION_EVENT_ACCOUNT_INVITATION_SENT->label(),
            self::NOTIFICATION_EVENT_ACCOUNT_OTP_SENT->value => self::NOTIFICATION_EVENT_ACCOUNT_OTP_SENT->label(),
            self::NOTIFICATION_EVENT_ASSIGNED_LICENSE->value => self::NOTIFICATION_EVENT_ASSIGNED_LICENSE->label(),
            self::NOTIFICATION_EVENT_PROVIDED_LICENSE->value => self::NOTIFICATION_EVENT_PROVIDED_LICENSE->label(),
            self::NOTIFICATION_EVENT_RECEIPT_APPROVED->value => self::NOTIFICATION_EVENT_RECEIPT_APPROVED->label(),
            self::NOTIFICATION_EVENT_RECEIPT_REJECTED->value => self::NOTIFICATION_EVENT_RECEIPT_REJECTED->label(),
            self::NOTIFICATION_EVENT_SUBSCRIPTION_ACTIVATED->value => self::NOTIFICATION_EVENT_SUBSCRIPTION_ACTIVATED->label(),
        ];
    }

}
