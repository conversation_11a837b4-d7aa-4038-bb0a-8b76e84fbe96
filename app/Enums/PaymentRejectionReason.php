<?php

namespace App\Enums;

enum PaymentRejectionReason: string
{
    case INCORRECT_AMOUNT = 'Incorrect Amount';
    case INVALID_RECEIPT = 'Invalid Receipt';
    case REJECTED_BY_BANK = 'Rejected By Bank';
    case PAYMENT_NOT_RECEIVED = 'Payment Not Received';
    case DUPLICATE_PAYMENT = 'Duplicate Payment';
    case INCORRECT_CURRENCY = 'Incorrect Currency';
    case INCOMPLETE_INFORMATION = 'Incomplete Information';
    case PAYMENT_METHOD_NOT_ACCEPTED = 'Payment Method Not Accepted';
    case OTHER = 'Other';

    /**
     * Get all values as an array
     *
     * @return array<string, string>
     */
    public static function getValues(): array
    {
        return array_column(self::cases(), 'value', 'name');
    }

    /**
     * Get the description for a reason
     */
    public function getDescription(): string
    {
        return match($this) {
            self::INCORRECT_AMOUNT => 'The payment amount does not match the invoice amount.',
            self::INVALID_RECEIPT => 'The receipt provided is invalid or cannot be verified.',
            self::REJECTED_BY_BANK => 'The bank has rejected the payment.',
            self::PAYMENT_NOT_RECEIVED => 'The payment was not received in our account.',
            self::DUPLICATE_PAYMENT => 'This appears to be a duplicate payment submission.',
            self::INCORRECT_CURRENCY => 'The payment was made in an incorrect currency.',
            self::INCOMPLETE_INFORMATION => 'The payment information provided is incomplete.',
            self::PAYMENT_METHOD_NOT_ACCEPTED => 'The payment method used is not accepted.',
            self::OTHER => 'Other reason (will be specified in comments).',
        };
    }
}
