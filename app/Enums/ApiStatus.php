<?php

namespace App\Enums;

enum ApiStatus: int
{
    case SUCCESS = 200;
    case CREATED = 201;
    case BAD_REQUEST = 400;
    case UNAUTHORIZED = 401;
    case FORBIDDEN = 403;
    case NOT_FOUND = 404;
    case METHOD_NOT_ALLOWED = 405;
    case TOO_MANY_REQUESTS = 429;
    case SERVER_ERROR = 500;
    
    public function description(): string
    {
        return match($this) {
            self::SUCCESS => 'Success',
            self::CREATED => 'Created',
            self::BAD_REQUEST => 'Bad request',
            self::UNAUTHORIZED => 'Unauthorized',
            self::FORBIDDEN => 'Forbidden',
            self::METHOD_NOT_ALLOWED => 'Method not allowed',
            self::NOT_FOUND => 'Not found',
            self::SERVER_ERROR => 'Internal server error',
            self::TOO_MANY_REQUESTS => 'Too Many Requests',
        };
    }
}
