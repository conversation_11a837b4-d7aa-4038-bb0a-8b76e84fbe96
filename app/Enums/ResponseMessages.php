<?php

namespace App\Enums;

use Illuminate\Support\Facades\Lang;

enum ResponseMessages: string
{
    // CUSTOMERS
    case CUSTOMER_CREATED = "customer_created";
    case CUSTOMER_UPDATED = "customer_updated";

    // CUSTOMER USERS
    case USER_CREATED = "user_created";
    case USER_UPDATED = "user_updated";
    case USER_SUSPENDED = "user_suspended";
    case USER_REACTIVATED = "user_reactivated";

    // INVITATIONS
    case INVITATION_SENT = "invitation_sent";

    public function description(?string $lang = null): string
    {
        $lang = $lang ?: app()->getLocale(); 
        return Lang::get("response.messages." . $this->value, [], $lang);
    }
}