<?php

namespace App\Enums;

enum ApiSettings: string
{
    // JWT
    case JWT_TOKEN_TTL_MINUTES = 'jwt_token_ttl_minutes';

    // OTP
    case OTP_RENEW_MINUTES = 'otp_renew_minutes';
    case OTP_LOCKING_MINUTES = 'otp_locking_minutes';
    case OTP_EXPIRY_MINUTES = 'otp_expiry_minutes';
    case OTP_DEFAULT_RANGE = 'otp_default_range';
    case OTP_LOCKING_FAILED_ATTEMPTS = 'otp_locking_failed_attempts';

    // USER
    case USER_ACCOUNT_LOCKING_MINUTES = 'user_account_locking_minutes';
    case USER_ACCOUNT_LOCKING_FAILED_ATTEMPTS = 'user_account_locking_failed_attempts';
    case USER_IP_LOCKING_MINUTES = 'user_ip_locking_minutes';
    case USER_IP_LOCKING_FAILED_ATTEMPTS = 'user_ip_locking_failed_attempts';
    case USER_INVITATION_EXPIRY_MINUTES = 'user_invitation_expiry_minutes';

    public function getValue(): float | string | array
    {
        return match($this) {
            // JWT
            self::JWT_TOKEN_TTL_MINUTES => 86400, // 60 days

            // OTP
            self::OTP_RENEW_MINUTES => 0.5,
            self::OTP_LOCKING_MINUTES => 5,
            self::OTP_EXPIRY_MINUTES => 10,
            self::OTP_LOCKING_FAILED_ATTEMPTS => 5,
            self::OTP_DEFAULT_RANGE => [1000,9999],

            // USER
            self::USER_ACCOUNT_LOCKING_MINUTES => 10,
            self::USER_ACCOUNT_LOCKING_FAILED_ATTEMPTS => 3,
            self::USER_IP_LOCKING_MINUTES => 30,
            self::USER_IP_LOCKING_FAILED_ATTEMPTS => 5,
            self::USER_INVITATION_EXPIRY_MINUTES => 10080, // 7 days
        };
    }

    public static function list()
    {
        return [
            // TOKEN 
            'token' => [
                ApiSettings::JWT_TOKEN_TTL_MINUTES->value => ApiSettings::JWT_TOKEN_TTL_MINUTES->getValue(),
            ],


            // USER
            'user' => [
                ApiSettings::USER_ACCOUNT_LOCKING_FAILED_ATTEMPTS->value => ApiSettings::USER_ACCOUNT_LOCKING_FAILED_ATTEMPTS->getValue(),
                ApiSettings::USER_IP_LOCKING_FAILED_ATTEMPTS->value => ApiSettings::USER_IP_LOCKING_FAILED_ATTEMPTS->getValue(),
                ApiSettings::USER_ACCOUNT_LOCKING_MINUTES->value => ApiSettings::USER_ACCOUNT_LOCKING_MINUTES->getValue(),
                ApiSettings::USER_IP_LOCKING_MINUTES->value => ApiSettings::USER_IP_LOCKING_MINUTES->getValue(),
            ],

            // OTP
            'otp' => [
                ApiSettings::OTP_RENEW_MINUTES->value => ApiSettings::OTP_RENEW_MINUTES->getValue(),
                ApiSettings::OTP_LOCKING_MINUTES->value => ApiSettings::OTP_LOCKING_MINUTES->getValue(),
                ApiSettings::OTP_EXPIRY_MINUTES->value => ApiSettings::OTP_EXPIRY_MINUTES->getValue(),
                ApiSettings::OTP_LOCKING_FAILED_ATTEMPTS->value => ApiSettings::OTP_LOCKING_FAILED_ATTEMPTS->getValue(),
            ]
        ];
    }
}
