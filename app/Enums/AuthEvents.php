<?php

namespace App\Enums;

use Illuminate\Support\Facades\Lang;

enum AuthEvents: string
{
    case REGISTER = "register";
    case LOGIN = "login";
    case LOGOUT = "logout";
    case PASSWORD_SET = "password_set";
    case PASSWORD_RESET = "password_reset";
    case PASSWORD_FORGOT = "password_forgot";
    case PASSWORD_CHANGE = "password_change";
    case OTP_REQUEST = "otp_request";
    case OTP_VALIDATE = "otp_validate";
    case OTP_RENEW = "otp_renew";

    public function description(?string $lang = null): string
    {
        $lang = $lang ?: app()->getLocale(); 
        return Lang::get("auth.events." . $this->value, [], $lang);
    }
}