<?php

namespace App\Enums;

use App\Traits\EnumLabels;

enum SubscriptionLicenses: string
{
    use EnumLabels;

    // ENVIRONMENT STATUS
    case ENVIRONMENT_STATUS_ACTIVE = 'active';
    case ENVIRONMENT_STATUS_PENDING = 'pending';
    case ENVIRONMENT_STATUS_FAILED = 'failed';

    /**
     * Get the label for the enum case.
     *
     * @return string
     */
    public function label(): string
    {
        return match ($this) {

            // ENVIRONMENT STATUS
            self::ENVIRONMENT_STATUS_ACTIVE => 'Active',
            self::ENVIRONMENT_STATUS_PENDING => 'Pending',
            self::ENVIRONMENT_STATUS_FAILED => 'Failed',

            default => $this->value,
        };
    }
}
