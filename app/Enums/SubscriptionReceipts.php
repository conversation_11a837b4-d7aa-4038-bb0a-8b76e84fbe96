<?php

namespace App\Enums;

use App\Traits\EnumLabels;

enum SubscriptionReceipts: string
{
    use EnumLabels;

    // STATUSES
    CASE RECEIPT_STATUS_PENDING = 'pending';
    CASE RECEIPT_STATUS_REJECTED = 'rejected';
    CASE RECEIPT_STATUS_APPROVED = 'approved';

    /**
     * Get the label for the enum case.
     *
     * @return string
     */
    public function label(): string
    {
        return match ($this) {
            self::RECEIPT_STATUS_PENDING => 'Pending',
            self::RECEIPT_STATUS_REJECTED => 'Rejected',
            self::RECEIPT_STATUS_APPROVED => 'Approved',
        };
    }
}
