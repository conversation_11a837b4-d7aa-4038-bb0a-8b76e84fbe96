<?php

namespace App\Enums;

use Illuminate\Support\Facades\Lang;

enum SubscriptionMessages: string
{
    case SUBSCRIPTION_FAILURE = "failure";

    case SUBSCRIPTION_TRIAL_EXIST = "trial_exist";

    case SUBSCRIPTION_LINE_OF_CODE_BUNDLE_REQUIRED = "loc_required";

    case SUBSCRIPTION_INVALID_RECEIPT = "invalid_receipt";

    case SUBSCRIPTION_INVALID_SERVER_ID_FORMAT = "invalid_server_id_format";

    case DUPLICATED_SUBSCRIPTIONS = "duplicated_subscriptions";

    public function description(?string $lang = null): string
    {
        $lang = $lang ?: app()->getLocale(); 
        return Lang::get("subscription_messages." . $this->value, [], $lang);
    }

    public static function fromValue(string $value): ?self
    {
        return self::tryFrom($value);
    }
}