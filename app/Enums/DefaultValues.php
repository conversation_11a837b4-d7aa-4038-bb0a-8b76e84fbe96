<?php

namespace App\Enums;

enum DefaultValues: string
{
    // GCS TEMPORARY LINKS
    case GCS_TEMPORARY_LINK_EXPIRATION_DAYS = 'gcs_temporary_link_expiration_day';

    // DATE TIMES
    case DATE_TIME = 'date_time';
    case DATE_TIME_FORMAT = 'date_time_format';
    case DATE_FORMAT = 'date_format';
    
    // CONSTANTS
    case STRING = 'string';
    case FOREING_KEY = 'foreing_key';
    case ARRAY = 'array';
    case SYSTEM_USER_ID = 'system_user_id';
    case TIMEZONE = 'timezone';

    // CURRENCY
    case CURRENCY = 'currency';
    case CURRENCY_SYMBOL = 'currency_symbol';

    // SUBSCRIPTIONS
    case SUBSCRIPTION_TRIAL_PERIOD_DAYS = 'trial_period_days';
    case SUBSCRIPTION_LIFETIME_YEARS = 'subscription_lifetime_years';

    // VAT
    case VAT = 'vat';

    // PREFIX
    case CUSTOMER_PREFIX = 'customer_prefix';
    case CUSTOMER_USER_PREFIX = 'customer_user_prefix';

    case SUBSCRIPTION_PREFIX = 'subscription_prefix';
    case SUBSCRIPTION_HISTORY_PREFIX = 'subscription_history_prefix';
    case SUBSCRIPTION_LICENSE_HISTORY_PREFIX = 'subscription_license_history_prefix';

    case ACTIVITY_PREFIX = 'activity_prefix';
    case LOG_PREFIX = 'log_prefix';

    public function get(): float | string | array | null
    {
        return match($this) {
            // GCS TEMPORARY LINKS
            self::GCS_TEMPORARY_LINK_EXPIRATION_DAYS => 1, 

            // DATE TIMES
            self::DATE_TIME => null,
            self::DATE_TIME_FORMAT => 'd M, Y - h:i A',
            self::DATE_FORMAT => 'd M, Y',

            // CONSTANTS
            self::STRING => '',
            self::FOREING_KEY => 1,
            self::ARRAY => [],
            self::SYSTEM_USER_ID => 1,
            self::TIMEZONE => 'UTC',

            // CURRENCY
            self::CURRENCY => 'USD',
            self::CURRENCY_SYMBOL => '$',

            // SUBSCRIPTIONS
            self::SUBSCRIPTION_TRIAL_PERIOD_DAYS => 14,
            self::SUBSCRIPTION_LIFETIME_YEARS => 1,

            // VAT
            self::VAT => 0.00,

            // PREFIX
            self::CUSTOMER_PREFIX => 'CUST_',
            self::CUSTOMER_USER_PREFIX => 'CUSTU_',

            self::SUBSCRIPTION_PREFIX => 'CUSTS_',
            self::SUBSCRIPTION_HISTORY_PREFIX => 'CUSTSH_',
            self::SUBSCRIPTION_LICENSE_HISTORY_PREFIX => 'ACTIONH_',

            self::ACTIVITY_PREFIX => 'ACTIV_',
            self::LOG_PREFIX => 'LOG_',
        };
    }

    public function name(): string
    {
        return match($this) {
            self::SYSTEM_USER_ID => 'System user',
        };
    }
}
