<?php

namespace App\Observers;

use App\Enums\Subscriptions;
use App\Models\SubscriptionLicense;
use App\Models\SubscriptionLicenseHistory;
use App\Traits\Blamable;

class SubscriptionLicenseObserver
{
    use Blamable;

    public static bool $skipObserver = false;
    public static ?string $eventType = null;
    public static ?string $changeReason = null;

    /**
     * Handle the SubscriptionLicense "created" event.
     */
    public function created(SubscriptionLicense $license): void
    {
        if (self::$skipObserver) {
            return;
        }

        $modelData = $this->mapLicenseToHistoryData($license);
        $history = new SubscriptionLicenseHistory($modelData);

        // Check for specific changes
        if ($license->isDirty('server_id')) {
            if ($license->getOriginal('server_id')) {
            $history->history_event = Subscriptions::LICENSE_HISTORY_SERVER_ID_MODIFIED;
            } else {
            $history->history_event = Subscriptions::LICENSE_HISTORY_SERVER_ID_ADDED;
            }
        } elseif ($license->isDirty('license_key')) {
            $history->history_event = Subscriptions::LICENSE_HISTORY_LICENSE_KEY_PROVIDED;
        } else {
            $history->history_event = self::$eventType ?? Subscriptions::LICENSE_HISTORY_CREATED;
        }

        $history->change_reason = self::$changeReason;
        $history->save();
    }

    /**
     * Handle the SubscriptionLicense "updated" event.
     */
    public function updated(SubscriptionLicense $license): void
    {
        if (self::$skipObserver) {
            return;
        }

        if (!$license->isDirty()) {
            return;
        }

        $modelData = $this->mapLicenseToHistoryData($license);
        $history = new SubscriptionLicenseHistory($modelData);

        // Check for specific changes
        if ($license->isDirty('server_id')) {
            if ($license->getOriginal('server_id')) {
            $history->history_event = Subscriptions::LICENSE_HISTORY_SERVER_ID_MODIFIED;
            } else {
            $history->history_event = Subscriptions::LICENSE_HISTORY_SERVER_ID_ADDED;
            }
        } elseif ($license->isDirty('license_key')) {
            $history->history_event = Subscriptions::LICENSE_HISTORY_LICENSE_KEY_PROVIDED;
        } else {
            $history->history_event = self::$eventType ?? Subscriptions::LICENSE_HISTORY_UPDATED;
        }

        $history->change_reason = self::$changeReason;
        $history->save();
    }

    /**
     * Handle the SubscriptionLicense "deleted" event.
     */
    public function deleted(SubscriptionLicense $license): void
    {
        if (self::$skipObserver) {
            return;
        }

        $modelData = $this->mapLicenseToHistoryData($license);
        $history = new SubscriptionLicenseHistory($modelData);
        $history->history_event = self::$eventType ?? Subscriptions::LICENSE_HISTORY_REVOKED;
        $history->change_reason = self::$changeReason;
        $history->deleted_at = now();
        $history->deleted_by = self::getBlamableId();
        $history->save();
    }

    /**
     * Handle the SubscriptionLicense "restored" event.
     */
    public function restored(SubscriptionLicense $license): void
    {
        if (self::$skipObserver) {
            return;
        }

        $modelData = $this->mapLicenseToHistoryData($license);
        $history = new SubscriptionLicenseHistory($modelData);
        $history->history_event = self::$eventType ?? Subscriptions::LICENSE_HISTORY_RENEWED;
        $history->change_reason = self::$changeReason;
        $history->save();
    }

    /**
     * Handle the SubscriptionLicense "force deleted" event.
     */
    public function forceDeleted(SubscriptionLicense $license): void
    {
        if (self::$skipObserver) {
            return;
        }

        $modelData = $this->mapLicenseToHistoryData($license);
        $history = new SubscriptionLicenseHistory($modelData);
        $history->history_event = self::$eventType ?? Subscriptions::LICENSE_HISTORY_REVOKED;
        $history->change_reason = self::$changeReason;
        $history->save();
    }

    /**
     * Map license data to history data
     */
    private function mapLicenseToHistoryData(SubscriptionLicense $license): array
    {
        return [
            'subscription_license_id' => $license->subscription_license_id,
            'subscription_id' => $license->subscription_id,
            'company_id' => $license->company_id,
            'license_key' => $license->license_key,
            'server_id' => $license->server_id,
            'sonar_url' => $license->sonar_url,
            'sonar_username' => $license->sonar_username,
            'sonar_password' => $license->sonar_password,
            'sonar_api_token' => $license->sonar_api_token,
            'environment_status' => $license->environment_status,
            'license_type' => $license->license_type,
            'environment' => $license->environment,
            'request_date' => $license->request_date,
            'issue_date' => $license->issue_date,
            'first_use_date' => $license->first_use_date,
        ];
    }
}