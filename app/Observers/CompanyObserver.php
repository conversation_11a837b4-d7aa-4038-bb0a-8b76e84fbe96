<?php

namespace App\Observers;

use App\Models\Company;
use App\Models\CompanyHistory;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

class CompanyObserver
{
    /**
     * Handle the Company "created" event.
     */
    public function created(Company $company): void
    {
        $this->createHistoryRecord($company, 'created');
    }

    /**
     * Handle the Company "updated" event.
     */
    public function updated(Company $company): void
    {
        $this->createHistoryRecord($company, 'updated', $company->getChanges(), $company->getOriginal());
    }

    /**
     * Handle the Company "deleted" event.
     */
    public function deleted(Company $company): void
    {
        $this->createHistoryRecord($company, 'deleted');
    }

    /**
     * Handle the Company "restored" event.
     */
    public function restored(Company $company): void
    {
        $this->createHistoryRecord($company, 'restored');
    }

    /**
     * Create a history record for the company.
     */
    private function createHistoryRecord(Company $company, string $action, array $changedFields = [], array $oldValues = []): void
    {
        $this->createHistoryRecordWithType($company, $action, 'direct', $changedFields, $oldValues);
    }

    /**
     * Create a history record with specific change type.
     */
    private function createHistoryRecordWithType(Company $company, string $action, string $changeType, array $changedFields = [], array $oldValues = [], array $pivotData = []): void
    {
        // Get the user who performed the action
        $performedBy = null;
        if (Auth::check()) {
            $performedBy = Auth::id();
        } elseif ($company->created_by && $action === 'created') {
            // Check if the user exists before setting performed_by
            if (\App\Models\User::find($company->created_by)) {
                $performedBy = $company->created_by;
            }
        } elseif ($company->updated_by && $action === 'updated') {
            // Check if the user exists before setting performed_by
            if (\App\Models\User::find($company->updated_by)) {
                $performedBy = $company->updated_by;
            }
        } elseif ($company->deleted_by && $action === 'deleted') {
            // Check if the user exists before setting performed_by
            if (\App\Models\User::find($company->deleted_by)) {
                $performedBy = $company->deleted_by;
            }
        }

        // Prepare new values for changed fields
        $newValues = [];
        if (!empty($changedFields)) {
            foreach (array_keys($changedFields) as $field) {
                $newValues[$field] = $company->getAttribute($field);
            }
        }

        $historyData = [
            'company_id' => $company->company_id,
            'action' => $action,
            'change_type' => $changeType,
            'performed_by' => $performedBy,

            // Company data snapshot
            'public_company_id' => $company->public_company_id,
            'name' => $company->name,
            'status_id' => $company->status_id,
            'timezone' => $company->timezone,
            'website' => $company->website,
            'number_of_employees' => $company->number_of_employees,
            'registration_type' => $company->registration_type,
            'invitation_expires_at' => $company->invitation_expires_at,
            'last_activity_at' => $company->last_activity_at,

            // Original timestamps
            'original_created_at' => $company->created_at,
            'original_updated_at' => $company->updated_at,
            'original_deleted_at' => $company->deleted_at,
            'original_created_by' => $company->created_by,
            'original_updated_by' => $company->updated_by,
            'original_deleted_by' => $company->deleted_by,

            // Change tracking
            'changed_fields' => !empty($changedFields) ? array_keys($changedFields) : null,
            'old_values' => !empty($oldValues) ? $oldValues : null,
            'new_values' => !empty($newValues) ? $newValues : null,

            // Request context
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'request_data' => $this->getRequestContext(),
        ];

        // Add pivot data if provided
        if (!empty($pivotData)) {
            $historyData = array_merge($historyData, $pivotData);
        }

        CompanyHistory::create($historyData);
    }

    /**
     * Create a history record for pivot table changes.
     */
    public static function createPivotHistory(Company $company, string $pivotType, array $oldIds, array $newIds): void
    {
        $observer = new self();

        $pivotData = [];
        switch ($pivotType) {
            case 'countries':
                $pivotData = [
                    'old_country_ids' => $oldIds,
                    'new_country_ids' => $newIds,
                ];
                break;
            case 'sectors':
                $pivotData = [
                    'old_sector_ids' => $oldIds,
                    'new_sector_ids' => $newIds,
                ];
                break;
            case 'industries':
                $pivotData = [
                    'old_industry_ids' => $oldIds,
                    'new_industry_ids' => $newIds,
                ];
                break;
        }

        $observer->createHistoryRecordWithType($company, 'updated', $pivotType, [], [], $pivotData);
    }

    /**
     * Get relevant request context for logging.
     */
    private function getRequestContext(): ?array
    {
        if (app()->runningInConsole()) {
            return ['source' => 'console'];
        }

        $context = [];
        
        // Add route information if available
        if (Request::route()) {
            $context['route'] = Request::route()->getName();
            $context['method'] = Request::method();
        }

        // Add relevant request parameters (exclude sensitive data)
        $excludeKeys = ['password', 'password_confirmation', 'token', 'api_key'];
        $requestData = Request::except($excludeKeys);

        if (!empty($requestData)) {
            $context['request_params'] = $requestData;
        }

        return !empty($context) ? $context : null;
    }
}
