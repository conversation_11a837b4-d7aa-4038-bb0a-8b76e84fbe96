<?php

namespace App\Observers;

use App\Models\Subscription;
use App\Models\SubscriptionHistory;
use App\Traits\Blamable;

class SubscriptionObserver
{
    use Blamable;

    public static bool $skipObserver = false;

    public static ?string $eventType = null;
    
    public static string|array|null $eventDescription = null;
    public static ?string $rejectionReasonDescription = null;
    public static ?string $rejectionReason = null;

    /**
     * Handle the Subscription "created" event.
     */
    public function created(Subscription $subscription): void
    {
        if (self::$skipObserver) {
            return;
        }

        $modelData = SubscriptionHistory::mapParentColumns($subscription->toArray());

        $history = new SubscriptionHistory($modelData);
        $history->history_event = self::$eventType;
        $this->setDescriptionTranslations($history);
        $history->save();
    }

    /**
     * Handle the Subscription "updated" event.
     */
    public function updated(Subscription $subscription): void
    {
        if (self::$skipObserver) {
            return;
        }

        if (!$subscription->isDirty()) {
            return;
        }

        $modelData = SubscriptionHistory::mapParentColumns($subscription->toArray());

        $history = new SubscriptionHistory($modelData);
        $history->history_event = self::$eventType;
        $history->rejection_reason_description = self::$rejectionReasonDescription;
        $history->rejection_reason = self::$rejectionReason;
        $this->setDescriptionTranslations($history);
        $history->save();
    }

    /**
     * Handle the Subscription "deleted" event.
     */
    public function deleted(Subscription $subscription): void
    {
        if (self::$skipObserver) {
            return;
        }

        $modelData = SubscriptionHistory::mapParentColumns($subscription->toArray());

        $history = new SubscriptionHistory($modelData);
        $history->history_event = self::$eventType;
        $this->setDescriptionTranslations($history);
        $history->deleted_at = now();
        $history->deleted_by = self::getBlamableId();
        $history->save();
    }

    /**
     * Handle the Subscription "restored" event.
     */
    public function restored(Subscription $subscription): void
    {
        if (self::$skipObserver) {
            return;
        }

        $modelData = SubscriptionHistory::mapParentColumns($subscription->toArray());

        $history = new SubscriptionHistory($modelData);
        $history->history_event = self::$eventType;
        $this->setDescriptionTranslations($history);
        $history->save();
    }

    /**
     * Handle the Subscription "force deleted" event.
     */
    public function forceDeleted(Subscription $subscription): void
    {
        if (self::$skipObserver) {
            return;
        }

        $modelData = SubscriptionHistory::mapParentColumns($subscription->toArray());

        $history = new SubscriptionHistory($modelData);
        $history->history_event = self::$eventType;
        $this->setDescriptionTranslations($history);
        $history->save();
    }

    /**
     * Set description translations on the history model.
     */
    private function setDescriptionTranslations(SubscriptionHistory $history): void
    {
        $locale = app()->getLocale();
        $fallbackLocale = config('app.fallback_locale', 'en');

        // If description is an array, set translations for each locale
        if (is_array(self::$eventDescription)) {
            foreach (self::$eventDescription as $lang => $text) {
                $history->setTranslation('description', $lang, $text);
            }
            return;
        }

        // If description is a string, set it for current locale and fallback
        if (is_string(self::$eventDescription)) {
            // Set for current locale
            $history->setTranslation('description', $locale, self::$eventDescription);
            
            // Set for fallback locale if different
            if ($locale !== $fallbackLocale) {
                $history->setTranslation('description', $fallbackLocale, self::$eventDescription);
            }
        }
    }
}
